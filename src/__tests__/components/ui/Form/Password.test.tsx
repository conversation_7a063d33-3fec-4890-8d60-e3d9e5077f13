import PasswordInput from "@/components/ui/Form/Password";
import { render, screen, fireEvent } from "@testing-library/react";

describe("PasswordInput", () => {
  it("Renders PasswordInput component", () => {
    render(
      <PasswordInput
        label="Password"
        placeholder="Password"
        value=""
        onChange={() => {}}
      />,
    );
    expect(screen.getByText("Password")).toBeInTheDocument();
  });

  it("Calls onChange when input value changes", () => {
    const onChange = jest.fn();
    render(
      <PasswordInput
        label="Password"
        placeholder="Password"
        value=""
        onChange={onChange}
      />,
    );
    fireEvent.change(screen.getByPlaceholderText("Password"), {
      target: { value: "test" },
    });
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it("Toggles visibility when button is clicked", () => {
    render(
      <PasswordInput
        label="Password"
        placeholder="Password"
        value=""
        onChange={() => {}}
      />,
    );
    fireEvent.click(screen.getByRole("button"));
    expect(screen.getByTestId("visibility_off")).toBeInTheDocument();
  });

  it("Renders error message when provided", () => {
    render(
      <PasswordInput
        label="Password"
        placeholder="Password"
        value=""
        onChange={() => {}}
        error="Error message"
      />,
    );
    expect(screen.getByText("Error message")).toBeInTheDocument();
  });
});
