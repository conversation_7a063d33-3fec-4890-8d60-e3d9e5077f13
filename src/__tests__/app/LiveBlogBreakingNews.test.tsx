import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import LiveBlogBreakingNews from "@/components/LiveBlog/LiveBlogBreakingNews";
import { Media } from "@/sanity/queries/dailyStory";
import { LiveBlogArticle } from "@/sanity/queries/liveBlog";

interface VideoProps {
  src: string;
  controls?: boolean;
  loop?: boolean;
  role?: string;
  pauseOnClickOutside?: boolean;
}
jest.mock("@/components/ui/VideoPlayer", () => ({
  __esModule: true,
  default: (props: VideoProps) => <video {...props} />,
}));

const mockMedia: Media[] = [
  {
    url: "/test-image.jpg",
    resource_type: "image",
    type: "image",
    text: "<PERSON> meets with the prime minister of the grand duchy.Credit: <PERSON>/<PERSON>",
  },
  {
    url: "/test-video.mp4",
    resource_type: "video",
    type: "video",
    text: "Test video caption",
  },
];

const mockArticle: LiveBlogArticle = {
  _id: "test-id",
  title: "Pope Emeritus <PERSON> dies at age 95",
  description:
    "Pope <PERSON>, a leading theologian of the 20th century and the first pope to resign from office in nearly 600 years, has died at the age of 95, the Vatican announced.",
  slug: "pope-emeritus-benedict-xvi-dies-at-age-95",
  publishedDate: "2022-02-28T14:00:00Z",
  updatedAt: "2022-02-28T14:00:00Z",
  media: mockMedia,
  author: [],
  category: {
    id: "",
    title: "",
    slug: "",
    description: "",
  },
  liveUpdates: [],
  isBreakingNews: false,
  relatedArticles: [],
};

describe("LiveBlogBreakingNews Component", () => {
  it("renders the breaking news title", () => {
    render(<LiveBlogBreakingNews liveBlogArticle={mockArticle} />);
    expect(
      screen.getByText("Pope Emeritus Benedict XVI dies at age 95"),
    ).toBeInTheDocument();
  });

  it("renders the excerpt correctly", () => {
    render(<LiveBlogBreakingNews liveBlogArticle={mockArticle} />);
    expect(
      screen.getByText(
        "Pope Emeritus Benedict XVI, a leading theologian of the 20th century and the first pope to resign from office in nearly 600 years, has died at the age of 95, the Vatican announced.",
      ),
    ).toBeInTheDocument();
  });

  it("renders the 'Breaking News' badge", () => {
    render(
      <LiveBlogBreakingNews
        liveBlogArticle={{ ...mockArticle, isBreakingNews: true }}
      />,
    );
    expect(screen.getByText("BREAKING NEWS")).toBeInTheDocument();
  });

  it("renders the media caption correctly", () => {
    render(<LiveBlogBreakingNews liveBlogArticle={mockArticle} />);
    expect(
      screen.getByText(
        "Pope Francis meets with the prime minister of the grand duchy.Credit: Daniel Ibañez/EWTN News",
      ),
    ).toBeInTheDocument();
  });

  // it("renders at least one 'Keep me updated' button", () => {
  //   render(<LiveBlogBreakingNews />);
  //   expect(screen.getAllByText("Keep me updated").length).toBeGreaterThan(0);
  // });
});
