"use client";

import { useEffect, useState } from "react";

export default function useDisqusSso() {
  const [ssoData, setSsoData] = useState<{
    remoteAuthS3: string;
    apiKey: string;
  } | null>(null);

  useEffect(() => {
    const fetchSso = async () => {
      try {
        const res = await fetch("/api/disqus/sso");
        if (!res.ok) return;

        const data = await res.json();
        setSsoData(data);
      } catch (e) {
        console.error("Failed to fetch Disqus SSO token:", e);
      }
    };

    fetchSso();
  }, []);

  return ssoData;
}
