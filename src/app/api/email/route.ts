import { NextRequest, NextResponse } from "next/server";
import { sendEmail } from "@/lib/email";

export async function POST(request: NextRequest) {
  const { name, lastName, phoneNumber, email, message } = await request.json();

  const emailDestination = process.env.CONTACT_EMAIL_DESTINATION;
  if (!emailDestination) {
    return NextResponse.json(
      { error: "Email destination not configured" },
      { status: 500 },
    );
  }
  const html = `
    <div style="font-family: Arial, sans-serif; color: #333; padding: 20px;">
        <h2 style="color: #2c3e50;">📬 Contact Form Submission</h2>
        
        <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px; font-weight: bold;">First Name:</td>
            <td style="padding: 8px;">${name}</td>
        </tr>
        <tr style="background-color: #f9f9f9;">
            <td style="padding: 8px; font-weight: bold;">Last Name:</td>
            <td style="padding: 8px;">${lastName}</td>
        </tr>
        <tr>
            <td style="padding: 8px; font-weight: bold;">Phone Number:</td>
            <td style="padding: 8px;">${phoneNumber}</td>
        </tr>
        <tr style="background-color: #f9f9f9;">
            <td style="padding: 8px; font-weight: bold;">Email:</td>
            <td style="padding: 8px;">${email}</td>
        </tr>
        </table>

        <div style="margin-top: 20px;">
        <p style="font-weight: bold; margin-bottom: 8px;">Message:</p>
        <p style="padding: 10px; background-color: #f4f4f4; border-left: 4px solid #2c3e50;">${message}</p>
        </div>
    </div>
    `;
  try {
    const result = await sendEmail({
      from: `"EWTN News" <<EMAIL>>`,
      to: emailDestination,
      subject: `EWTN News Contact Form Submitted`,
      html,
    });
    return NextResponse.json({ success: true, result }, { status: 200 });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { success: false, error: "Failed to send email" },
      { status: 500 },
    );
  }
}
