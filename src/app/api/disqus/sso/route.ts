import { NextResponse } from "next/server";
import crypto from "crypto";
import { auth0 } from "@/lib/auth0";

export async function GET() {
  const session = await auth0.getSession();

  const baseUrl = process.env.NEXT_PUBLIC_URL;

  const ssoConfig = {
    name: "<PERSON><PERSON>",
    button: `${baseUrl}/favicon.ico`,
    icon: `${baseUrl}/favicon.ico`,
    url: `${baseUrl}/auth/login?returnTo=/auth/sso-callback`,
    logout: `${baseUrl}/auth/logout`,
    width: "800",
    height: "400",
  };

  if (!session || !session.user) {
    return NextResponse.json({
      remoteAuthS3: null,
      apiKey: process.env.NEXT_PUBLIC_DISQUS_API_KEY,
      sso: ssoConfig,
    });
  }

  const user = session.user;

  const remoteDomainSlug = process.env.NEXT_PUBLIC_DISQUS_REMOTE_DOMAIN;
  const payload = {
    id: `${remoteDomainSlug}-${user.sub || user.user_id || user.email}`,
    username: user.name || user.nickname || user.email,
    email: user.email,
    avatar: user.picture,
  };

  const message = Buffer.from(JSON.stringify(payload)).toString("base64");
  const timestamp = Math.floor(Date.now() / 1000).toString();

  const secretKey = process.env.DISQUS_SSO_SECRET!;
  const sig = crypto
    .createHmac("sha1", secretKey)
    .update(`${message} ${timestamp}`)
    .digest("hex");

  const remoteAuthS3 = `${message} ${sig} ${timestamp}`;

  return NextResponse.json({
    remoteAuthS3,
    apiKey: process.env.NEXT_PUBLIC_DISQUS_API_KEY,
    sso: ssoConfig,
  });
}
