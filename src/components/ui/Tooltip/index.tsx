import { useEffect, useRef } from "react";

interface TooltipProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  buttonRef: React.RefObject<HTMLButtonElement>;
}

export default function Tooltip({
  isOpen,
  onClose,
  children,
  buttonRef,
}: TooltipProps) {
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        buttonRef.current !== event.target
      ) {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen, onClose, buttonRef]);

  return (
    <>
      <div
        className="
          absolute top-full left-1/2 transform -translate-x-1/2
          border-b-[0.5rem] border-transparent border-l-[0.5rem] border-r-[0.5rem] border-b-grey-100
        "
      ></div>
      <div
        ref={tooltipRef}
        data-testid="tooltip"
        className={`absolute top-full right-0 transform mt-2
            w-[230px] bg-grey-100 rounded-[16px] shadow-lg 
            flex flex-col items-start p-4 gap-4 isolation-isolate z-50 ${
              isOpen ? "opacity-100 visible" : "opacity-0 invisible"
            }`}
      >
        {children}
      </div>
    </>
  );
}
