import { useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";

interface AuthorsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  authors: {
    name: string;
    slug: string;
    image?: {
      url: string;
    };
  }[];
  buttonRef: React.RefObject<HTMLButtonElement>;
  showImages?: boolean;
}

export default function AuthorsDialog({
  isOpen,
  onClose,
  authors,
  buttonRef,
  showImages = false,
}: AuthorsDialogProps) {
  const dialogRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dialogRef.current &&
        !dialogRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen, onClose, buttonRef]);

  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-1/2 -translate-x-1/2 mt-2 z-50">
      <div className="absolute -top-2 left-1/2 -translate-x-1/2 z-50">
        <div className="w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-grey-100" />
      </div>
      <div
        ref={dialogRef}
        className="w-[214px] bg-white border border-grey-200 rounded-lg shadow-lg p-4 z-40"
      >
        <h4 className="text-xs mb-2">Authors</h4>
        <div className="flex flex-col gap-2">
          {authors.map((author, index) => (
            <div className="flex items-center gap-2" key={index}>
              {showImages && author?.image && (
                <div className="w-[38px] h-[38px] flex-shrink-0 relative">
                  <Link
                    role="link"
                    href={`/author/${author?.slug}`}
                    className="transition-opacity hover:opacity-85 block w-full h-full"
                  >
                    <Image
                      src={author.image?.url}
                      alt={author?.name}
                      fill
                      className="rounded-full mr-3"
                    />
                  </Link>
                </div>
              )}

              <div>
                <Link
                  href={`/author/${author?.slug}`}
                  className="text-sm text-gray-800"
                >
                  <span className="hover-underline-animation inline-block">
                    {author?.name}
                  </span>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
