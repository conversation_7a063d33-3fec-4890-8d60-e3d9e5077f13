import { sanityFetch } from "@/sanity/live";
import { moreArticlesQuery } from "@/sanity/queries/Category/category";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  {
    params,
  }: { params: Promise<{ categoryId: string; subcategoryId?: string | null }> },
) {
  try {
    const categoryId = (await params).categoryId;

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");
    const subcategoryId = url.searchParams.get("subcategoryId") || null;

    const ignoreArticlesIdsParam = url.searchParams.getAll(
      "ignoreArticlesIds[]",
    );
    const ignoreArticlesIds =
      ignoreArticlesIdsParam.length > 0 ? ignoreArticlesIdsParam : null;

    const validPage = isNaN(page) ? 1 : page;
    const validPageSize = isNaN(pageSize) ? 10 : pageSize;

    const start = (validPage - 1) * validPageSize;
    const end = start + validPageSize;

    const response = await sanityFetch({
      query: moreArticlesQuery,
      params: {
        categoryId: categoryId,
        subcategoryId: subcategoryId == "null" ? null : subcategoryId,
        ignoreArticlesIds: ignoreArticlesIds,
        start,
        end,
      },
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching articles:", error);

    return NextResponse.json(
      {
        error: "Error fetching articles",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
