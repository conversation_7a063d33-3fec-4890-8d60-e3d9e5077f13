import { WhatsappShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const WhatsappButton = ({ shareUrl }: { shareUrl: string }) => {
  return (
    <WhatsappShareButton
      url={shareUrl}
      title="Check this out"
      separator=":: "
      aria-label="Share on Whatsapp"
    >
      <ShareIconButton
        icon={(hover) => (
          <Icon icon="whatsapp" size={24} color={hover ? "#24d366" : "black"} />
        )}
      />
    </WhatsappShareButton>
  );
};

export default WhatsappButton;
