"use client";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";

type Props = {
  praying?: boolean;
  size?: number;
  color?: string;
};

export default function PrayingHands({
  praying = false,
  size = 24,
  color = "black",
}: Props) {
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const hasMounted = useRef(false);

  useEffect(() => {
    if (hasMounted.current && praying) {
      setShouldAnimate(true);
    }
    hasMounted.current = true;
  }, [praying]);

  return (
    <div
      className="relative"
      style={{
        width: size * 1.25,
        height: size * 2,
      }}
    >
      <motion.div
        className="absolute top-0 left-0 origin-bottom-right"
        animate={shouldAnimate ? { rotate: [0, 30, 0] } : { rotate: 0 }}
        transition={
          shouldAnimate
            ? {
                duration: 1,
                times: [0, 0.5, 1],
                ease: "easeInOut",
              }
            : { duration: 0 }
        }
        style={{
          width: size,
          height: size * 1.8,
          x: size / 2,
        }}
        onAnimationComplete={() => setShouldAnimate(false)}
      >
        <LeftHand color={color} />
      </motion.div>

      <motion.div
        className="absolute top-0 right-0 origin-bottom-left"
        animate={shouldAnimate ? { rotate: [0, -30, 0] } : { rotate: 0 }}
        transition={
          shouldAnimate
            ? {
                duration: 1,
                times: [0, 0.5, 1],
                ease: "easeInOut",
              }
            : { duration: 0 }
        }
        style={{
          width: size,
          height: size * 1.8,
          x: -size / 2,
        }}
        onAnimationComplete={() => setShouldAnimate(false)}
      >
        <RightHand color={color} />
      </motion.div>
    </div>
  );
}

function LeftHand({ color }: { color: string }) {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 12 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ transform: "scaleX(-1)" }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.9553 0.614843C10.4904 0.295102 10.2605 0.105308 9.93143 0.0393521C9.60236 -0.0266036 9.26228 -0.00944934 8.94133 0.0892952C8.62037 0.18804 8.32845 0.365326 8.09142 0.605451C7.85438 0.845576 7.67955 1.14113 7.58241 1.46592L4.16444 12.8733L0.767097 16.3098C0.486018 16.5943 0.328125 16.98 0.328125 17.3823C0.328125 17.7845 0.486018 18.1702 0.767097 18.4547L3.83165 21.5546C3.97093 21.6955 4.1363 21.8073 4.3183 21.8835C4.5003 21.9598 4.69538 21.9991 4.89238 21.9991C5.08938 21.9991 5.28445 21.9598 5.46646 21.8835C5.64846 21.8073 5.81383 21.6955 5.95311 21.5546L10.4792 16.9736C10.6046 16.8463 11.1476 16.2379 11.2544 16.0943C11.5716 15.6774 11.448 0.965433 10.9553 0.614843ZM9.90955 15.1673C10.0222 14.8906 10.0794 14.594 10.0779 14.2948V2.06521C10.0812 1.93147 10.036 1.80114 9.95081 1.69878C9.86565 1.59643 9.74641 1.52911 9.61558 1.50952C9.48474 1.48993 9.35134 1.51943 9.24052 1.59244C9.12969 1.66545 9.04909 1.77695 9.01391 1.90591L5.54532 13.4954C5.50956 13.615 5.44515 13.7238 5.35783 13.8121L4.0782 15.1074L7.14181 18.2063L9.42171 15.9068C9.63109 15.6954 9.7969 15.444 9.90955 15.1673Z"
        fill={color}
      />
    </svg>
  );
}

function RightHand({ color }: { color: string }) {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 12 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.9553 0.614843C10.4904 0.295102 10.2605 0.105308 9.93143 0.0393521C9.60236 -0.0266036 9.26228 -0.00944934 8.94133 0.0892952C8.62037 0.18804 8.32845 0.365326 8.09142 0.605451C7.85438 0.845576 7.67955 1.14113 7.58241 1.46592L4.16444 12.8733L0.767097 16.3098C0.486018 16.5943 0.328125 16.98 0.328125 17.3823C0.328125 17.7845 0.486018 18.1702 0.767097 18.4547L3.83165 21.5546C3.97093 21.6955 4.1363 21.8073 4.3183 21.8835C4.5003 21.9598 4.69538 21.9991 4.89238 21.9991C5.08938 21.9991 5.28445 21.9598 5.46646 21.8835C5.64846 21.8073 5.81383 21.6955 5.95311 21.5546L10.4792 16.9736C10.6046 16.8463 11.1476 16.2379 11.2544 16.0943C11.5716 15.6774 11.448 0.965433 10.9553 0.614843ZM9.90955 15.1673C10.0222 14.8906 10.0794 14.594 10.0779 14.2948V2.06521C10.0812 1.93147 10.036 1.80114 9.95081 1.69878C9.86565 1.59643 9.74641 1.52911 9.61558 1.50952C9.48474 1.48993 9.35134 1.51943 9.24052 1.59244C9.12969 1.66545 9.04909 1.77695 9.01391 1.90591L5.54532 13.4954C5.50956 13.615 5.44515 13.7238 5.35783 13.8121L4.0782 15.1074L7.14181 18.2063L9.42171 15.9068C9.63109 15.6954 9.7969 15.444 9.90955 15.1673Z"
        fill={color}
      />
    </svg>
  );
}
