import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import Link from "next/link";
import Button from "../ui/Button";
import { useRef, useState } from "react";
import { HomeArticle } from "@/sanity/queries/HomePage/homePage";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";
import AuthorsList from "../Author/AuthorsList";

export function ArticleCard({ article }: { article: HomeArticle }) {
  return (
    <div className="pr-4 mr-4 border-r border-grey-200 md:border-0 md:mr-0 md:px-4">
      <h3 className="text-[12px] uppercase text-gray-500 mb-2">
        <Link
          href={`/${article.category.slug}`}
          className="hover-underline-animation"
        >
          {article.category.title}
        </Link>
      </h3>
      <div className="flex gap-3">
        <div className="flex-1">
          <h2 className="text-[18px] lg:text-[20px] font-semibold font-titles pt-1">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="hover-underline-animation"
            >
              {truncateTitle(article.title)}
            </Link>
          </h2>
        </div>
        {article.media && (
          <div className="relative w-[85px] h-[85px] rounded-2xl overflow-hidden">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="hover:opacity-85 transition-all"
            >
              <Image
                src={getMediaImage(article.media[0])}
                alt={article.title}
                fill
                style={{ objectFit: "cover" }}
                className="rounded-2xl"
              />
            </Link>
          </div>
        )}
      </div>
      <p className="text-[15px] text-gray-500 pt-4 leading-6">
        {truncateDescription(article.description)}
      </p>
    </div>
  );
}

export function TertiaryNews({ articles }: { articles: HomeArticle[] }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<{
    slidePrev: () => void;
    slideNext: () => void;
  } | null>(null);

  return (
    <section className="pt-4 pb-8 lg:pb-0 md:border-grey-200 sm:-mr-40 md:sm:-mr-0">
      <div className="md:hidden">
        <Swiper
          modules={[Navigation]}
          navigation
          loop={true}
          slidesPerView={"auto"}
          centeredSlides={false}
          className="relative news-swiper"
        >
          {articles.map((article, index) => (
            <SwiperSlide key={index} className="!w-[90%] sm:!w-[40.6%]">
              <ArticleCard article={article} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      <div className="hidden md:block">
        <div className="md:-mx-4">
          <Swiper
            modules={[Pagination, Autoplay]}
            spaceBetween={10}
            slidesPerView={1.2}
            loop={true}
            breakpoints={{
              650: { slidesPerView: 3, spaceBetween: 0 },
              1024: { slidesPerView: 2, spaceBetween: 0 },
              990: { slidesPerView: 2, spaceBetween: 0 },
            }}
            onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
            onSwiper={(swiper) => (swiperRef.current = swiper)}
          >
            {articles.map((article, index) => (
              <SwiperSlide
                key={index}
                className={`${index == activeIndex ? "md:border-r border-grey-200" : ""}`}
              >
                <ArticleCard article={article} />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {articles.length > 2 && (
          <div className="flex mt-4 space-x-2 text-sm lg:items-center lg:justify-center ">
            <div>
              <Button
                onClick={() => swiperRef.current?.slidePrev()}
                iconName="arrow_back"
                variant="secondary"
                className="!rounded-full"
                size="xs"
                as="icon"
              />
            </div>
            <div>
              <Button
                onClick={() => swiperRef.current?.slideNext()}
                iconName="arrow_forward"
                variant="secondary"
                className="!rounded-full"
                size="xs"
                as="icon"
              />
            </div>
          </div>
        )}
      </div>
    </section>
  );
}

export default function MainSection({
  mainArticles,
}: {
  mainArticles: {
    main: HomeArticle;
    secondary: HomeArticle[];
    tertiary: HomeArticle[];
  };
}) {
  const mainArticle = mainArticles.main;
  const secondaryArticles = mainArticles.secondary;
  const tertiaryArticles = mainArticles.tertiary;

  return (
    <div className="">
      {/* Main News Section */}
      <section className="w-screen sm:w-full -ml-4 sm:ml-0">
        {mainArticle.media && (
          <div className="relative md:w-full aspect-[16/9] left-1/2 -translate-x-1/2 md:left-0 md:translate-x-0">
            <Link
              href={getArticleUrl(
                mainArticle.category.slug,
                mainArticle.slug,
                mainArticle.subcategory?.slug,
              )}
              className="hover:opacity-85 transition-all"
            >
              <Image
                src={getMediaImage(mainArticle.media[0])}
                alt={mainArticle.title}
                fill
                priority
                style={{ objectFit: "cover" }}
                className="w-full h-full sm:rounded-2xl"
              />
            </Link>
          </div>
        )}
        <div className="px-4 sm:px-0">
          <p className="text-[11px] md:text-[12px] text-grey-400 my-4 border-b border-grey-200 pb-3">
            {mainArticle.media && mainArticle.media[0]?.text}
          </p>
        </div>
      </section>

      <section className="pb-4 md:border-b md:border-grey-200">
        <h2 className="text-[13px] mb-2 uppercase">
          <Link
            href={`/${mainArticle?.category?.slug}`}
            className="hover-underline-animation"
          >
            {mainArticle?.category?.slug}
          </Link>
        </h2>

        <h1 className="font-semibold font-titles text-[32px] lg:text-[42px] leading-tight mb-5">
          <Link
            href={getArticleUrl(
              mainArticle?.category?.slug,
              mainArticle?.slug,
              mainArticle?.subcategory?.slug,
            )}
            className="hover-underline-animation"
          >
            {truncateTitle(mainArticle?.title)}
          </Link>
        </h1>

        <span>{truncateDescription(mainArticle?.description)}</span>

        {mainArticle?.authors && mainArticle?.authors.length > 0 && (
          <AuthorsList
            authors={mainArticle.authors}
            publishedDate={mainArticle._updatedAt}
          />
        )}
      </section>

      {/* Secondary News Section */}
      <section className="py-4 md:pb-0 sm:border-grey-200">
        <div className="space-y-4">
          {secondaryArticles.slice(0, 3).map((article, index) => (
            <div
              key={index}
              className="flex flex-col sm:flex-row md:flex-col lg:flex-row sm:items-center md:items-start lg:items-center gap-4 border-b border-grey-200 pb-4 md:last:border-0 md:last:pb-0 lg:last:border-b lg:last:pb-4"
            >
              {/* Image Section */}
              <div className="relative w-full sm:w-1/2 md:w-full lg:w-1/2 aspect-[16/9] rounded-2xl overflow-hidden">
                <Link
                  href={getArticleUrl(
                    article.category?.slug,
                    article.slug,
                    article.subcategory?.slug,
                  )}
                  className="hover:opacity-85 transition-all"
                >
                  {article.media && (
                    <Image
                      src={getMediaImage(article.media[0])}
                      alt={article.title}
                      fill
                      style={{ objectFit: "cover" }}
                      className="rounded-2xl"
                    />
                  )}
                </Link>
              </div>

              {/* Text Content */}
              <div className="flex-1">
                <h2 className="text-xs uppercase text-gray-500">
                  <Link
                    href={`/${article.category?.slug}`}
                    className="hover-underline-animation"
                  >
                    {article.category?.title}
                  </Link>
                </h2>
                <h3 className="text-2xl font-semibold font-titles pt-1">
                  <Link
                    href={getArticleUrl(
                      article.category?.slug,
                      article.slug,
                      article.subcategory?.slug,
                    )}
                    className="hover-underline-animation"
                  >
                    {truncateTitle(article.title)}
                  </Link>
                </h3>
                <div className="text-[12px] mt-4">
                  <AuthorsList
                    authors={article.authors}
                    publishedDate={article._updatedAt}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      <div className="md:hidden lg:block">
        <TertiaryNews articles={tertiaryArticles} />
      </div>
    </div>
  );
}
