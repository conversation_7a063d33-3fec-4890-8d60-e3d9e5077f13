import React from "react";
import { render, screen, act } from "@testing-library/react";
import VideoPlayer, { VideoPlayerProps } from "@/components/ui/VideoPlayer";

const mockPlay = jest.fn(() => Promise.resolve());
const mockPause = jest.fn();

const originalContains = Element.prototype.contains;

describe("VideoPlayer Component", () => {
  const defaultProps: VideoPlayerProps = {
    src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4",
  };

  beforeAll(() => {
    Object.defineProperty(HTMLMediaElement.prototype, "play", {
      writable: true,
      value: mockPlay,
    });

    Object.defineProperty(HTMLMediaElement.prototype, "pause", {
      writable: true,
      value: mockPause,
    });
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    Object.defineProperty(Element.prototype, "contains", {
      value: originalContains,
    });
  });

  test("renders the video player component", async () => {
    render(<VideoPlayer {...defaultProps} />);

    const videoElement = await screen.findByTestId("videoPlayer");
    expect(videoElement).toBeInTheDocument();
  });

  test("respects loop prop", async () => {
    render(<VideoPlayer {...defaultProps} loop={true} />);

    const videoElement = await screen.findByTestId("videoPlayer");
    expect(videoElement).toHaveAttribute("loop");
  });

  test("respects controls prop", async () => {
    render(<VideoPlayer {...defaultProps} controls={false} />);

    const videoElement = await screen.findByTestId("videoPlayer");
    expect(videoElement).not.toHaveAttribute("controls");
  });

  test("doesn't pause video when clicking outside if pauseOnClickOutside is false", async () => {
    render(
      <VideoPlayer
        {...defaultProps}
        pauseOnClickOutside={false}
        playing={true}
      />,
    );

    const event = new MouseEvent("mousedown", {
      bubbles: true,
      cancelable: true,
    });

    Element.prototype.contains = jest.fn(() => false);

    // Dispatch the event
    await act(async () => {
      document.dispatchEvent(event);
    });

    expect(mockPause).not.toHaveBeenCalled();
  });
});
