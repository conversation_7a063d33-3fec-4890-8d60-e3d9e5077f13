import { notFound } from "next/navigation";
import { mockVideoItems } from "../mocks";
import { TopicClient } from "./TopicClient";

type TopicProps = {
  params: Promise<{
    topic: string;
  }>;
};

// TODO: Add metadata of video page

export default async function TopicPage({ params }: TopicProps) {
  const { topic } = await params;

  if (!topic) {
    return notFound();
  }

  const videos = [
    ...[...mockVideoItems].sort(() => Math.random() - 0.5),
    ...[...mockVideoItems].sort(() => Math.random() - 0.5),
    ...[...mockVideoItems].sort(() => Math.random() - 0.5),
    ...[...mockVideoItems].sort(() => Math.random() - 0.5),
    ...[...mockVideoItems].sort(() => Math.random() - 0.5),
  ];

  // TODO: Add jsonLD
  // const jsonLd = buildVideoPageJsonLd();

  return (
    <>
      {/*
        // TODO: Add jsonLD
        <JsonLD jsonLd={jsonLd} />
      */}

      <TopicClient videos={videos} topic={topic} />
    </>
  );
}
