import AuthorsList from "@/components/Author/AuthorsList";
import Button from "@/components/ui/Button";
import { ArticleProps } from "@/types/interests";
import { getArticleUrl, getMediaImage, truncateTitle } from "@/utils/utils";
import Image from "next/image";
import Link from "next/link";

export const NewsCard: React.FC<ArticleProps> = ({ item, onRemoveAuthor }) => (
  <div
    className="flex gap-4 items-center group"
    data-testid="news-card-container"
  >
    <div className="flex gap-4 border-b pb-4 border-grey-200 group-last:border-b-0 flex-1">
      <div
        className="w-[128px] h-[85px] relative shrink-0"
        data-testid="news-image-container"
      >
        {item.media && (
          <Link
            href={getArticleUrl(
              item.category.slug,
              item.slug,
              item.subcategory?.slug,
            )}
            className="hover:opacity-85 transition-all"
          >
            <Image
              src={getMediaImage(item.media)}
              alt={item.title}
              className="rounded"
              layout="responsive"
              width={16}
              height={9}
            />
          </Link>
        )}
      </div>

      <div className="flex-1 flex flex-col justify-between">
        <div>
          <h3 className="font-titles text-[16px] lg:text-[19px] font-bold leading-6">
            <Link
              href={getArticleUrl(
                item.category.slug,
                item.slug,
                item.subcategory?.slug,
              )}
              className="hover-underline-animation"
            >
              {truncateTitle(item.title)}
            </Link>
          </h3>
        </div>
        {item.authors && (
          <AuthorsList
            authors={item.authors}
            publishedDate={item.updated_at}
          ></AuthorsList>
        )}
      </div>
    </div>
    {onRemoveAuthor && (
      <div className="pb-4" data-testid="news-remove-button-container">
        <Button
          as="icon"
          className="!rounded-xl"
          iconName="savedArticleFilled"
          onClick={() => onRemoveAuthor(item.id)}
          label="Remove Author"
        />
      </div>
    )}
  </div>
);

export default NewsCard;
