import TrendingNewsClient from "./TrendingNewsClient";
import { Metadata } from "next";
import { generatePageMetadata } from "@/utils/metadata";
import { fetchTrendingArticlesFromAnalytics } from "../api/trending";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata("/latest", "Latest News", "Latest News Page");
}

const TrendingNewsPage = async () => {
  const trendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 10,
    categorySlug: null,
    subcategorySlug: null,
    periodEnd: undefined,
    periodStart: undefined,
  });

  return <TrendingNewsClient trendingArticles={trendingArticles} />;
};

export default TrendingNewsPage;
