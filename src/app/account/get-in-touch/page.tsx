// GetInTouchPage.tsx (Server Component)
import GetInTouchClient from "@/app/account/get-in-touch/GetInTouchClient";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import { auth0 } from "@/lib/auth0";

export const metadata: Metadata = {
  title: "Account - Get in Touch",
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_URL}/account/get-in-touch`,
  },
  other: {
    "parsely-title": "Account - Get in Touch",
    "parsely-type": "index",
  },
};

export default async function GetInTouchPage() {
  const session = await auth0.getSession();

  if (!session?.user) {
    redirect("/auth/login?returnTo=/account");
  }

  return <GetInTouchClient />;
}
