# API Documentation

## Overview

The `/api` directory contains the backend endpoints for the CNA web application. The backend is built using Next.js API routes.

## Endpoints

### Articles

#### Saved articles

User must be authenticated to access these endpoints.
If the user is not authenticated, the server will return a 401 status code.

#### Parameters

- `article_id` (string): The ID of the article to be bookmarked or whose bookmark status is to be retrieved or deleted.

#### Example Requests and Responses

##### POST - `/articles/:article_id/bookmarks`

- **Description**: Save an article as a bookmark.
- **Request Parameters**:
  - `article_id` (string, required): The ID of the article to bookmark.
- **Example Request**:
  ```json
  {
    "article_id": "12345"
  }
  ```
- **Example Response**:
  ```json
  {
    "message": "Article bookmarked",
    "status": 201
  }
  ```

##### GET - `/articles/:article_id/bookmarks`

- **Description**: Retrieve bookmark information for a specific article.
- **Request Parameters**:
  - `article_id` (string, required): The ID of the article for which to retrieve bookmark information.
- **Example Request**:
  ```json
  {
    "article_id": "12345"
  }
  ```
- **Example Response**:
  ```json
  {
    "bookmarkCount": 42,
    "userBookmarked": true,
    "status": 200
  }
  ```

##### DELETE - `/articles/:article_id/bookmarks`

- **Description**: Remove a saved article from bookmarks.
- **Request Parameters**:
  - `article_id` (string, required): The ID of the article to remove from bookmarks.
- **Example Request**:
  ```json
  {
    "article_id": "12345"
  }
  ```
- **Example Response**:
  ```json
  {
    "message": "Article bookmark removed",
    "status": 200
  }
  ```

#### Liked articles

User must be authenticated to access these endpoints.
If the user is not authenticated, the server will return a 401 status code.

#### Parameters

- `article_id` (string): The ID of the article to be liked.

#### Example Requests and Responses

##### POST - `/articles/:article_id/likes`

- **Description**: Like an article.
- **Request Parameters**:
  - `article_id` (string, required): The ID of the article to like.
- **Example Request**:
  ```json
  {
    "article_id": "12345"
  }
  ```
- **Example Response**:
  ```json
  {
    "message": "Article liked",
    "status": 201
  }
  ```

##### GET - `/articles/:article_id/likes`

- **Description**: Retrieve likes information for a specific article.
- **Request Parameters**:
  - `article_id` (string, required): The ID of the article for which to retrieve likes information.
- **Example Request**:
  ```json
  {
    "article_id": "12345"
  }
  ```
- **Example Response**:
  ```json
  {
    "likeCount": 224,
    "userLiked": true,
    "status": 200
  }
  ```

##### DELETE - `/articles/:article_id/likes`

- **Description**: Remove a like from article.
- **Request Parameters**:
  - `article_id` (string, required): The ID of the article to remove the like.
- **Example Request**:
  ```json
  {
    "article_id": "12345"
  }
  ```
- **Example Response**:
  ```json
  {
    "message": "Article like removed",
    "status": 200
  }
  ```

#### Follow authors

User must be authenticated to access these endpoints.
If the user is not authenticated, the server will return a 401 status code.

#### Parameters

- `author_id` (string): The ID of the author to be followed.

#### Example Requests and Responses

##### POST - `/authors/:author_id/followers`

- **Description**: Follow an author.
- **Request Parameters**:
  - `author_id` (string, required): The ID of the author to follow.
- **Example Request**:
  ```json
  {
    "author_id": "author123"
  }
  ```
- **Example Response**:
  ```json
  {
    "message": "Author followed",
    "status": 201
  }
  ```

##### GET - `/authors/:author_id/followers`

- **Description**: Retrieve follow information for a specific author.
- **Request Parameters**:
  - `author_id` (string, required): The ID of the author for which to retrieve follow information.
- **Example Request**:
  ```json
  {
    "author_id": "author123"
  }
  ```
- **Example Response**:
  ```json
  {
    "followerCount": 12,
    "userFollows": true,
    "status": 200
  }
  ```

##### DELETE - `/authors/:author_id/followers`

- **Description**: Remove a follow from author.
- **Request Parameters**:
  - `author_id` (string, required): The ID of the author to remove the follow.
- **Example Request**:
  ```json
  {
    "article_id": "author123"
  }
  ```
- **Example Response**:
  ```json
  {
    "message": "Author follower removed",
    "status": 200
  }
  ```
