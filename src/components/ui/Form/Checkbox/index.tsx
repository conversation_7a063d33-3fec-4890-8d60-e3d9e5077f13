import { useId } from "react";

interface CheckboxProps {
  checked: boolean;
  error?: boolean | string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string | React.ReactNode;
}

const Checkbox = ({ label, error, onChange, checked }: CheckboxProps) => {
  const id = useId();
  const inputId = `input-${id}`;

  return (
    <div>
      <div
        className="flex flex-row items-center gap-x-2.5"
        onChange={onChange}
        aria-checked={checked}
        role="checkbox"
        aria-label="checkbox"
      >
        <input
          type="checkbox"
          id={inputId}
          className={`
                        relative peer shrink-0 appearance-none w-6 h-6 bg-white focus:outline-none border-2 
                        ${typeof error === "string" || error ? "border-red-700 bg-red-100" : "border-black"}
                    `}
        />
        <label htmlFor={inputId} className="text-[16px]">
          {label}
        </label>
        <svg
          className="absolute p-1 w-6 h-6 hidden peer-checked:block pointer-events-none"
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="9"
          viewBox="0 0 12 9"
          fill="none"
        >
          <path
            d="M3.72667 7.05333L0.946667 4.27333L0 5.21333L3.72667 8.94L11.7267 0.94L10.7867 0L3.72667 7.05333Z"
            fill="black"
          />
        </svg>
      </div>
      {typeof error === "string" && (
        <p className="mt-1 text-red-700 text-sm">{error}</p>
      )}
    </div>
  );
};

export default Checkbox;
