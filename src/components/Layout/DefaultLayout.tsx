import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  NavigationMenuQueryResult,
  SocialMediaQueryResult,
  ComplianceLinkQueryResult,
  DonationLinkQueryResult,
  EwtnNetworkLinksQueryResult,
} from "@/sanity/queries/layout";
import { AllShowsQueryResult } from "@/sanity/queries/Show/show";

interface DefaultLayoutProps {
  navigationMenu: NavigationMenuQueryResult;
  socialMediaItems: SocialMediaQueryResult;
  complianceLinkItems: ComplianceLinkQueryResult;
  donationLink: DonationLinkQueryResult;
  children: React.ReactNode;
  isHeaderReduced?: boolean;
  ewtnNetworkLinks: EwtnNetworkLinksQueryResult;
  allShows: AllShowsQueryResult;
}

export default function DefaultLayout({
  navigationMenu,
  socialMediaItems,
  complianceLinkItems,
  donationLink,
  children,
  isHeaderReduced = false,
  ewtnNetworkLinks,
  allShows,
}: DefaultLayoutProps) {
  return (
    <>
      <Header
        navigationMenu={navigationMenu}
        socialMediaItems={socialMediaItems}
        complianceLinkItems={complianceLinkItems}
        donationLink={donationLink}
        isHeaderReduced={isHeaderReduced}
        ewtnNetworkLinks={ewtnNetworkLinks}
        allShows={allShows}
      />
      <main>{children}</main>
      <Footer
        navigationMenu={navigationMenu}
        socialMediaItems={socialMediaItems}
        complianceLinkItems={complianceLinkItems}
        donationLink={donationLink}
        isFooterReduced={false}
      />
    </>
  );
}
