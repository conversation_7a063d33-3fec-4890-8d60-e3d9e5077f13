import { createClient, errorCodes } from "@/app/api/util/db/supabase";
import { workspace } from "@/sanity/env";

export async function addFollower(
  user_id: string,
  author_id: string,
): Promise<void> {
  const supabase = await createClient();

  const { count, error: countError } = await supabase
    .from("author_followers")
    .select("*", { count: "exact", head: true })
    .eq("agency", workspace)
    .eq("user_id", user_id);

  if (countError) {
    console.error("Count error", countError);
    throw new Error("Could not check follow limit");
  }

  if ((count ?? 0) >= 5) {
    throw new Error("Follow limit reached (max 5 authors)");
  }

  const { error, status } = await supabase
    .from("author_followers")
    .insert([{ user_id, author_id, agency: workspace }]);

  if (error) {
    console.log(error);
    if (status === errorCodes.ALREADY_EXIST_STATUS) {
      throw new Error("Already followed");
    }
    if (status === errorCodes.FORBIDDEN) {
      throw new Error("Forbidden");
    }
    throw new Error("Unexpected database error");
  }
}

export async function removeFollower(
  user_id: string,
  author_id: string,
): Promise<void> {
  const supabase = await createClient();
  const { error, status } = await supabase
    .from("author_followers")
    .delete()
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .eq("author_id", author_id);

  if (error) {
    if (status === errorCodes.NOT_FOUND) {
      throw new Error("Not found");
    }
    throw new Error("Unexpected database error");
  }
}

export async function getFollowerCount(
  user_id: string,
): Promise<{ follower_count: number }> {
  const supabase = await createClient();

  const { count, error } = await supabase
    .from("author_followers")
    .select("user_id", { count: "exact", head: true })
    .eq("agency", workspace)
    .eq("user_id", user_id);

  if (error) {
    throw new Error("Unexpected database error");
  }

  return { follower_count: count ?? 0 };
}

export async function getUserFollows(
  user_id: string,
  author_id: string,
): Promise<{ id: string; user_id: string; author_id: string }> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("author_followers")
    .select("*")
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .eq("author_id", author_id)
    .maybeSingle();

  if (error) {
    throw new Error("Unexpected database error");
  }

  return data;
}

export async function getUserFollowing(
  user_id: string,
): Promise<
  { id: string; user_id: string; author_id: string; created_at: Date }[]
> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("author_followers")
    .select("*")
    .eq("agency", workspace)
    .eq("user_id", user_id);

  if (error) {
    throw new Error("Unexpected database error");
  }

  return data;
}
