import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import Button, { ButtonProps } from "./index";

export default {
  title: "Components/Button",
  component: Button,
  argTypes: {
    onClick: { action: "clicked" },
  },
} as Meta;

const Template: StoryFn<ButtonProps> = (args) => <Button {...args} />;

export const Primary = Template.bind({});
Primary.args = {
  variant: "primary",
  state: "enabled",
  children: "Support EWTN",
  iconName: "donate",
};

export const Secondary = Template.bind({});
Secondary.args = {
  variant: "secondary",
  state: "enabled",
  children: "Listen (2 min)",
  iconName: "listen",
};

export const Outlined = Template.bind({});
Outlined.args = {
  variant: "outlined",
  state: "enabled",
  children: "Go Deeper",
};
