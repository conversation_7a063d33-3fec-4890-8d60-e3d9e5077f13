import { NextRequest } from "next/server";
import {
  addLike,
  getLikeCount,
  getUser<PERSON>ike,
  removeLike,
} from "@/app/api/services/articles/likes";
import {
  unauthorizedResponse,
  badRequestResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { getAuth0UserId, extractParam } from "@/app/api/util";

/**
 * Handles the POST request to like an article.
 *
 * @param params.articleId - The ID of the article to like.
 * @returns A JSON response containing a success or error message.
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const articleId = await extractParam(params, "articleId");
    if (!articleId) {
      return badRequestResponse("Missing articleId in request params");
    }

    await addLike(auth0UserId, articleId);
    return successResponse("Article liked", 201);
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the GET request to retrieve likes of specific article.
 *
 * @param params.articleId - The ID of the article for which to retrieve the likes.
 * @returns A JSON response containing the likes count and whether the user has liked the article.
 *
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    const articleId = await extractParam(params, "articleId");
    if (!articleId) {
      return badRequestResponse("Missing articleId in request params");
    }

    const [countData, userData] = await Promise.all([
      getLikeCount(articleId),
      auth0UserId ? getUserLike(auth0UserId, articleId) : null,
    ]);

    return successResponseWithData(
      {
        likeCount: countData?.like_count || 0,
        userLiked: !!userData,
      },
      200,
    );
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the DELETE request to remove the like from user.
 *
 * @param params.articleId - The ID of the article.
 * @returns A JSON response containing a success or error message.
 *
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const articleId = await extractParam(params, "articleId");
    if (!articleId) {
      return badRequestResponse("Missing articleId in request params");
    }

    await removeLike(auth0UserId, articleId);
    return successResponse("Article like removed", 200);
  } catch (error) {
    return errorResponse(error);
  }
}
