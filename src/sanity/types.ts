/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type ThisWeekInPhotosSection = {
  _type: "thisWeekInPhotosSection";
  title?: string;
  description?: string;
};

export type MoreNewsSection = {
  _type: "moreNewsSection";
  title?: string;
  description?: string;
};

export type TrendingArticlesSection = {
  _type: "trendingArticlesSection";
  title?: string;
};

export type HighlightedArticleSection = {
  _type: "highlightedArticleSection";
  article?:
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
      }
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "liveBlogArticle";
      };
  title?: string;
  description?: string;
  image?: CloudinaryAsset;
};

export type CategoryWithSubcategoriesSection = {
  _type: "categoryWithSubcategoriesSection";
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
};

export type CategorySection = {
  _type: "categorySection";
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
};

export type HomePage = {
  _id: string;
  _type: "homePage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  mainArticles?: {
    main?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
    };
    secondary?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
    }>;
    tertiary?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
    }>;
  };
  sections?: Array<
    | ({
        _key: string;
      } & TrendingArticlesSection)
    | ({
        _key: string;
      } & CategorySection)
    | ({
        _key: string;
      } & CategoryWithSubcategoriesSection)
    | ({
        _key: string;
      } & MoreNewsSection)
    | ({
        _key: string;
      } & HighlightedArticleSection)
    | ({
        _key: string;
      } & ThisWeekInPhotosSection)
  >;
};

export type BreakingNews = {
  _id: string;
  _type: "breakingNews";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  active?: boolean;
  title?: string;
  lastUpdate?: string;
  media?: CloudinaryAsset;
  relatedLiveBlogArticle?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "liveBlogArticle";
  };
};

export type LiveUpdate = {
  _type: "liveUpdate";
  timestamp?: string;
  title?: string;
  slug?: Slug;
  featuredMedia?: CloudinaryAsset;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  authors?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "author";
  }>;
  relatedArticle?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
  };
};

export type LiveBlogArticle = {
  _id: string;
  _type: "liveBlogArticle";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  featuredMedia?: Array<
    {
      _key: string;
    } & CloudinaryAsset
  >;
  liveUpdates?: Array<
    {
      _key: string;
    } & LiveUpdate
  >;
  isBreakingNews?: boolean;
  slug?: Slug;
  publishedDate?: string;
  socialMediaMetadata?: SocialMediaMetadata;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  subcategory?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "subcategory";
  };
  tags?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "tag";
  }>;
  countries?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "country";
  }>;
  authors?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "author";
  }>;
  relatedArticles?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
  }>;
  seoMetadata?: SeoMetadata;
};

export type DailyStoryArticle = {
  _id: string;
  _type: "dailyStoryArticle";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  body?: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?:
          | "normal"
          | "h1"
          | "h2"
          | "h3"
          | "h4"
          | "h5"
          | "h6"
          | "blockquote";
        listItem?: "bullet" | "number";
        markDefs?: Array<{
          href?: string;
          _type: "link";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }
    | ({
        _key: string;
      } & CloudinaryAsset)
    | {
        articles?: Array<
          | {
              _ref: string;
              _type: "reference";
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
            }
          | {
              _ref: string;
              _type: "reference";
              _weak?: boolean;
              [internalGroqTypeReferenceTo]?: "liveBlogArticle";
            }
        >;
        _type: "inlineRelatedArticles";
        _key: string;
      }
    | {
        content?: string;
        _type: "highlightedBlockquote";
        _key: string;
      }
    | {
        url?: string;
        _type: "twitterEmbed";
        _key: string;
      }
    | {
        url?: string;
        _type: "instagramEmbed";
        _key: string;
      }
    | {
        url?: string;
        _type: "facebookEmbed";
        _key: string;
      }
    | {
        url?: string;
        _type: "iframeEmbed";
        _key: string;
      }
  >;
  featuredMedia?: Array<
    {
      _key: string;
    } & CloudinaryAsset
  >;
  slug?: Slug;
  publishedDate?: string;
  socialMediaMetadata?: SocialMediaMetadata;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  subcategory?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "subcategory";
  };
  tags?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "tag";
  }>;
  isAnOpinionArticle?: boolean;
  countries?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "country";
  }>;
  authors?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "author";
  }>;
  relatedArticles?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
  }>;
  seoMetadata?: SeoMetadata;
};

export type Country = {
  _id: string;
  _type: "country";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  isoCode?: string;
};

export type SocialMediaMetadata = {
  _type: "socialMediaMetadata";
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: CloudinaryAsset;
  twitterTweetContent?: string;
  isTweeted?: boolean;
  facebookTitle?: string;
  facebookDescription?: string;
  facebookImage?: CloudinaryAsset;
};

export type Tag = {
  _id: string;
  _type: "tag";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  image?: CloudinaryAsset;
  seoMetadata?: SeoMetadata;
};

export type SeoMetadata = {
  _type: "seoMetadata";
  title?: string;
  description?: string;
};

export type InquiryTopic = {
  _id: string;
  _type: "inquiryTopic";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
};

export type ComplianceLink = {
  _id: string;
  _type: "complianceLink";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  isOnReducedFooter?: boolean;
};

export type Timeline = {
  _id: string;
  _type: "timeline";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  items?: Array<{
    title?: string;
    year?: number;
    highlighted?: boolean;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
};

export type StaticTabbedPage = {
  _id: string;
  _type: "staticTabbedPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote" | "dropCap";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  content?: {
    tabBody?: Array<{
      title?: string;
      image?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      };
      slug?: Slug;
      textBody?: Array<
        | {
            children?: Array<{
              marks?: Array<string>;
              text?: string;
              _type: "span";
              _key: string;
            }>;
            style?:
              | "normal"
              | "h1"
              | "h2"
              | "h3"
              | "h4"
              | "h5"
              | "h6"
              | "blockquote";
            listItem?: "bullet" | "number";
            markDefs?: Array<{
              href?: string;
              _type: "link";
              _key: string;
            }>;
            level?: number;
            _type: "block";
            _key: string;
          }
        | {
            image?: {
              asset?: {
                _ref: string;
                _type: "reference";
                _weak?: boolean;
                [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
              };
              hotspot?: SanityImageHotspot;
              crop?: SanityImageCrop;
              _type: "image";
            };
            description?: Array<{
              children?: Array<{
                marks?: Array<string>;
                text?: string;
                _type: "span";
                _key: string;
              }>;
              style?:
                | "normal"
                | "h1"
                | "h2"
                | "h3"
                | "h4"
                | "h5"
                | "h6"
                | "blockquote";
              listItem?: "bullet" | "number";
              markDefs?: Array<{
                href?: string;
                _type: "link";
                _key: string;
              }>;
              level?: number;
              _type: "block";
              _key: string;
            }>;
            _type: "grayArea";
            _key: string;
          }
        | {
            items?: Array<{
              title?: string;
              year?: number;
              highlighted?: boolean;
              image?: {
                asset?: {
                  _ref: string;
                  _type: "reference";
                  _weak?: boolean;
                  [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
                };
                hotspot?: SanityImageHotspot;
                crop?: SanityImageCrop;
                _type: "image";
              };
              _key: string;
            }>;
            _type: "timeline";
            _key: string;
          }
      >;
      _key: string;
    }>;
  };
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type StaticColumnPage = {
  _id: string;
  _type: "staticColumnPage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  layout?: "double" | "single";
  content?: {
    textBody?: Array<{
      textBody?: Array<{
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?:
          | "normal"
          | "h1"
          | "h2"
          | "h3"
          | "h4"
          | "h5"
          | "h6"
          | "blockquote";
        listItem?: "bullet" | "number";
        markDefs?: Array<{
          href?: string;
          _type: "link";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }>;
      _key: string;
    }>;
  };
};

export type SocialMedia = {
  _id: string;
  _type: "socialMedia";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  link?: string;
  icon?:
    | "x"
    | "facebook"
    | "instagram"
    | "youtube"
    | "linkedin"
    | "ios"
    | "android"
    | "googleNews";
};

export type MenuItem = {
  _type: "menuItem";
  title?: string;
  linkType?: "internal" | "external";
  internalLink?: Slug;
  externalLink?: string;
};

export type Subcategory = {
  _id: string;
  _type: "subcategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  pinnedArticles?: Array<
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
      }
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "liveBlogArticle";
      }
  >;
};

export type Category = {
  _id: string;
  _type: "category";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  slug?: Slug;
  subcategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "subcategory";
  }>;
  pinnedArticles?: Array<
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "dailyStoryArticle";
      }
    | {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "liveBlogArticle";
      }
  >;
};

export type SectionObject = {
  _type: "sectionObject";
  title?: string;
  slug?: Slug;
  menuItems?: Array<
    {
      _key: string;
    } & MenuItem
  >;
  isContentSectionOption?: boolean;
};

export type NavigationMenu = {
  _id: string;
  _type: "navigationMenu";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  sections?: Array<
    {
      _key: string;
    } & SectionObject
  >;
};

export type AuthorSocialMedia = {
  _type: "authorSocialMedia";
  icon?: "facebook" | "twitter" | "linkedin" | "instagram" | "youtube";
  link?: string;
  nickname?: string;
};

export type AboutAuthor = {
  _type: "aboutAuthor";
  title?: string;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  blockWithSocialLinks?: boolean;
};

export type Author = {
  _id: string;
  _type: "author";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  fullName?: string;
  jobTitle?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "jobTitle";
  };
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  image?: CloudinaryAsset;
  shortBio?: string;
  longBio?: string;
  slug?: Slug;
  email?: string;
  socialMedia?: Array<
    {
      _key: string;
    } & AuthorSocialMedia
  >;
  about?: Array<
    {
      _key: string;
    } & AboutAuthor
  >;
};

export type Slug = {
  _type: "slug";
  current?: string;
  source?: string;
};

export type Location = {
  _id: string;
  _type: "location";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
};

export type JobTitle = {
  _id: string;
  _type: "jobTitle";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
};

export type CloudinaryAssetContextCustom = {
  _type: "cloudinary.assetContextCustom";
  alt?: string;
  caption?: string;
};

export type CloudinaryAssetDerived = {
  _type: "cloudinary.assetDerived";
  raw_transformation?: string;
  url?: string;
  secure_url?: string;
};

export type CloudinaryAsset = {
  _type: "cloudinary.asset";
  public_id?: string;
  resource_type?: string;
  type?: string;
  format?: string;
  version?: number;
  url?: string;
  secure_url?: string;
  width?: number;
  height?: number;
  bytes?: number;
  duration?: number;
  tags?: Array<string>;
  created_at?: string;
  derived?: Array<
    {
      _key: string;
    } & CloudinaryAssetDerived
  >;
  access_mode?: string;
  context?: CloudinaryAssetContext;
};

export type CloudinaryAssetContext = {
  _type: "cloudinary.assetContext";
  custom?: CloudinaryAssetContextCustom;
};

export type AllSanitySchemaTypes =
  | SanityImagePaletteSwatch
  | SanityImagePalette
  | SanityImageDimensions
  | SanityFileAsset
  | Geopoint
  | ThisWeekInPhotosSection
  | MoreNewsSection
  | TrendingArticlesSection
  | HighlightedArticleSection
  | CategoryWithSubcategoriesSection
  | CategorySection
  | HomePage
  | BreakingNews
  | LiveUpdate
  | LiveBlogArticle
  | DailyStoryArticle
  | Country
  | SocialMediaMetadata
  | Tag
  | SeoMetadata
  | InquiryTopic
  | ComplianceLink
  | Timeline
  | StaticTabbedPage
  | SanityImageCrop
  | SanityImageHotspot
  | SanityImageAsset
  | SanityAssetSourceData
  | SanityImageMetadata
  | StaticColumnPage
  | SocialMedia
  | MenuItem
  | Subcategory
  | Category
  | SectionObject
  | NavigationMenu
  | AuthorSocialMedia
  | AboutAuthor
  | Author
  | Slug
  | Location
  | JobTitle
  | CloudinaryAssetContextCustom
  | CloudinaryAssetDerived
  | CloudinaryAsset
  | CloudinaryAssetContext;
export declare const internalGroqTypeReferenceTo: unique symbol;
