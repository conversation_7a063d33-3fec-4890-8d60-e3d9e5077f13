"use client";
import { useState, useRef } from "react";
import { MultiSelectProps } from "@/components/ui/Form/MultiSelect/types";
import { Icon } from "@/components/Icon";

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selectedOptions,
  maxSelectedOptions = 20,
  onSelect,
  onRemove,
  placeholder,
  onEmptyText,
}) => {
  const [searchText, setSearchText] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredOptions = options.filter(
    (option) =>
      !selectedOptions.some((selected) => selected.id === option.id) &&
      option.name?.toLowerCase().includes(searchText.toLowerCase()),
  );

  const handleSelect = (id: string) => {
    onSelect(id);
    setSearchText("");
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === "Escape") {
      setSearchText("");
    }
  };

  return (
    <div>
      <div className="rounded-xl bg-white px-4 py-3 flex gap-4 mb-4">
        <input
          ref={inputRef}
          type="text"
          className="border-0 text-sm outline-none flex-grow disabled:bg-transparent"
          placeholder={placeholder}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          disabled={selectedOptions.length >= maxSelectedOptions}
          onKeyDown={handleKeyDown}
        />

        <Icon icon="add" />
      </div>

      {searchText.length >= 3 &&
        (filteredOptions.length ? (
          <div className="flex flex-wrap gap-2 mb-4">
            {filteredOptions.map((option) => (
              <button
                key={option.id}
                className="flex transition-all hover:border-black gap-1 items-center justify-center border-grey-200 rounded-3xl border py-1 px-4 text-sm bg-white cursor-pointer"
                onClick={() => handleSelect(option.id)}
              >
                {option.name}
                <Icon icon="add" />
              </button>
            ))}
          </div>
        ) : (
          <div className="text-xs mb-4">No matches for that search.</div>
        ))}

      <div className="mb-4 text-[16px]">
        <span
          className={`${selectedOptions.length >= maxSelectedOptions ? "text-red-700" : ""} font-bold`}
        >
          {selectedOptions.length}
        </span>
        /<span className="font-bold">20</span>
        {selectedOptions.length >= maxSelectedOptions ? (
          <span className="ml-1">{onEmptyText}</span>
        ) : (
          ""
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {selectedOptions.map((selectedOption) => (
          <button
            key={selectedOption.id}
            className="flex transition-all hover:border-black gap-1 items-center justify-center border-grey-200 rounded-3xl border py-1 px-4 text-sm bg-white cursor-pointer"
            onClick={() => onRemove(selectedOption.id)}
          >
            {selectedOption.name}

            <Icon icon="error" />
          </button>
        ))}
      </div>
    </div>
  );
};

export default MultiSelect;
