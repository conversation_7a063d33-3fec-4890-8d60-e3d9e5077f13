## Cna Webapp

This is a web application for the Catholic News Agency project. It is built with Next.js, a React framework. The application is a single page application that allows users to view and interact with the CNA news. The application is hosted on Vercel.

## Getting Started

First, install the dependencies:

```bash
pnpm install
```

Next we login to Vercel using the Vercel CLI to fetch local development
environment variables.

```bash
pnpm i -g vercel
vercel login
```

Note, using the Github authentication method is recommended. Be careful, do not share your credentials nor check them into to version control.

To generate local environment file we invoke the Vercel CLI with a `link` argument. The tool will
guide you through the process.
The following is an example where linking was performed locally.

```bash
➜  cna-webapp git:(main) ✗ vercel link
Vercel CLI 39.3.0
> > No existing credentials found. Please log in:
? Log in to Vercel github
> Success! GitHub authentication complete for ewtn-news
? Set up “~/ewtn/cna-webapp”? yes
? Which scope should contain your project? EWTN News
? Found project “ewtn-news/cna-webapp”. Link to it? yes
✅  Linked to ewtn-news/cna-webapp (created .vercel)
```

When the link has been established, we can pull the variables using the env pull subcommand.

```bash
vercel env pull --environment=development
```

Then run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the webapp locally.

## Project Structure

The project is structured as follows:

- `components/`: Contains all the React components used in the application.
- `app/`: Contains the Next.js pages that are used to render the application.
- `sanity/`: Contains the sanity configuration, queries and types.
- `public/`: Contains the public assets used in the application.
- `styles/`: Contains the global styles used in the application.
- `utils/`: Contains utility functions used in the application.
- `__tests__/`: Contains the tests for the application.

The project uses tailwindcss for styling. The tailwindcss configuration is in the `tailwind.config.ts` file.

## Testing

To run the tests, run the following command:

```bash
pnpm test
```

This will run all the tests in the `__tests__/` directory.

## Linting

To lint the project, run the following command:

```bash
pnpm lint
```

This will run the linter on the project.

## Building

To build the project, run the following command:

```bash
pnpm build
```

This will build the project for production.

## Storybook

To run the storybook, run the following command:

```bash
pnpm storybook
```

This will run the storybook on [http://localhost:3100](http://localhost:3100).
It allows you to explore and interact with components in an isolated environment, making it easier to test and document them.

# Husky Pre-Push Hook

This project uses [Husky](https://typicode.github.io/husky/) to enforce consistent code formatting and run tests automatically before pushing to the repository.

Format and unit tests are locally required to push the code.

