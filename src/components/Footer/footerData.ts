import { IconName } from "../Icon";

export const otherLinksData = [
  {
    name: "Editor Service",
    href:
      process.env.NEXT_PUBLIC_EDITOR_SERVICE_URL ||
      "https://editors.catholicnewsagency.com/login",
    icon: "edit" as IconName,
  },
];

// TODO: Replace with localization feature
export const languages = [
  { label: "English", value: 1 },
  { label: "Spanish", value: 2 },
  { label: "French", value: 3 },
  { label: "German", value: 4 },
  { label: "Italian", value: 5 },
  { label: "Chinese", value: 6 },
  { label: "Japanese", value: 7 },
  { label: "Korean", value: 8 },
  { label: "Portuguese", value: 9 },
  { label: "Russian", value: 10 },
  { label: "Arabic", value: 11 },
  { label: "Hindi", value: 12 },
];
