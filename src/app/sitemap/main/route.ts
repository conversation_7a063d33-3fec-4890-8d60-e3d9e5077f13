import { sanityFetch } from "@/sanity/client";
import {
  Author,
  authorsQuery,
  categoriesQuery,
  Category,
  Tag,
  tagsQuery,
} from "@/sanity/queries/sitemap";
import { NextResponse } from "next/server";

export async function GET() {
  const baseUrl = process.env.APP_BASE_URL || "";

  const sections: {
    url: string;
    changefreq?: string;
    priority?: string;
  }[] = [
    {
      url: "",
      changefreq: "always",
      priority: "1.0",
    },
    {
      url: "top-stories",
      changefreq: "always",
      priority: "1.0",
    },
    {
      url: "latest",
      changefreq: "always",
      priority: "1.0",
    },
    {
      url: "about-us",
    },
    {
      url: "contact-us",
    },
    {
      url: "terms-and-conditions",
    },
  ];

  const categories: Category[] = await sanityFetch({
    query: categoriesQuery,
    params: {},
  });
  const authors: Author[] = await sanityFetch({
    query: authorsQuery,
    params: {},
  });
  const tags: Tag[] = await sanityFetch({
    query: tagsQuery,
    params: {},
  });

  categories.map((category) => {
    sections.push({ url: category.slug });

    if (category.subcategories) {
      category.subcategories.map((subcategory) => {
        sections.push({ url: `${category.slug}/${subcategory.slug}` });
      });
    }
  });

  authors.map((author) => {
    sections.push({ url: `author/${author.slug}` });
  });

  tags.map((tag) => {
    sections.push({ url: `tags/${tag.slug}` });
  });

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  sections.map(
    (section) =>
      (sitemap += `<url>
      <loc>${baseUrl}${section.url}</loc>
      <lastmod>${new Date().toISOString()}</lastmod>
      <changefreq>${section.changefreq ?? "montly"}</changefreq>
      <priority>${section.priority ?? "0.7"}</priority>
    </url>`),
  );

  sitemap += `</urlset>`;

  return new NextResponse(sitemap, {
    headers: {
      "Content-Type": "application/xml",
    },
  });
}
