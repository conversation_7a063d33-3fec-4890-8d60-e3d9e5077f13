import { Article } from "@/sanity/queries/Trending/trending";
import ImageCarousel from "../ImageCarousel";
import CategoryArticlesSection from "./CategoryArticlesSection";
import CategoryWithSubcategoriesSection from "./CategoryWithSubcategoriesSection";
import HighlightedNews from "./HighlightedNews";
import MoreNewsSection from "./MoreNewsSection";
import { Trending } from "./Trending";
import { HomeSection } from "@/sanity/queries/HomePage/homePage";
import { SectionType } from "@/types/homePage";

type SectionMapperProps = {
  section: HomeSection;
  trendingArticles: Article[];
};

export default function SectionMapper({
  section,
  trendingArticles,
}: SectionMapperProps) {
  const { _type: type } = section;
  switch (type) {
    case SectionType.Category:
      return <CategoryArticlesSection section={section} />;
    case SectionType.CategoryWithSubcategories:
      return <CategoryWithSubcategoriesSection section={section} />;
    case SectionType.HighlightedArticle:
      return <HighlightedNews section={section} />;
    case SectionType.MoreNews: {
      const moreNewsArticles =
        (trendingArticles.length >= 6 &&
          trendingArticles.slice(5, trendingArticles.length)) ||
        [];
      return (
        <>
          {moreNewsArticles.length > 0 && (
            <MoreNewsSection articles={moreNewsArticles} />
          )}
        </>
      );
    }
    case SectionType.ThisWeekInPhotos:
      return (
        <section className="container mx-auto py-4 px-4">
          <div className="pb-2 h-full">
            <ImageCarousel section={section} />
          </div>
        </section>
      );
    case SectionType.TrendingArticlesSection:
      const top5Trending =
        (trendingArticles.length >= 5 && trendingArticles.slice(0, 5)) || [];
      return (
        <div className="order-4 lg:order-none bg-grey-100">
          <Trending trendingArticles={top5Trending} />
        </div>
      );
    default:
      return null;
  }
}
