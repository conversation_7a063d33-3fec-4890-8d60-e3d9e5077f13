"use client";
import { Icon } from "@/components/Icon";
import { Section } from "@/sanity/queries/layout";
import Link from "next/link";
import { useState } from "react";

export interface ExpandableListProps {
  section: Section;
  setOpenNavigation: (open: boolean) => void; // Accept setOpenNavigation as a prop
}

const ExpandableList = ({
  section,
  setOpenNavigation,
}: ExpandableListProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      data-testid="expandable-list"
      className="border-b border-grey-200 pb-3"
    >
      <div
        className="flex justify-between items-center font-semibold cursor-pointer"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
      >
        <h3 data-testid="expandable-list-title">{section.title}</h3>
        <Icon icon={isOpen ? "collapse" : "open"} />
      </div>
      {isOpen && (
        <ul
          data-testid="expandable-list-items"
          className="mt-4 flex flex-col gap-2"
        >
          {section.menuItems.map((item) => (
            <li key={item.title}>
              <Link
                className="hover-underline-animation"
                href={
                  item.linkType === "internal"
                    ? `/${section.slug?.current}/${item.internalLink?.current}`
                    : item.externalLink
                }
                onClick={() => setOpenNavigation(false)} // Close the drawer when clicked
              >
                {item.title}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ExpandableList;
