"use client";

import { ReactNode } from "react";
import { usePathname } from "next/navigation";
import DefaultLayout from "@/components/Layout/DefaultLayout";
import MinimizedLayout from "@/components/Layout/MinimizedLayout";
import {
  DonationLinkQueryResult,
  EwtnNetworkLinksQueryResult,
  NavigationMenuQueryResult,
} from "@/sanity/queries/layout";

// Use the type that matches the expected structure
import {
  SocialMediaQueryResult,
  ComplianceLinkQueryResult,
} from "@/sanity/queries/layout";
import { AllShowsQueryResult } from "@/sanity/queries/Show/show";

const minimizedLayoutPrefixes = [
  "/account",
  "/register",
  "/login",
  "/activate-your-account",
];
const minimizedHeaderPrefixes = [
  "/register",
  "/login",
  "/activate-your-account",
];

export default function ClientLayout({
  layoutData,
  children,
}: {
  layoutData: {
    navigationMenu: NavigationMenuQueryResult;
    watchMenu: NavigationMenuQueryResult;
    socialMediaItems: SocialMediaQueryResult;
    complianceLinkItems: ComplianceLinkQueryResult;
    donationLink: DonationLinkQueryResult;
    ewtnNetworkLinks: EwtnNetworkLinksQueryResult;
    allShows: AllShowsQueryResult;
  };
  children: ReactNode;
}) {
  const pathname = usePathname();

  const usesMinimizedLayout = minimizedLayoutPrefixes.some((prefix) =>
    pathname?.startsWith(prefix),
  );
  const usesMinimizedHeader = minimizedHeaderPrefixes.some((prefix) =>
    pathname?.startsWith(prefix),
  );

  const navigationMenu = pathname?.startsWith("/watch")
    ? layoutData.watchMenu
    : layoutData.navigationMenu;

  const layoutProps = {
    navigationMenu,
    socialMediaItems: layoutData.socialMediaItems,
    complianceLinkItems: layoutData.complianceLinkItems,
    donationLink: layoutData.donationLink,
    isHeaderReduced: usesMinimizedHeader,
    ewtnNetworkLinks: layoutData.ewtnNetworkLinks,
    allShows: layoutData.allShows,
  };

  return usesMinimizedLayout ? (
    <MinimizedLayout {...layoutProps}>{children}</MinimizedLayout>
  ) : (
    <DefaultLayout {...layoutProps}>{children}</DefaultLayout>
  );
}
