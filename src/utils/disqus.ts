export async function fetchDisqusCommentCount(
  articleId: string,
): Promise<number | null> {
  const apiKey = process.env.NEXT_PUBLIC_DISQUS_API_KEY;
  if (!apiKey) {
    console.error("Disqus API key missing");
    return null;
  }

  try {
    const res = await fetch(
      `https://disqus.com/api/3.0/threads/details.json?api_key=${apiKey}&forum=ewtn-dev&thread:ident=dailyStory-${articleId}`,
    );

    const data = await res.json();

    return data?.response?.posts ?? 0;
  } catch (e) {
    console.error("Error fetching Disqus comment count", e);
    return null;
  }
}
