"use client";
import React, { useEffect, useState, useRef } from "react";
import { DateRange } from "react-date-range";
import { RangeKeyDict } from "react-date-range";
import { format } from "date-fns";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { useRouter } from "next/navigation";

export interface DateRangeOption {
  startDate: string;
  endDate: string;
  key: string;
}

interface DueDateDropdownProps {
  onChange?: (selected: DateRangeOption) => void;
  onClose?: () => void;
  value?: DateRangeOption;
}

const parseDate = (dateString: string): Date => {
  const [year, month, day] = dateString.split("-");
  return new Date(Number(year), Number(month) - 1, Number(day));
};

const DueDateDropdown = ({
  onChange,
  onClose,
  value,
}: DueDateDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [internalSelected, setInternalSelected] = useState<DateRangeOption>();
  const selected = value ?? internalSelected;
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [dateRange, setDateRange] = useState([
    {
      startDate: undefined as Date | undefined,
      endDate: undefined as Date | undefined,
      key: "selection",
    },
  ]);

  const router = useRouter();
  const [hasSelectedStart, setHasSelectedStart] = useState(false);

  useEffect(() => {
    if (selected?.startDate && selected?.endDate) {
      setDateRange([
        {
          startDate: parseDate(selected.startDate.split("T")[0]),
          endDate: parseDate(selected.endDate.split("T")[0]),
          key: selected.key,
        },
      ]);
    } else {
      setDateRange([
        {
          startDate: undefined,
          endDate: undefined,
          key: "selection",
        },
      ]);
    }
  }, [selected]);

  const updateSelection = (next: DateRangeOption) => {
    if (onChange) onChange(next);
    if (!value) setInternalSelected(next);
  };

  const clearSelection = () => {
    const emptyRange = { startDate: "", endDate: "", key: "selection" };
    updateSelection(emptyRange);
    setDateRange([
      {
        startDate: undefined,
        endDate: undefined,
        key: "selection",
      },
    ]);
    setInternalSelected(undefined);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        onClose?.();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  const formattedRange =
    dateRange[0].startDate && dateRange[0].endDate
      ? `${format(dateRange[0].startDate, "MMM dd, yyyy")} → ${format(
          dateRange[0].endDate,
          "MMM dd, yyyy",
        )}`
      : "";

  useEffect(() => {
    if (!isOpen) {
      onClose?.();
    }
  }, [isOpen, onClose]);

  useEffect(() => {
    if (!isOpen) return;

    const timeout = setTimeout(() => {
      const inputs = document.querySelectorAll(".rdrDateInput input");
      if (
        inputs.length >= 2 &&
        !(dateRange[0].startDate && dateRange[0].endDate)
      ) {
        const inputStart = inputs[0] as HTMLInputElement;
        const inputEnd = inputs[1] as HTMLInputElement;

        inputStart.placeholder = "Start date";
        inputStart.value = "";

        inputEnd.placeholder = "End date";
        inputEnd.value = "";
      }
    }, 0);

    return () => clearTimeout(timeout);
  }, [isOpen, dateRange]);
  return (
    <div className="relative w-full" ref={dropdownRef}>
      <div
        onClick={() => setIsOpen(true)}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            setIsOpen(true);
          }
        }}
        className="h-[46px] px-4 border border-grey-200 rounded-xl bg-white flex items-center justify-between cursor-pointer relative transition-all duration-200"
        style={{
          width: formattedRange ? "260px" : "141px",
        }}
      >
        <span className="truncate leading-[20px] font-normal">
          {formattedRange || "Select Date"}
        </span>
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-600 flex items-center justify-center">
          {dateRange[0].startDate && dateRange[0].endDate ? (
            <button
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
                const queryParams = new URLSearchParams(window.location.search);
                queryParams.delete("startDate");
                queryParams.delete("endDate");
                router.replace(`?${queryParams.toString()}`);
              }}
              className="text-[18px] cursor-pointer"
              title="Clear date"
              aria-label="Clear date"
            >
              ×
            </button>
          ) : (
            <svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-full h-full pointer-events-none"
            >
              <polyline points="6 9 12 15 18 9" />
            </svg>
          )}
        </div>
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-2 bg-white rounded-xl shadow-lg overflow-hidden">
          <DateRange
            editableDateInputs
            onChange={(item: RangeKeyDict) => {
              const { startDate, endDate, key } = item.selection;

              setDateRange([item.selection]);

              if (!hasSelectedStart) {
                setHasSelectedStart(true);
                return;
              }
              if (!startDate || !endDate) return;

              const formattedStart = startDate.toISOString();
              const formattedEnd = endDate.toISOString();
              const start = formattedStart.split("T")[0];
              const end = formattedEnd.split("T")[0];

              const prevStart = value?.startDate?.split("T")[0] || "";
              const prevEnd = value?.endDate?.split("T")[0] || "";

              const hasChanged = start !== prevStart || end !== prevEnd;

              if (hasChanged) {
                updateSelection({
                  startDate: formattedStart,
                  endDate: formattedEnd,
                  key,
                });

                const queryParams = new URLSearchParams(window.location.search);
                queryParams.set("startDate", start);
                queryParams.set("endDate", end);
                router.replace(`?${queryParams.toString()}`);
              }

              setHasSelectedStart(false);
            }}
            moveRangeOnFirstSelection={false}
            ranges={
              dateRange[0].startDate && dateRange[0].endDate
                ? dateRange
                : [
                    {
                      startDate: new Date(),
                      endDate: new Date(),
                      key: "selection",
                    },
                  ]
            }
          />
        </div>
      )}
    </div>
  );
};

export default DueDateDropdown;
