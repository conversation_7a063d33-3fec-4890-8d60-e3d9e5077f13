export const showQuery = `*[_type == "show" && slug.current == $slug][0] {
  _id,
  name,
  "slug": slug.current,
  shortDescription,
  description,
  "imageUrl": image.secure_url,
  "imageMobileUrl": imageMobile.secure_url,
  "logoUrl": logo.secure_url,
  "bannerUrl": banner.secure_url,
  "bannerMobileUrl": bannerMobile.secure_url,
  redirectBannerUrl,
  dateTime,
  socialMedia,
  relatedShows[]->{
    name,
    "slug": slug.current,
    "portraitImageUrl": portraitImage.secure_url
  }
}`;

export const allShowsQuery = `*[_type == "show" && defined(slug.current)]{
  name,
  "slug": slug.current
}`;

export type AllShowsQueryResult = Array<{
  name: string;
  slug: string;
}>;
