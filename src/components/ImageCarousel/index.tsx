"use client";

import { useState, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import Button from "../ui/Button";
import { getArticleUrl, getMediaImage, truncateTitle } from "@/utils/utils";
import { Media } from "@/sanity/queries/HomePage/categoryArticles";
import Link from "next/link";
import Image from "next/image";

// Updated Slide type
type Slide = {
  imageUrl: string;
  category: string;
  caption: string;
  title: string;
  slug: string;
  categorySlug?: string;
  subcategorySlug?: string;
};

// Component props
type ImageCarouselProps = {
  section: {
    _type: "thisWeekInPhotosSection";
    articles: {
      title: string;
      slug: string;
      media: Media[];
      category?: { title: string; slug: string };
      subcategory?: { slug?: string };
    }[];
  };
};

const ImageCarousel: React.FC<ImageCarouselProps> = ({ section }) => {
  const [currentIndex, setCurrentIndex] = useState(1);
  const swiperRef = useRef<SwiperType | null>(null);

  const slides: Slide[] = section.articles
    .filter((x) => x.media)
    .map((article) => ({
      imageUrl: getMediaImage(article.media[0]),
      category: article.category?.title ?? "",
      caption: article.media[0]?.text ?? "",
      title: article.title,
      slug: article.slug,
      categorySlug: article.category?.slug,
      subcategorySlug: article.subcategory?.slug,
    }));

  return (
    <div className="relative w-full mx-auto border-t py-3 xl:pt-3">
      <h2 className="text-[18px] lg:text-[20px] font-bold uppercase">
        This Week in Photos
      </h2>
      <p className="text-[16px] lg:text-[18px] pb-3">
        Capturing Moments that Matter
      </p>

      {/* Swiper */}
      <div className="relative">
        <Swiper
          modules={[Navigation, Pagination]}
          pagination={{ clickable: true }}
          onSlideChange={(swiper) => setCurrentIndex(swiper.realIndex + 1)}
          onSwiper={(swiper) => (swiperRef.current = swiper)}
          className="w-full h-[480px] sm:h-[350px] md:h-[512px] overflow-hidden rounded-2xl"
          loop={true}
          autoplay={{ delay: 5000 }}
        >
          {slides.map((item, index) => (
            <SwiperSlide key={index}>
              <div className="relative w-full h-full">
                <Image
                  src={item.imageUrl}
                  alt={item.caption}
                  className="!w-full !h-full object-cover"
                  layout="responsive"
                  width={16}
                  height={9}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>

                {/* Text Content */}
                <div className="absolute bottom-4 left-4 right-4 text-white p-4 flex flex-col md:flex-row justify-between items-start lg:items-end gap-4">
                  <div className="w-full md:w-auto">
                    <p className="text-sm uppercase">{item.category}</p>
                    <h3 className="text-[22px] sm:text-[24px] md:text-[38px] lg:text-[43px] font-bold font-titles">
                      <Link
                        href={getArticleUrl(
                          item.categorySlug!,
                          item.slug,
                          item.subcategorySlug!,
                        )}
                        className="hover-underline-animation-white"
                      >
                        {truncateTitle(item.title)}
                      </Link>
                    </h3>
                  </div>
                  <div className="w-full md:w-3/5 lg:w-2/5 xl:w-1/4 text-left border-t border-white border-opacity-70 md:border-l md:border-t-0 md:pl-3 pt-3">
                    <p className="text-sm font-semibold">
                      {currentIndex}/{slides.length}
                    </p>
                    <p className="text-[10px] sm:text-[14px] lg:text-sm">
                      {item.caption}
                    </p>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Custom Navigation Buttons - Half Inside, Half Outside */}
        <Button
          as="icon"
          onClick={() => swiperRef.current?.slidePrev()}
          iconName="arrow_back"
          variant="secondary"
          className="hidden w-[60px] h-[60px] lg:flex absolute top-1/2 -translate-y-1/2 left-[-30px] z-10"
        />
        <Button
          as="icon"
          onClick={() => swiperRef.current?.slideNext()}
          iconName="arrow_forward"
          variant="secondary"
          className="hidden w-[60px] h-[60px] lg:flex absolute top-1/2 -translate-y-1/2 right-[-30px] z-10"
        />
      </div>
    </div>
  );
};

export default ImageCarousel;
