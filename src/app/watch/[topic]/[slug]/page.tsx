import { notFound } from "next/navigation";
import { fetchTrendingArticlesFromAnalytics } from "@/app/api/trending";
import JsonLD from "@/components/JsonLd";
import { buildVideoPageJsonLd } from "@/utils/metadata";
import { VideoClient } from "./VideoClient";
import { mockVideoItems } from "../../mocks";

type VideoProps = {
  params: Promise<{
    slug: string;
  }>;
};

// TODO: Add metadata of video page

export default async function VideoPage({ params }: VideoProps) {
  const { slug } = await params;

  if (!slug) {
    return notFound();
  }

  const index = mockVideoItems.findIndex(
    (video) => video.href == `/watch/topic/${slug}`,
  );
  console.log(slug, index);

  const reorderedVideos = [
    ...mockVideoItems.slice(index),
    ...mockVideoItems.slice(0, index),
  ];

  const trendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 15,
    categorySlug: null,
    subcategorySlug: null,
    periodEnd: undefined,
    periodStart: undefined,
  });

  const jsonLd = buildVideoPageJsonLd(
    reorderedVideos[0].title,
    `${reorderedVideos[0].href}`,
    `${reorderedVideos[0].href}`,
    `${reorderedVideos[0].href}`,
    `${reorderedVideos[0].timestamp}+00:00`,
    "PT2M30S",
    reorderedVideos[0].description,
  );
  return (
    <>
      <JsonLD jsonLd={jsonLd} />

      <VideoClient
        playlistVideos={reorderedVideos}
        relatedArticles={trendingArticles}
        relatedVideos={reorderedVideos}
        moreVideos={reorderedVideos}
        category="EWTN NEWS IN DEPTH"
      />
    </>
  );
}
