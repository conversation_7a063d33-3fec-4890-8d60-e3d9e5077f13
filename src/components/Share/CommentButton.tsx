"use client";

import { Icon } from "../Icon";

interface CommentButtonProps {
  commentCount: number | null;
  commentCountLoading?: boolean;
  onOpenComments?: () => void;
  useLikeButtonStyle?: boolean;
  isFloating?: boolean;
}

const CommentButton = ({
  commentCount,
  commentCountLoading = false,
  onOpenComments,
  useLikeButtonStyle = false,
  isFloating = false,
}: CommentButtonProps) => {
  const handleClick = () => {
    onOpenComments?.();
  };

  return (
    <div className="relative z-20">
      {useLikeButtonStyle ? (
        <div className="relative">
          <button
            className="flex flex-row items-center text-[12px] justify-center mt-3 hover:border-black transition-all p-4"
            onClick={handleClick}
            disabled={commentCountLoading}
          >
            <Icon icon="comments" className="mr-1" />
            <span className="transition-all">
              {commentCountLoading ? (
                <Icon
                  icon="loading"
                  className="animate-spin inline-block w-4"
                />
              ) : commentCount !== null ? (
                commentCount
              ) : (
                "0"
              )}
            </span>
          </button>
        </div>
      ) : (
        <div className="flex border border-grey-200 rounded-md bg-white hover:border-black transition-all">
          <button
            onClick={handleClick}
            className={`flex flex-col items-center text-[12px] justify-center pt-2  ${isFloating ? "pb-1 mt-[0.4em] px-[1.1em]" : "px-3 pb-[0.345em] mt-1.5"}`}
          >
            <Icon icon="comments" className="mt-2" />
            <span className="relative -top-1 transition-all">
              {commentCountLoading ? (
                <Icon
                  icon="loading"
                  className="animate-spin inline-block w-4"
                />
              ) : commentCount !== null ? (
                commentCount
              ) : (
                "0"
              )}
            </span>
          </button>
        </div>
      )}
    </div>
  );
};

export default CommentButton;
