import { render, screen } from "@testing-library/react";
import AboutUsPage from "@/app/about-us/page";
import { sanityFetch } from "@/sanity/client";
import {
  staticTabbedPageQuery,
  StaticTabbedPageQueryResult,
} from "@/sanity/queries/static-tabbed-page";

jest.mock("@/sanity/client", () => ({
  sanityFetch: jest.fn(),
}));

describe("AboutUsPage", () => {
  const mockPageData: StaticTabbedPageQueryResult = {
    title: "About Us",
    slug: { current: "about-us" },
    imageUrl: "/test.jpg",
    description: [
      {
        _type: "block",
        children: [
          {
            text: "Description text",
            _type: "span",
            marks: [],
            _key: "1",
          },
        ],
        _key: "block1",
      },
    ],
    content: {
      tabBody: [
        {
          title: "Tab 1",
          slug: { current: "tab-1" },
          textBody: [
            {
              _type: "block",
              children: [
                {
                  text: "Tab 1 content",
                  _type: "span",
                  marks: [],
                  _key: "2",
                },
              ],
              _key: "block2",
            },
          ],
        },
      ],
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (sanityFetch as jest.Mock).mockResolvedValue(mockPageData);
  });

  test("fetches and renders page data", async () => {
    const page = await AboutUsPage();
    render(page);

    expect(sanityFetch).toHaveBeenCalledWith({
      query: staticTabbedPageQuery,
      params: { slug: "about-us" },
    });

    expect(
      screen.getByRole("heading", { name: /about us/i }),
    ).toBeInTheDocument();
  });
});
