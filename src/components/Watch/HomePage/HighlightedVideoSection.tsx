"use client";
import { HighlightVideoItem } from "@/app/watch/mocks";
import VideoPlayer from "../../ui/VideoPlayer";

type HiglightVideoSectionProps = {
  highlightVideo: HighlightVideoItem;
};

export default function HighlightedVideoSection({
  highlightVideo,
}: HiglightVideoSectionProps) {
  return (
    <div className="w-full bg-black">
      <div className="mx-0 py-6 border-t border-grey-400 pt-4 pb-8">
        <h2 className="text-[18px] md:text-[20px] font-bold uppercase mb-4 text-white">
          LIFE OF THE POPE
        </h2>

        <div className="flex flex-col lg:flex-row lg:items-start gap-6">
          <div className="flex-none w-full aspect-[16/9] sm:w-[458px] md:w-[640px] lg:w-[958px] relative rounded-2xl overflow-hidden">
            <VideoPlayer
              src={highlightVideo.videoUrl}
              playing={false}
              controls
              pauseOnClickOutside={false}
            />
          </div>

          <div className="flex-1">
            <p className="uppercase text-sm text-white/70 mb-2">
              {highlightVideo.tag}
            </p>
            <h3 className="text-white text-[24px] lg:text-[32px] font-bold leading-snug mb-4">
              {highlightVideo.title}
            </h3>
            <p className="text-white/80 text-sm leading-relaxed mb-4">
              {highlightVideo.description}
            </p>
            <p className="text-white/60 text-sm">
              by <span className="font-semibold">Kristina Millare</span> · 6:34
              PM ET
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
