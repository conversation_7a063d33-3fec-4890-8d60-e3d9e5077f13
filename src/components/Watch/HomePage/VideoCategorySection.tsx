"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { useState, useRef } from "react";
import type { Swiper as SwiperType } from "swiper";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Link from "next/link";
import { Icon } from "@/components/Icon";
import Button from "../../ui/Button";
import { VideoItem } from "@/app/watch/mocks";
import { VideoCard } from "../VideoCard";

type VideoCategorySectionProps = {
  title: string;
  videos: VideoItem[];
};

export default function VideoCategorySection({
  title,
  videos,
}: VideoCategorySectionProps) {
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const swiperRef = useRef<SwiperType | null>(null);

  function createSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // Remover caracteres especiales
      .replace(/\s+/g, "-") // Reemplazar espacios con guiones
      .replace(/-+/g, "-") // Reemplazar múltiples guiones con uno solo
      .trim() // Quitar espacios al inicio y final
      .replace(/^-+|-+$/g, ""); // Quitar guiones al inicio y final
  }

  return (
    <div className="relative border-t pt-2 lg:pt-4 pb-2 lg:pb-12 border-grey-400 overflow-visible">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-white text-xl font-semibold">{title}</h2>
        <Link
          href={`/watch/${createSlug(title)}`}
          className="text-white flex items-center gap-1 text-sm"
        >
          View All
          <Icon icon="arrow_forward" className="!text-sm" />
        </Link>
      </div>

      <div className="mx-auto relative overflow-visible">
        <div className="-mx-4 sm:-mx-6 px-4 sm:px-6 overflow-hidden">
          <Swiper
            spaceBetween={30}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
            modules={[Navigation]}
            className="!overflow-visible mt-2"
            onSwiper={(swiper) => {
              swiperRef.current = swiper;
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }}
            onSlideChange={(swiper) => {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }}
            breakpoints={{
              0: { slidesPerView: 1.3 },
              768: { slidesPerView: 2 },
              1024: { slidesPerView: 3 },
              1280: { slidesPerView: 4 },
            }}
          >
            {videos.map((video, index) => (
              <SwiperSlide
                key={video.title}
                className={`pl-[2px] pr-[2px] ${
                  index === videos.length - 1 ? "mr-2" : ""
                }`}
              >
                <VideoCard
                  showDescription={false}
                  video={video}
                  thumbnail={video.imageUrl}
                  mode="column"
                  showCaption={false}
                  showTimestamp={true}
                />
              </SwiperSlide>
            ))}
          </Swiper>

          {!isBeginning && (
            <Button
              as="icon"
              onClick={() => swiperRef.current?.slidePrev()}
              iconName="arrow_back"
              variant="secondary"
              className="hidden lg:flex w-[50px] h-[50px] absolute top-[calc(50%-60px)] left-0 -ml-[30px] z-10"
            />
          )}
          {!isEnd && (
            <Button
              as="icon"
              onClick={() => swiperRef.current?.slideNext()}
              iconName="arrow_forward"
              variant="secondary"
              className="hidden lg:flex w-[50px] h-[50px] absolute top-[calc(50%-60px)] right-0 -mr-[30px] z-10"
            />
          )}
        </div>
      </div>
    </div>
  );
}
