import { NextRequest, NextResponse } from "next/server";
import { fetchTrendingArticlesFromAnalytics } from ".";

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const listSize = parseInt(url.searchParams.get("listSize") || "12");
    const periodStart = url.searchParams.get("periodStart") || undefined;
    const periodEnd = url.searchParams.get("periodEnd") || undefined;

    const trendingNews = await fetchTrendingArticlesFromAnalytics({
      listSize,
      categorySlug: null,
      subcategorySlug: null,
      periodEnd,
      periodStart,
    });

    return NextResponse.json(trendingNews);
  } catch (error) {
    console.error("Error fetching articles:", error);

    return NextResponse.json(
      {
        error: "Error fetching articles",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
