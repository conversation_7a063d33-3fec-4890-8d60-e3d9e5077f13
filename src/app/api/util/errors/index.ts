import { NextResponse } from "next/server";

export function successResponse(message: string, status: number = 200) {
  return NextResponse.json({ message }, { status });
}

export function successResponseWithData(data: unknown, status: number = 200) {
  return NextResponse.json(data, { status });
}

export function unauthorizedResponse() {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}

export function badRequestResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 400 });
}

export function errorResponse(error: unknown) {
  const errorMessage =
    error instanceof Error ? error.message : "An unknown error occurred";
  return NextResponse.json({ error: errorMessage }, { status: 500 });
}
