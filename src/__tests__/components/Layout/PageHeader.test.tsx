import { render, screen } from "@testing-library/react";
import PageHeader from "@/components/Layout/PageHeader";

// Mock Next.js Link component
jest.mock("next/link", () => {
  return function MockLink({
    href,
    children,
    className,
  }: {
    href: string;
    children: React.ReactNode;
    className?: string;
  }) {
    return (
      <a href={href} className={className} data-testid="next-link">
        {children}
      </a>
    );
  };
});

describe("PageHeader Component", () => {
  const defaultProps = {
    title: "Test Page Title",
    description: "This is a test page description",
  };

  test("renders the page header with title and description", () => {
    render(<PageHeader {...defaultProps} />);

    // Check if title is rendered correctly in the heading
    const headingTitle = screen.getByRole("heading", { level: 1 });
    expect(headingTitle).toHaveTextContent("Test Page Title");

    // Check if the breadcrumb shows the title
    const breadcrumbTitle = screen.getAllByText("Test Page Title")[0];
    expect(breadcrumbTitle).toBeInTheDocument();

    // Check if description is rendered
    expect(
      screen.getByText("This is a test page description"),
    ).toBeInTheDocument();
  });

  test("renders the breadcrumb navigation correctly", () => {
    render(<PageHeader {...defaultProps} />);

    // Check if Home link is present
    const homeLink = screen.getByTestId("next-link");
    expect(homeLink).toHaveAttribute("href", "/");
    expect(homeLink).toHaveTextContent("Home");

    // Check for breadcrumb separator (should be '·')
    expect(screen.getByText("·")).toBeInTheDocument();
  });
});
