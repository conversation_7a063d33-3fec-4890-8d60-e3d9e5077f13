import { Metadata } from "next";
import { generateShowMetadata } from "@/utils/metadata";
import { client } from "@/sanity/client";
import { showQuery } from "@/sanity/queries/Show/show";
import ShowClient, { ShowData } from "./ShowClient";
import { showVideosMock } from "./../showVideosMock.js";

type Props = {
  params: Promise<{ slug: string }>;
};

export async function generateMetadata(props: Props): Promise<Metadata> {
  const { slug } = await props.params;
  const showData: ShowData = await client.fetch(showQuery, { slug });
  return generateShowMetadata(slug, showData);
}

const ShowPage = async (props: Props) => {
  const { slug } = await props.params;
  const showData: ShowData = await client.fetch(showQuery, { slug });

  return <ShowClient showData={showData} showVideos={showVideosMock} />;
};

export default ShowPage;
