import { Icon } from "../Icon";
import ShareIconButton from "./ShareIconButton";

const FacebookMessengerButton = ({ shareUrl }: { shareUrl: string }) => {
  const shareOnMessenger = () => {
    const messengerUrl = `https://m.me/CatholicNewsAgency?text=${encodeURIComponent(
      "Check this out :: " + shareUrl,
    )}`;
    window.open(messengerUrl, "_blank", "width=800,height=600");
  };
  return (
    <button onClick={shareOnMessenger}>
      <ShareIconButton
        icon={(hover) => (
          <Icon
            icon="facebookMessenger"
            size={24}
            color={hover ? "#0088cc" : "black"}
          />
        )}
      />
    </button>
  );
};

export default FacebookMessengerButton;
