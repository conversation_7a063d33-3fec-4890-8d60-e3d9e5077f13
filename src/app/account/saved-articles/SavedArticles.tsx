"use client";

import { Article } from "@/app/api/articles/bookmarks/route";
import NewsCard from "@/components/Account/SavedArticles/newsCard";
import { Icon } from "@/components/Icon";
import Button from "@/components/ui/Button";
import { IMAGES_PATH } from "@/constants";
import { usePagination } from "@/utils/usePagination";
import Image from "next/image";

type Props = {
  onReload: () => void;
  initialItems: Article[];
  totalCount: number;
};

export default function SavedArticles({
  onReload,
  initialItems,
  totalCount,
}: Props) {
  const {
    items: news,
    isLoading,
    hasMore,
    loadMore,
  } = usePagination<Article>({
    initialItems,
    itemsPerPage: 10,
    totalCount,
    apiUrl: "/api/articles/bookmarks",
    initialPage: 1,
  });

  const handleRemoveAuthor = async (id: string): Promise<void> => {
    try {
      const response = await fetch(`/api/articles/${id}/bookmarks`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to remove");
      onReload();
    } catch (error) {
      console.error("Error removing saved article:", error);
    }
  };

  return (
    <>
      <div className="mt-6 pt-2 border-t border-black flex items-center gap-1 mb-4">
        {/* Temporarily hides saved articles count */}
        {/* <Icon icon="savedArticle" className="-ml-1" />
        <span className="text-[16px] lg:text-[18px] font-bold uppercase">
          {totalCount} saved articles
        </span> */}
      </div>

      {news.length === 0 ? (
        <div className="mt-8 max-w-96 mx-auto flex flex-col items-center">
          <Image
            src={`${IMAGES_PATH}/account/no-saved-articles.png`}
            alt="You don’t have any saved articles yet"
            width={170}
            height={170}
            className="max-w-full"
          />
          <p className="text-sm text-center">
            You don’t have any saved articles yet
          </p>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {news.map((item) => (
              <NewsCard
                key={item.id}
                item={item}
                onRemoveAuthor={() => handleRemoveAuthor(item.id)}
              />
            ))}
          </div>

          {hasMore && (
            <div className="mt-6 flex justify-center">
              <Button
                iconName="savedArticleFilled"
                onClick={loadMore}
                className="w-[280px] max-w-full"
                state={isLoading ? "disabled" : "enabled"}
                label="Load More"
              >
                {isLoading ? (
                  <Icon
                    icon="loading"
                    color="white"
                    className="animate-spin inline-block -mb-[5px]"
                  />
                ) : (
                  "Load More"
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </>
  );
}
