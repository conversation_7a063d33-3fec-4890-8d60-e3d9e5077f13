import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ClipboardButton from "@/components/Share/Clipboard";
import { toast } from "react-toastify";

// Mock the toast notifications and clipboard API
jest.mock("react-toastify", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
});

describe("ClipboardButton", () => {
  const shareUrl = "https://ewtn.com";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should display a success toast when the link is copied", async () => {
    (navigator.clipboard.writeText as jest.Mock).mockResolvedValueOnce(true);

    render(<ClipboardButton shareUrl={shareUrl} />);

    const button = screen.getByTestId("clipboard-button");

    fireEvent.click(button);

    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(shareUrl);
      expect(toast.success).toHaveBeenCalledWith(
        "Link successfully copied to the clipboard!",
        expect.any(Object),
      );
    });
  });

  it("should display an error toast when the clipboard copy fails", async () => {
    (navigator.clipboard.writeText as jest.Mock).mockRejectedValueOnce(
      new Error("Clipboard error"),
    );

    render(<ClipboardButton shareUrl={shareUrl} />);

    const button = screen.getByTestId("clipboard-button");

    fireEvent.click(button);

    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(shareUrl);
      expect(toast.error).toHaveBeenCalledWith("Failed to copy the link!");
    });
  });

  it("should change background color on hover", () => {
    render(<ClipboardButton shareUrl={shareUrl} />);

    const button = screen.getByTestId("clipboard-button");

    // Simulate hover
    fireEvent.mouseEnter(button);
    expect(button).toHaveStyle("background-color: #E3E3E3");

    fireEvent.mouseLeave(button);
    expect(button).toHaveStyle("background-color: transparent");
  });
});
