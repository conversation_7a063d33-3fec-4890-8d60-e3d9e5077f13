import { render, screen } from "@testing-library/react";
import LatestNews from "@/components/HomePage/LatestNews";
import "@testing-library/jest-dom";

const mockLatestNews = {
  articles: [
    {
      id: "1",
      updated_at: "2025-03-27",
      title: "Mock News 1",
      slug: "mock-news-1",
      description: "Mock description",
      publishedDate: "2025-03-26",
      isLive: false,
      category: {
        id: "cat-1",
        title: "World",
        slug: "world",
      },
      authors: [
        {
          _id: "author-1",
          name: "Author One",
          slug: "author-one",
        },
      ],
    },
    {
      id: "2",
      updated_at: "2025-03-27",
      title: "Mock News 2",
      slug: "mock-news-2",
      description: "Mock description",
      publishedDate: "2025-03-26",
      isLive: false,
      category: {
        id: "cat-1",
        title: "World",
        slug: "world",
      },
      authors: [],
    },
    {
      id: "3",
      updated_at: "2025-03-27",
      title: "Mock News 3",
      slug: "mock-news-3",
      description: "Mock description",
      publishedDate: "2025-03-26",
      isLive: false,
      category: {
        id: "cat-1",
        title: "World",
        slug: "world",
      },
      authors: [],
    },
    {
      id: "4",
      updated_at: "2025-03-27",
      title: "Mock News 4",
      slug: "mock-news-4",
      description: "Mock description",
      publishedDate: "2025-03-26",
      isLive: false,
      category: {
        id: "cat-1",
        title: "World",
        slug: "world",
      },
      authors: [],
    },
    {
      id: "5",
      updated_at: "2025-03-27",
      title: "Mock News 5",
      slug: "mock-news-5",
      description: "Mock description",
      publishedDate: "2025-03-26",
      isLive: false,
      category: {
        id: "cat-1",
        title: "World",
        slug: "world",
      },
      authors: [],
    },
  ],
};

describe("LatestNews Component", () => {
  it("renders correctly", () => {
    render(<LatestNews latestNews={mockLatestNews} />);
    expect(screen.getByText("LATEST NEWS")).toBeInTheDocument();
  });

  it("displays the correct number of news items", () => {
    render(<LatestNews latestNews={mockLatestNews} />);
    const newsItems = screen.getAllByRole("listitem");
    expect(newsItems.length).toBe(5);
  });

  it("renders the 'View All' button", () => {
    render(<LatestNews latestNews={mockLatestNews} />);
    expect(
      screen.getByRole("button", { name: /view all/i }),
    ).toBeInTheDocument();
  });
});
