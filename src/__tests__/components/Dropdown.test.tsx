// Dropdown.test.tsx
import { render, screen, fireEvent } from "@testing-library/react";
import Dropdown, { DropdownProps } from "@/components/ui/Dropdown";

describe("Dropdown Component", () => {
  const mockOptions = [
    { label: "Option 1", value: "1" },
    { label: "Option 2", value: "2" },
    { label: "Option 3", value: "3" },
  ];
  const mockOnOptionSelect = jest.fn();

  const defaultProps: DropdownProps = {
    options: mockOptions,
    onOptionSelect: mockOnOptionSelect,
  };

  it("renders correctly with default props", () => {
    render(<Dropdown {...defaultProps} />);
    expect(screen.getByText("Option 1")).toBeInTheDocument();
  });

  it("opens and closes the dropdown menu", () => {
    render(<Dropdown {...defaultProps} />);
    const button = screen.getByRole("button");

    // Open dropdown
    fireEvent.click(button);
    expect(screen.getByText("Option 2")).toBeVisible();

    // Close dropdown
    fireEvent.click(button);
    expect(screen.queryByText("Option 2")).not.toBeInTheDocument();
  });

  it("selects an option and calls onOptionSelect callback", () => {
    render(<Dropdown {...defaultProps} />);
    fireEvent.click(screen.getByRole("button")); // Open dropdown
    fireEvent.click(screen.getByText("Option 2")); // Select second option

    expect(mockOnOptionSelect).toHaveBeenCalledWith("2");
    expect(screen.getByRole("button")).toHaveTextContent("Option 2");
  });

  it("closes dropdown when clicking outside", () => {
    const { container } = render(<Dropdown {...defaultProps} />);
    fireEvent.click(screen.getByRole("button")); // Open dropdown
    expect(screen.getByText("Option 2")).toBeVisible();

    // Click outside
    fireEvent.mouseDown(container);
    expect(screen.queryByText("Option 2")).not.toBeInTheDocument();
  });

  it("displays the correct default value", () => {
    render(<Dropdown {...defaultProps} defaultValue="3" />);
    expect(screen.getByRole("button")).toHaveTextContent("Option 3");
  });
});
