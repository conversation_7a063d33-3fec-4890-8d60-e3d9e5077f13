"use client";

import { format, isYesterday, differenceInHours, isToday } from "date-fns";

interface TimestampProps {
  date?: string;
  rawDate?: boolean;
}

export default function Timestamp({ date, rawDate = false }: TimestampProps) {
  if (!date) return null;

  const parsedDate = new Date(date);
  const now = new Date();
  const minutesDiff = Math.floor(
    (now.getTime() - parsedDate.getTime()) / (1000 * 60),
  );
  const hoursDiff = differenceInHours(now, parsedDate);

  if (minutesDiff < 1) {
    return "1 min ago";
  }

  if (hoursDiff <= 1 && minutesDiff < 60) {
    return `${minutesDiff} ${minutesDiff > 1 ? "mins" : "min"} ago`;
  }

  if (hoursDiff <= 11) {
    return `${hoursDiff} ${hoursDiff > 1 ? "hours" : "hour"} ago`;
  }

  if (isToday(parsedDate)) {
    return <>{format(parsedDate, "h:mmaaa 'EST'")}</>;
  }

  if (isYesterday(parsedDate)) {
    return <>Yesterday</>;
  }

  if (rawDate) {
    const [year, month, day] = date.split("T")[0].split("-").map(Number);
    const utcDate = new Date(year, month - 1, day);

    return <>{format(utcDate, "MMMM do, yyyy")}</>;
  }

  return <>{format(parsedDate, "MMMM do, yyyy")}</>;
}
