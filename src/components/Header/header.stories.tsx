import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import Header from ".";
import {
  ComplianceLinkQueryResult,
  NavigationMenuQueryResult,
  SocialMediaQueryResult,
  EwtnNetworkLinksQueryResult,
} from "@/sanity/queries/layout";

const navigationMenu: NavigationMenuQueryResult = {
  // mock data for navigationMenu
  title: "Main Menu",
  sections: [
    {
      title: "Section 1",
      slug: {
        _type: "slug",
        current: "section-1",
      },
      menuItems: [
        {
          title: "Item 1",
          linkType: "internal",
          internalLink: {
            _type: "slug",
            current: "item-1",
          },
          externalLink: "",
        },
      ],
    },
  ],
};

const socialMediaItems: SocialMediaQueryResult = [
  {
    _id: "123",
    _type: "socialMedia",
    _createdAt: "2022-01-01T00:00:00Z",
    _updatedAt: "2022-01-01T00:00:00Z",
    _rev: "123",
    link: "https://www.facebook.com/ewtnonline",
    icon: "facebook",
  },
  {
    _id: "456",
    _type: "socialMedia",
    _createdAt: "2022-01-01T00:00:00Z",
    _updatedAt: "2022-01-01T00:00:00Z",
    _rev: "456",
    link: "https://www.instagram.com/ewtnmedia",
    icon: "instagram",
  },
  {
    _id: "789",
    _type: "socialMedia",
    _createdAt: "2022-01-01T00:00:00Z",
    _updatedAt: "2022-01-01T00:00:00Z",
    _rev: "789",
    link: "https://www.youtube.com/user/EWTN",
    icon: "youtube",
  },
];

const complianceLinkItems: ComplianceLinkQueryResult = [
  {
    _id: "1",
    _type: "complianceLink",
    _createdAt: "2022-01-01T00:00:00Z",
    _updatedAt: "2022-01-01T00:00:00Z",
    _rev: "1",
    title: "Privacy Policy",
    slug: {
      _type: "slug",
      current: "privacy-policy",
    },
    isOnReducedFooter: false,
    externalUrl: "",
  },
  {
    _id: "2",
    _type: "complianceLink",
    _createdAt: "2022-01-01T00:00:00Z",
    _updatedAt: "2022-01-01T00:00:00Z",
    _rev: "1",
    title: "Terms And Conditions",
    slug: {
      _type: "slug",
      current: "terms-and-conditions",
    },
    isOnReducedFooter: true,
    externalUrl: "",
  },
];

const ewtnNetworkLinks: EwtnNetworkLinksQueryResult = [
  {
    title: "facebook",
    url: "http://www.facebook.com",
  },
  {
    title: "instagram",
    url: "http://www.instagram.com",
  },
];

export default {
  title: "Components/Header",
  component: Header,
  argTypes: {
    isHeaderReduced: {
      control: {
        type: "boolean",
      },
    },
  },
  parameters: {
    nextjs: { appDirectory: true },
  },
} as Meta;

const Template: StoryFn = (args) => (
  <Header
    navigationMenu={navigationMenu}
    socialMediaItems={socialMediaItems}
    complianceLinkItems={complianceLinkItems}
    ewtnNetworkLinks={ewtnNetworkLinks}
    allShows={[]}
    donationLink={{
      label: "Support EWTN",
      url: "https://donate.ewtn.com",
    }}
    {...args}
  />
);

export const Main = Template.bind({});
Main.args = {};
