export interface DrawerProps {
  children: React.ReactNode;
  open: boolean;
  onClose: () => void;
  side: "left" | "right";
}

const openClassNames = {
  right: "translate-x-0",
  left: "translate-x-0",
};

const closeClassNames = {
  right: "translate-x-full",
  left: "-translate-x-full",
};

const classNames = {
  right: "inset-y-0 right-0",
  left: "inset-y-0 left-0",
};

const Drawer = ({ children, open, side, onClose }: DrawerProps) => {
  return (
    <div
      id={`dialog-${side}`}
      className={`fixed inset-0 z-50 transition-all duration-200 ease-in-out ${open ? "visible opacity-100" : "invisible opacity-0"}`}
      aria-labelledby="slide-over"
      role="dialog"
      aria-modal="true"
      onClick={onClose}
      data-testid="drawer"
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black bg-opacity-70" />

      {/* Drawer container */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className={`fixed max-w-full ${classNames[side]}`}>
            <div
              className={`relative w-full h-full transform transition ease-in-out duration-200 ${open ? openClassNames[side] : closeClassNames[side]}`}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="md:w-auto flex flex-col h-full p-5 bg-white shadow-xl overflow-y-auto hide-scrollbar">
                {children}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Drawer;
