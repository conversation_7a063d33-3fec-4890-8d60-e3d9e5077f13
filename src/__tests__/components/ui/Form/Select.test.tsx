import { render, screen, fireEvent } from "@testing-library/react";
import Select from "@/components/ui/Form/Select";

describe("Select Component", () => {
  const mockOptions = [
    { value: "1", label: "Option 1" },
    { value: "2", label: "Option 2" },
    { value: "3", label: "Option 3" },
  ];

  const defaultProps = {
    label: "Test Label",
    placeholder: "Select an option",
    value: "",
    options: mockOptions,
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders label correctly when provided", () => {
    render(<Select {...defaultProps} />);
    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  it("does not render label when not provided", () => {
    render(<Select {...defaultProps} label="" />);
    expect(screen.queryByText("Test Label")).not.toBeInTheDocument();
  });

  it("renders placeholder option", () => {
    render(<Select {...defaultProps} />);
    expect(screen.getByText("Select an option")).toBeInTheDocument();
  });

  it("renders all options", () => {
    render(<Select {...defaultProps} />);
    mockOptions.forEach((option) => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
    });
  });

  it("calls onChange when selecting an option", () => {
    render(<Select {...defaultProps} />);
    const select = screen.getByRole("combobox");
    fireEvent.change(select, { target: { value: "2" } });
    expect(defaultProps.onChange).toHaveBeenCalledWith("2");
  });

  it("shows error state with string message", () => {
    const errorMessage = "This field is required";
    render(<Select {...defaultProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole("combobox").parentElement).toHaveClass(
      "border-red-700",
      "bg-red-100",
    );
  });

  it("shows error state with boolean error", () => {
    render(<Select {...defaultProps} error={true} />);
    expect(screen.getByRole("combobox").parentElement).toHaveClass(
      "border-red-700",
      "bg-red-100",
    );
  });

  it("applies disabled styles when disabled", () => {
    render(<Select {...defaultProps} disabled />);
    const selectContainer = screen.getByRole("combobox").parentElement;
    expect(selectContainer).toHaveClass("cursor-not-allowed", "opacity-70");
  });

  it("applies correct size classes", () => {
    render(<Select {...defaultProps} size="xs" />);
    expect(screen.getByRole("combobox")).toHaveClass("text-xs");
  });

  it("renders icon component", () => {
    render(<Select {...defaultProps} />);
    expect(screen.getByTestId("open")).toBeInTheDocument();
  });

  it("applies custom className correctly", () => {
    const customClass = "custom-class";
    render(<Select {...defaultProps} className={customClass} />);
    expect(screen.getByRole("combobox")).toHaveClass(customClass);
  });
});
