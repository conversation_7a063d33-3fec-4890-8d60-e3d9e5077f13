import { defineQuery } from "next-sanity";
import { Article } from "../Trending/trending";
import { Media } from "./categoryArticles";
import { workspace } from "@/sanity/env";

export const homePageQuery = defineQuery(`
*[_type == "homePage" && agency == '${workspace}'][0]{
  mainArticles {
    main -> {
      "id": _id,
      title,
      description,
      _updatedAt,
      "slug": slug.current,
      publishedDate,
      "created_at": _createdAt,
      "media": featuredMedia[] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "tags": tags[]-> {
        "id": _id,
        title,
        "slug": slug.current
      },
    },
    secondary[] ->{
      "id": _id,
      title,
      description,
      _updatedAt,
      "slug": slug.current,
      publishedDate,
      "created_at": _createdAt,
      "media": featuredMedia[] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "tags": tags[]-> {
        "id": _id,
        title,
        "slug": slug.current
      }, 
    },
    tertiary[] ->{
      "id": _id,
      title,
      description,
      _updatedAt,
      "slug": slug.current,
      publishedDate,
      "created_at": _createdAt,
      "media": featuredMedia[] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "tags": tags[]-> {
        "id": _id,
        title,
        "slug": slug.current
      },
    }
  },
  rightColumn {
    isSubcategory,
    subcategory -> {
      "id": _id,
      title,
      "slug": slug.current,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      }
    },
    category -> {
      "id": _id,
      title,
      "slug": slug.current,
      description
    }
  },
  sections[] {
    _type,
    // Fetch fields dynamically depending on the type
    _type == "trendingArticlesSection" => {
      title
    },
    _type == "moreNewsSection" => {
      title,
      description
    },
    _type == "thisWeekInPhotosSection" => {
      title,
      description
    },
    _type == "categorySection" => {
      _type,
      "category": category->{
        _id,
        title,
        description,
        "slug": slug.current,
        pinnedArticles[]->{
          "id": _id,
          "created_at": dateTime(_createdAt),
          "updated_at": dateTime(_updatedAt),
          "publishedDate": dateTime(publishedDate),
          "slug": slug.current,
          title,
          truncatedTitle,
          "excerpt": description,
          "media": featuredMedia[] {
            "url": secure_url,
            "type": resource_type,
            "text": context.custom.caption
          },
          "category": category-> {
            "id": _id,
            title,
            "slug": slug.current,
            description
          },
          "subcategory": subcategory-> {
            "id": _id,
            title,
            "slug": slug.current
          },
          "tags": tags[]-> {
            "id": _id,
            title,
            "slug": slug.current
          },
          "authors": authors[]-> {
            "slug": slug.current,
            "name": fullName,
            image {
              "url": secure_url
            }
          },
        },
      }
    },
    _type == "categoryWithSubcategoriesSection" => {
      _type,
      "category": category->{
        "id": _id,
        _id,
        title,
        description,
        "slug": slug.current,
        "subcategories": subcategories[]->{
          _id,
          title,
          "slug": slug.current,
          pinnedArticles[]->{
            "id": _id,
            "created_at": dateTime(_createdAt),
            "updated_at": dateTime(_updatedAt),
            "publishedDate": dateTime(publishedDate),
            "slug": slug.current,
            title,
            truncatedTitle,
            "excerpt": description,
            "media": featuredMedia[] {
              "url": secure_url,
              "type": resource_type,
              "text": context.custom.caption
            },
            "category": category-> {
              "id": _id,
              title,
              "slug": slug.current,
              description
            },
            "subcategory": subcategory-> {
              "id": _id,
              title,
              "slug": slug.current
            },
            "tags": tags[]-> {
              "id": _id,
              title,
              "slug": slug.current
            },
            "authors": authors[]-> {
              "slug": slug.current,
              "name": fullName,
              image {
                "url": secure_url
              }
            },
          },
        }
      }
    },
    _type == "highlightedArticleSection" => {
      _type,
      status,
      title,
      description,
      image {
          "url": secure_url,
          "type": resource_type,
          "text": context.custom.caption
        },
      "article": article->{
        "id": _id,
        title,
        description,
        _updatedAt,
        "slug": slug.current,
        publishedDate,
        "created_at": _createdAt,
        "media": featuredMedia[] {
          "url": secure_url,
          "type": resource_type,
          "text": context.custom.caption
        },
        "subcategory": subcategory-> {
          "id": _id,
          title,
          "slug": slug.current
        },
        "authors": authors[]-> {
          "slug": slug.current,
          "name": fullName,
          image {
            "url": secure_url
          }
        },
        "category": category-> {
          "id": _id,
          title,
          "slug": slug.current,
          description
        },
        "tags": tags[]-> {
          "id": _id,
          title,
          "slug": slug.current
        },
      }
    }
  },
}
`);

export type HomeArticle = {
  id: string;
  title: string;
  description: string;
  _updatedAt: string;
  slug: string;
  publishedDate: string;
  created_at: string;
  media: {
    url: string;
    type: string;
    text: string;
  }[];
  subcategory: {
    id: string;
    title: string;
    slug: string;
  };
  authors: {
    slug: string;
    name: string;
    image: {
      url: string;
    };
  }[];
  category: {
    id: string;
    title: string;
    slug: string;
    description: string;
  };
  tags: {
    id: string;
    title: string;
    slug: string;
  }[];
};

export type SectionArticle = HomeArticle;

export type HomeSection =
  | {
      _type: "categorySection";
      category: {
        _id: string;
        title: string;
        slug: string;
        description: string;
        articles: Article[];
        pinnedArticles: Article[];
      };
    }
  | {
      _type: "categoryWithSubcategoriesSection";
      category: {
        id: string;
        _id: string;
        status: boolean;
        title: string;
        description: string;
        slug: string;
        subcategories: {
          _id: string;
          title: string;
          slug: string;
          articles: Article[];
          pinnedArticles: Article[];
        }[];
      };
    }
  | {
      _type: "highlightedArticleSection";
      status: boolean;
      title: string;
      description: string;
      image: {
        url: string;
        type: string;
        text: string;
      };
      article: SectionArticle;
    }
  | {
      _type: "trendingArticlesSection";
      title: string;
    }
  | {
      _type: "moreNewsSection";
      title: string;
      description: string;
    }
  | {
      _type: "thisWeekInPhotosSection";
      title: string;
      description: string;
      articles: {
        title: string;
        media: Media[];
        category?: { description?: string; title: string; slug: string };
        slug: string;
      }[];
    };

export type HomePageResult = {
  mainArticles: {
    main: HomeArticle;
    secondary: HomeArticle[];
    tertiary: HomeArticle[];
  };
  rightColumn: {
    isSubcategory: boolean;
    subcategory: {
      id: string;
      title: string;
      slug: string;
      category: {
        id: string;
        title: string;
        slug: string;
        description: string;
      };
      articles: Article[];
    };
    category: {
      id: string;
      title: string;
      slug: string;
      description: string;
      articles: Article[];
    };
  };
  sections: HomeSection[];
  trendingArticles: Article[];
};
