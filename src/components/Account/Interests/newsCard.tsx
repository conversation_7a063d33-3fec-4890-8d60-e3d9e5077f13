import AuthorsList from "@/components/Author/AuthorsList";
import { ArticleProps } from "@/types/interests";
import { getArticleUrl, getMediaImage, truncateTitle } from "@/utils/utils";
import Image from "next/image";
import Link from "next/link";

export const NewsCard: React.FC<ArticleProps> = ({ item }) => (
  <div
    className="flex gap-4 border-b border-grey-200 pb-4"
    data-testid="mock-news-card"
  >
    <div className="flex-1">
      <p className="text-[12px] lg:text-[13px] text-gray-600 mb-1">
        <Link
          href={`/${item.category.slug}`}
          className="hover-underline-animation"
        >
          {item.category.title}
        </Link>
      </p>

      <h3 className="font-titles text-[17px] lg:text-[19px] font-bold mb-2 leading-none">
        <Link
          href={getArticleUrl(
            item.category.slug,
            item.slug,
            item.subcategory?.slug,
          )}
          className="hover-underline-animation"
        >
          {truncateTitle(item.title)}
        </Link>
      </h3>

      {item.authors && (
        <AuthorsList
          authors={item.authors}
          publishedDate={item.updated_at}
        ></AuthorsList>
      )}
    </div>

    <div className="w-20 h-20 relative shrink-0">
      {item.media && (
        <Link
          href={getArticleUrl(
            item.category.slug,
            item.slug,
            item.subcategory?.slug,
          )}
          className="hover:opacity-85 transition-all"
        >
          <Image
            src={getMediaImage(item.media)}
            alt={item.title}
            fill
            className="w-full h-full object-cover rounded"
          />
        </Link>
      )}
    </div>
  </div>
);

export default NewsCard;
