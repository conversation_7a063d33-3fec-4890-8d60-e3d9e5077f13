import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import SavedArticlesClient from "@/app/account/saved-articles/SavedArticlesClient";
import "@testing-library/jest-dom";

const mockArticles = Array.from({ length: 14 }).map((_, i) => ({
  id: `${i + 1}`,
  title: `Mock Article ${i + 1}`,
  slug: `mock-article-${i + 1}`,
  updated_at: "2025-04-01T00:00:00Z",
  media: {
    type: "image",
    url: `https://example.com/image-${i + 1}.jpg`,
  },
  category: {
    id: "cat1",
    title: "World",
    slug: "world",
    description: "World news",
  },
  subcategory: {
    id: "sub1",
    title: "Europe",
    slug: "europe",
  },
  authors: [
    {
      _id: "auth1",
      name: "<PERSON>",
      slug: "john-doe",
    },
  ],
}));

jest.mock("@/components/Account/Sidebar/UserProfile", () => {
  const MockUserProfile = () => (
    <div>
      <span>User Profile</span>
    </div>
  );
  MockUserProfile.displayName = "MockUserProfile";
  return MockUserProfile;
});

beforeEach(() => {
  global.fetch = jest.fn((url: RequestInfo): Promise<Response> => {
    if (typeof url === "string" && url.includes("/api/articles/bookmarks")) {
      return Promise.resolve({
        ok: true,
        json: async () => ({
          data: {
            items: mockArticles.slice(0, 10),
            totalCount: mockArticles.length,
          },
        }),
      } as Response);
    }

    return Promise.resolve({ ok: true } as Response);
  }) as jest.Mock;
});

afterEach(() => {
  jest.resetAllMocks();
});

describe("SavedArticlesClientWrapper", () => {
  it("renders articles and layout correctly", async () => {
    render(<SavedArticlesClient />);
    expect(await screen.findByText("Mock Article 1")).toBeInTheDocument();
    {
      /* Temporarily hides saved articles count */
    }
    // expect(screen.getByText("14 saved articles")).toBeInTheDocument();
  });

  it("loads more articles when clicking Load More", async () => {
    render(<SavedArticlesClient />);

    // Ensure the first items render
    await screen.findByText("Mock Article 1");

    // 🔁 Click Load More to trigger pagination
    fireEvent.click(screen.getByText("Load More"));

    // ✅ Wait for the article that appears after loading more
    await waitFor(() => {
      expect(screen.getByText("Mock Article 10")).toBeInTheDocument();
    });
  });

  it("shows empty state", async () => {
    (fetch as jest.Mock).mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () =>
          Promise.resolve({
            data: {
              items: [],
              totalCount: 0,
            },
          }),
      }),
    );

    render(<SavedArticlesClient />);
    await screen.findByText("You don’t have any saved articles yet");
    {
      /* Temporarily hides saved articles count */
    }
    // expect(screen.getByText("0 saved articles")).toBeInTheDocument();
  });
});
