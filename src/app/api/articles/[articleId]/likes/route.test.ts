import { NextRequest } from "next/server";
import { auth0 } from "@/lib/auth0";
import {
  addLike,
  getLikeCount,
  getUserLike,
  removeLike,
} from "@/app/api/services/articles/likes";
import {
  unauthorizedResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { POST, GET, DELETE } from "./route";

// Mock dependencies
jest.mock("@/lib/auth0", () => ({
  auth0: {
    getSession: jest.fn(),
  },
}));

jest.mock("@/app/api/services/articles/likes", () => ({
  addLike: jest.fn(),
  getLikeCount: jest.fn(),
  getUserLike: jest.fn(),
  removeLike: jest.fn(),
}));

jest.mock("@/app/api/util/errors", () => ({
  unauthorizedResponse: jest.fn(() => ({
    status: 401,
    message: "Unauthorized",
  })),
  badRequestResponse: jest.fn((message: string) => ({ status: 400, message })),
  successResponse: jest.fn((message: string, status: number) => ({
    status,
    message,
  })),
  errorResponse: jest.fn((error) => ({
    status: 500,
    message: error.message,
  })),
  successResponseWithData: jest.fn((data, status: number) => ({
    status,
    data,
  })),
}));

describe("Like API Routes", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /api/likes", () => {
    it("should return unauthorized if no user session", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce(null);

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(unauthorizedResponse).toHaveBeenCalled();
      expect(response).toEqual({ status: 401, message: "Unauthorized" });
    });

    it("should add a like and return success response", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(addLike).toHaveBeenCalledWith("user123", "123");
      expect(successResponse).toHaveBeenCalledWith("Article liked", 201);
      expect(response).toEqual({ status: 201, message: "Article liked" });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (addLike as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to add like"),
      );

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to add like"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to add like",
      });
    });
  });

  describe("GET /api/likes", () => {
    it("should return like count and user like status", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (getLikeCount as jest.Mock).mockResolvedValueOnce({
        like_count: 10,
      });
      (getUserLike as jest.Mock).mockResolvedValueOnce(true);

      const response = await GET({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(getLikeCount).toHaveBeenCalledWith("123");
      expect(getUserLike).toHaveBeenCalledWith("user123", "123");
      expect(successResponseWithData).toHaveBeenCalledWith(
        { likeCount: 10, userLiked: true },
        200,
      );
      expect(response).toEqual({
        status: 200,
        data: { likeCount: 10, userLiked: true },
      });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (getLikeCount as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to get like count"),
      );

      const response = await GET({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to get like count"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to get like count",
      });
    });
  });

  describe("DELETE /api/likes", () => {
    it("should return unauthorized if no user session", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce(null);

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(unauthorizedResponse).toHaveBeenCalled();
      expect(response).toEqual({ status: 401, message: "Unauthorized" });
    });

    it("should remove a like and return success response", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(removeLike).toHaveBeenCalledWith("user123", "123");
      expect(successResponse).toHaveBeenCalledWith("Article like removed", 200);
      expect(response).toEqual({
        status: 200,
        message: "Article like removed",
      });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (removeLike as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to remove like"),
      );

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to remove like"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to remove like",
      });
    });
  });
});
