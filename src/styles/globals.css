@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  overflow-x: hidden;
}

:root {
  --background: white;
  --foreground: black;
}

body {
  color: black;
  background: white;
  font-family: Roboto, Arial, Helvetica, sans-serif;
}

@layer utilities {
  .hide-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }
}

.material-symbols-rounded {
  font-variation-settings:
    "FILL" 0,
    "wght" 400,
    "GRAD" 0,
    "opsz" 24;
}

.news-swiper .swiper-wrapper {
  @apply pb-12;
}

.news-swiper .swiper-slide {
  @apply w-[85%];
}

.news-swiper .swiper-button-next,
.news-swiper .swiper-button-prev {
  @apply border border-grey-300 w-8 h-8 bg-white rounded-full bottom-0 top-auto left-0;
}

.news-swiper .swiper-button-next::after,
.news-swiper .swiper-button-prev::after {
  font-family: Material Symbols Rounded;
  font-weight: normal;
  font-style: normal;
  font-size: 20px !important;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
  content: "arrow_back";
}

.news-swiper .swiper-button-next {
  @apply left-10;
}

.news-swiper .swiper-button-next::after {
  content: "arrow_forward";
}

.news-swiper .swiper-button-next:hover,
.news-swiper .swiper-button-prev:hover {
  @apply bg-white border-black transition-all;
}

.news-swiper .swiper-button-next::after,
.news-swiper .swiper-button-prev::after {
  @apply text-[16px] text-black;
}

.news-swiper .swiper-button-disabled {
  @apply !opacity-50;
}

.dailyStory-swiper .swiper-button-next,
.dailyStory-swiper .swiper-button-prev {
  @apply border border-grey-300 w-8 h-8 rounded-full bottom-0 top-auto left-4 sm:left-0;
}

.swiper-fullscreen .dailyStory-swiper .swiper-button-next,
.swiper-fullscreen .dailyStory-swiper .swiper-button-prev {
  @apply bottom-0 left-auto right-4 lg:right-auto lg:top-1/2 lg:left-4 text-white bg-transparent transition-all;
}

.swiper-fullscreen .dailyStory-swiper .swiper-button-next:after,
.swiper-fullscreen .dailyStory-swiper .swiper-button-prev:after {
  @apply text-white transition-all;
}

.swiper-fullscreen .dailyStory-swiper .swiper-button-next:hover:after,
.swiper-fullscreen .dailyStory-swiper .swiper-button-prev:hover:after {
  @apply !text-black;
}

.dailyStory-swiper .swiper-button-lock {
  @apply !flex;
}

.swiper-fullscreen .dailyStory-swiper .swiper-button-prev {
  @apply right-16;
}

.swiper-fullscreen .dailyStory-swiper .swiper-button-next {
  @apply lg:left-auto lg:right-4;
}

.dailyStory-swiper .swiper-button-next::after,
.dailyStory-swiper .swiper-button-prev::after {
  font-family: Material Symbols Rounded;
  font-weight: normal;
  font-style: normal;
  font-size: 20px !important;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
  content: "arrow_back";
}

.dailyStory-swiper .swiper-button-next::after,
.dailyStory-swiper .swiper-button-prev::after {
  @apply text-[16px] text-black;
}

.dailyStory-swiper .swiper-button-next:hover,
.dailyStory-swiper .swiper-button-prev:hover {
  @apply bg-white border-black transition-all;
}

.dailyStory-swiper .swiper-button-next {
  @apply left-24 sm:left-20;
}

.dailyStory-swiper .swiper-button-next::after {
  content: "arrow_forward";
}

.dailyStory-swiper .swiper-button-disabled {
  @apply !opacity-50;
}

.authors-swiper {
  @apply !static;
}

.authors-swiper .swiper-wrapper {
  @apply pb-12;
}

.authors-swiper .swiper-slide {
  @apply h-auto;
}

.authors-swiper .swiper-button-next,
.authors-swiper .swiper-button-prev {
  @apply border border-grey-300 w-8 h-8 bg-white rounded-full bottom-0 top-auto left-1/2;
}

.authors-swiper .swiper-button-next::after,
.authors-swiper .swiper-button-prev::after {
  font-family: Material Symbols Rounded;
  font-weight: normal;
  font-style: normal;
  font-size: 20px !important;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
  content: "arrow_back";
}

.authors-swiper .swiper-button-prev {
  @apply -ml-10;
}

.authors-swiper .swiper-button-next {
  @apply ml-2;
}

.authors-swiper .swiper-button-next::after {
  content: "arrow_forward";
}

.authors-swiper .swiper-button-next:hover,
.authors-swiper .swiper-button-prev:hover {
  @apply bg-white border-black transition-all;
}

.authors-swiper .swiper-button-next::after,
.authors-swiper .swiper-button-prev::after {
  @apply text-[16px] text-black;
}

.authors-swiper .swiper-button-disabled {
  @apply !opacity-50;
}

.hide-scroll {
  -ms-overflow-style: none;
  /* IE y Edge */
  scrollbar-width: none;
  /* Firefox */
}

.hide-scroll::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari y Opera */
}

/* Change Swiper Pagination Dots to White */
.swiper-pagination-bullet {
  background-color: white !important;
}

/* Change the active dot color */
.swiper-pagination-bullet-active {
  background-color: white !important;
}

.hover-underline-animation,
.hover-underline-animation-white {
  @applybg-gradient-to-r bg-[length:0%_1px] bg-[0_95%] bg-no-repeat transition-all duration-300 hover:bg-[length:100%_1px] box-decoration-clone cursor-pointer;
}

.hover-underline-animation-white {
  @apply from-white to-white;
}

.hover-underline-animation-white-child .hover-underline-animation-child-item,
.hover-underline-animation-red-child .hover-underline-animation-child-item,
.hover-underline-animation-child .hover-underline-animation-child-item {
  @apply bg-gradient-to-r 
  bg-[length:0%_1px]
  bg-[0_95%] bg-no-repeat
  transition-all duration-300
  box-decoration-clone
  cursor-pointer;
}
.hover-underline-animation-white-child:hover
  .hover-underline-animation-child-item,
.hover-underline-animation-red-child:hover
  .hover-underline-animation-child-item,
.hover-underline-animation-child:hover .hover-underline-animation-child-item {
  @apply bg-[length:100%_1px];
}
.hover-underline-animation,
.hover-underline-animation-white,
.hover-underline-animation-red {
  @apply bg-gradient-to-r 
  bg-[length:0%_1px]
  bg-[0_95%] bg-no-repeat
  transition-all duration-300
  hover:bg-[length:100%_1px]
  box-decoration-clone
  cursor-pointer;
}
.hover-underline-animation-red-child:hover
  .hover-underline-animation-child-item,
.hover-underline-animation-red {
  @apply from-red-700 to-red-700;
}
.hover-underline-animation-white-child:hover
  .hover-underline-animation-child-item,
.hover-underline-animation-white {
  @apply from-white to-white;
}
.hover-underline-animation-child:hover .hover-underline-animation-child-item,
.hover-underline-animation {
  @apply from-black to-black;
}

.react-datepicker-wrapper {
  width: 100%;
}
.react-datepicker__triangle {
  display: none;
}
.react-datepicker {
  @apply !border-grey-200 !rounded-xl !overflow-hidden px-4 pt-2;
}
.react-datepicker__header {
  @apply !bg-white !border-0;
}
.react-datepicker__current-month {
  @apply !font-normal !leading-5;
}
.react-datepicker__day--today {
  @apply !rounded-full !bg-grey-200;
}
.react-datepicker__day--selected {
  @apply !rounded-full !bg-red-700;
}
.react-datepicker__navigation--previous {
  @apply !left-[18px] !top-[10px];
}
.react-datepicker__navigation--next {
  @apply !right-[18px] !top-[10px];
}
.react-datepicker__day--outside-month {
  @apply !text-grey-300;
}

.rdrDay.rdrDaySelected .rdrDayNumber span,
.rdrDay.rdrDayStartOfRange .rdrDayNumber span,
.rdrDay.rdrDayEndOfRange .rdrDayNumber span,
.rdrDay .rdrInRange .rdrDayNumber span {
  color: white !important;
}

.rdrDay .rdrInRange {
  background-color: #dc2626 !important;
}

.rdrDay .rdrStartEdge,
.rdrDay .rdrEndEdge {
  background-color: #dc2626 !important;
  border-radius: 0 !important;
  width: 100% !important;
  height: 100% !important;
  top: 0;
  left: 0;
  position: absolute !important;
  z-index: 1;
}

.rdrDayNumber {
  position: relative;
  z-index: 2;
}

.rdrDay .rdrStartEdge {
  border-top-left-radius: 9999px !important;
  border-bottom-left-radius: 9999px !important;
}
.rdrDay .rdrEndEdge {
  border-top-right-radius: 9999px !important;
  border-bottom-right-radius: 9999px !important;
}

.rdrDay {
  position: relative;
}

.rdrDay .rdrInRange {
  background-color: #dc2626 !important;
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  z-index: 1;
  border-radius: 0 !important;
}

#iubenda-iframe {
  z-index: 100 !important;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
