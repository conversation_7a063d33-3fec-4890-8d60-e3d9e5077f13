// types/react-date-range.d.ts
declare module "react-date-range" {
  import * as React from "react";
  import { Locale } from "date-fns";

  export interface Range {
    startDate: Date | undefined;
    endDate: Date | undefined;
    key: string;
  }

  export interface RangeKeyDict {
    [key: string]: Range;
  }

  export interface DateRangeProps {
    ranges: Range[];
    onChange: (item: RangeKeyDict) => void;
    moveRangeOnFirstSelection?: boolean;
    editableDateInputs?: boolean;
    locale?: Locale;
  }

  export class DateRange extends React.Component<DateRangeProps> {}
}
