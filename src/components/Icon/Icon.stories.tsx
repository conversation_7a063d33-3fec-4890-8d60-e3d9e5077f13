import React from "react";
import { Icon, ICONS, IconName } from "./";

export default {
  title: "Components/Icon",
  component: Icon,
};

export const AllIcons = () => {
  return (
    <div
      style={{
        backgroundColor: "black",
        padding: "16px",
        color: "white",
      }}
    >
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(120px, 1fr))",
          gap: "16px",
        }}
      >
        {Object.keys(ICONS).map((iconName) => (
          <div
            key={iconName}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Icon icon={iconName as IconName} className="text-4xl text-white" />
            <span
              style={{
                marginTop: "8px",
                fontSize: "14px",
                textAlign: "center",
                color: "white",
              }}
            >
              {iconName}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
