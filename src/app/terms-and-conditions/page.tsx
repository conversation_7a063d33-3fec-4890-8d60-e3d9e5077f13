import { Metadata, NextPage } from "next";
import { sanityFetch } from "@/sanity/client";
import { TermsAndConditionsQueryResult } from "@/sanity/queries/terms-and-conditions";
import RichContent, { Content } from "@/components/ui/RichContent";
import { termsAndConditionsQuery } from "@/sanity/queries/terms-and-conditions";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";
import { Key } from "react";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata(
    "/terms-and-conditions",
    "Terms and Conditions",
    "Terms and Conditions Page",
  );
}

const TermsAndConditionsPage: NextPage = async () => {
  try {
    const termsData = await sanityFetch<TermsAndConditionsQueryResult>({
      query: termsAndConditionsQuery,
    });

    if (!termsData) {
      throw new Error("No terms and conditions found");
    }

    const columns = termsData.layout === "double" ? 2 : 1;
    const rowsPerColumn = Math.ceil(
      termsData.content.textBody.length / columns,
    );

    const splitContents = Array.from({ length: columns }, (_, columnIndex) =>
      termsData.content.textBody.slice(
        columnIndex * rowsPerColumn,
        (columnIndex + 1) * rowsPerColumn,
      ),
    );

    const jsonLd = buildWebPageJsonLd(
      "Terms and Conditions",
      "/terms-and-conditions",
    );
    return (
      <>
        <JsonLD jsonLd={jsonLd} />
        <div
          className="min-h-screen bg-white"
          data-testid="terms-and-conditions"
        >
          <main className="container mx-auto px-4 py-8">
            <h1 className="mb-4 lg:mb-8 lg:pt-4 font-semibold font-titles text-[32px] lg:text-[52px]">
              {termsData.title}
            </h1>

            <div
              className={`grid gap-10 ${columns === 2 ? "grid-cols-1 md:grid-cols-2" : "grid-cols-1"}`}
            >
              {splitContents.map((columnContent, index) => (
                <div key={index} className="terms-column">
                  {columnContent.map(
                    (
                      contentGroup: { textBody: Content[] },
                      idx: Key | null | undefined,
                    ) => (
                      <div className="pb-6" key={idx}>
                        <RichContent content={contentGroup.textBody} />
                      </div>
                    ),
                  )}
                </div>
              ))}
            </div>
          </main>
        </div>
      </>
    );
  } catch (error) {
    console.error("Error in fetching terms and conditions:", error);
    return (
      <div className="min-h-screen flex items-center justify-center">
        <h1>Failed to load terms and conditions</h1>
      </div>
    );
  }
};

export default TermsAndConditionsPage;
