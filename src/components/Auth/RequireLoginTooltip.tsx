import { useRef } from "react";
import { usePathname } from "next/navigation";
import { useOnClickOutside } from "usehooks-ts";

export default function RequireLoginTooltip({
  show,
  centerOnMobile = false,
  alignToRight = false,
  alignToTop = false,
  onHideNeedLogin,
  label = "Sign in to save for later",
}: {
  show: boolean;
  centerOnMobile?: boolean;
  alignToRight?: boolean;
  alignToTop?: boolean;
  onHideNeedLogin: (show: boolean) => void;
  label?: string;
}) {
  const needLoginRef = useRef(null);
  useOnClickOutside(needLoginRef, () => {
    if (show) onHideNeedLogin(false);
  });

  const pathname = usePathname();

  return (
    <>
      {show && (
        <>
          <div className="fixed left-0 right-0 top-0 bottom-0 z-[10000] tooltip-overlay"></div>

          <div
            ref={needLoginRef}
            className={`
    p-4 bg-black text-white z-[10001] tooltip rounded-xl whitespace-nowrap absolute w-[205px] ${alignToTop ? "bottom-full mb-2" : "top-full mt-2"}
              ${
                centerOnMobile
                  ? "left-1/2 lg:left-0 flex justify-center -ml-[102.5px] lg:ml-0 lg:block before:left-1/2 before:lg:left-4"
                  : alignToRight
                    ? "right-0 flex justify-center before:right-4"
                    : "before:left-4"
              }
              ${
                alignToTop
                  ? "before:absolute before:-ml-[0.5rem] before:-bottom-[7px] before:content-[' '] before:border-t-[0.5rem] before:border-transparent before:border-t-black before:border-l-[0.5rem] before:border-r-[0.5rem]"
                  : "before:absolute before:-ml-[0.5rem] before:-top-[7px] before:content-[' '] before:border-b-[0.5rem] before:border-transparent before:border-b-black before:border-l-[0.5rem] before:border-r-[0.5rem]"
              }
            `}
          >
            <div>
              <div className="text-center text-[16px] font-semibold">
                {label}
              </div>

              <div className="flex gap-2 items-center mt-2">
                <a
                  className="px-4 pt-[10px] pb-[11px] rounded-[16px] text-[16px] transition-all transform flex items-center justify-center bg-red-700 text-white active:bg-black cursor-pointer font-normal hover:bg-red-700 hover:underline"
                  href={`/auth/login?screen_hint=signup&returnTo=${pathname}`}
                  data-testid="register"
                >
                  Register
                </a>
                or
                <a
                  className="border-0 pl-0 underline"
                  href={`/auth/login?returnTo=${pathname}`}
                  data-testid="signin"
                >
                  Sign In
                </a>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
}
