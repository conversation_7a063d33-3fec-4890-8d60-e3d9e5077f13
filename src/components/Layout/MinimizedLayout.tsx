import Header from "@/components/Header";
import {
  NavigationMenuQueryResult,
  SocialMediaQueryResult,
  ComplianceLinkQueryResult,
  DonationLinkQueryResult,
  EwtnNetworkLinksQueryResult,
} from "@/sanity/queries/layout";
import Footer from "../Footer";

interface DefaultLayoutProps {
  navigationMenu: NavigationMenuQueryResult;
  socialMediaItems: SocialMediaQueryResult;
  complianceLinkItems: ComplianceLinkQueryResult;
  donationLink: DonationLinkQueryResult;
  children: React.ReactNode;
  isHeaderReduced?: boolean;
  ewtnNetworkLinks: EwtnNetworkLinksQueryResult;
}

export default function MinimizedLayout({
  navigationMenu,
  socialMediaItems,
  complianceLinkItems,
  donationLink,
  children,
  isHeaderReduced = false,
  ewtnNetworkLinks,
}: DefaultLayoutProps) {
  return (
    <>
      <Header
        navigationMenu={navigationMenu}
        socialMediaItems={socialMediaItems}
        complianceLinkItems={complianceLinkItems}
        donationLink={donationLink}
        isHeaderReduced={isHeaderReduced}
        ewtnNetworkLinks={ewtnNetworkLinks}
        allShows={[]}
      />
      <main>{children}</main>
      <Footer
        navigationMenu={navigationMenu}
        socialMediaItems={socialMediaItems}
        complianceLinkItems={complianceLinkItems}
        donationLink={donationLink}
        isFooterReduced={true}
      />
    </>
  );
}
