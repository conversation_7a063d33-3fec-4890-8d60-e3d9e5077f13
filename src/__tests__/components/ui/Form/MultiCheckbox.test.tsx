import { render, screen, fireEvent } from "@testing-library/react";
import MultiCheckbox from "@/components/ui/Form/MultiCheckbox";

const mockOptions = [
  { id: 1, name: "Option 1" },
  { id: 2, name: "Option 2" },
  { id: 3, name: "Option 3" },
];

const defaultProps = {
  options: mockOptions,
  selectedValues: [],
  onChange: jest.fn(),
};

describe("MultiCheckbox", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all options correctly", () => {
    render(<MultiCheckbox {...defaultProps} />);

    mockOptions.forEach((option) => {
      expect(screen.getByLabelText(option.name)).toBeInTheDocument();
    });
  });

  it("shows correct initial selected state", () => {
    render(<MultiCheckbox {...defaultProps} selectedValues={[1, 2]} />);

    const checkbox1 = screen.getByLabelText("Option 1") as HTMLInputElement;
    const checkbox2 = screen.getByLabelText("Option 2") as HTMLInputElement;
    const checkbox3 = screen.getByLabelText("Option 3") as HTMLInputElement;

    expect(checkbox1.checked).toBe(true);
    expect(checkbox2.checked).toBe(true);
    expect(checkbox3.checked).toBe(false);
  });

  it("calls onChange with correct id when clicking an option", () => {
    render(<MultiCheckbox {...defaultProps} />);

    fireEvent.click(screen.getByLabelText("Option 1"));

    expect(defaultProps.onChange).toHaveBeenCalledWith(1);
  });

  it("hides checkbox input visually", () => {
    render(<MultiCheckbox {...defaultProps} />);

    const checkboxInputs = screen.getAllByRole("checkbox");
    checkboxInputs.forEach((input) => {
      expect(input).toHaveClass("hidden");
    });
  });

  it("maintains accessibility despite hidden checkbox", () => {
    render(<MultiCheckbox {...defaultProps} />);

    const checkboxes = screen.getAllByRole("checkbox");
    expect(checkboxes).toHaveLength(mockOptions.length);

    checkboxes.forEach((checkbox, index) => {
      expect(checkbox).toHaveAccessibleName(mockOptions[index].name);
    });
  });

  it("handles empty options array", () => {
    render(<MultiCheckbox {...defaultProps} options={[]} />);

    expect(screen.queryByRole("checkbox")).not.toBeInTheDocument();
  });
});
