import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const identifier = searchParams.get("identifier");

  if (!identifier) {
    return NextResponse.json(
      { message: "Missing identifier" },
      { status: 400 },
    );
  }

  const apiKey = process.env.NEXT_PUBLIC_DISQUS_API_KEY;
  const forum = process.env.NEXT_PUBLIC_DISQUS_FORUM;

  const apiUrl = `https://disqus.com/api/3.0/threads/details.json?api_key=${apiKey}&forum=${forum}&thread:ident=${identifier}`;

  const response = await fetch(apiUrl);
  if (!response.ok) {
    return NextResponse.json(
      { message: "Failed to fetch Disqus data" },
      { status: response.status },
    );
  }

  const json = await response.json();

  return NextResponse.json({
    commentCount: json?.response?.posts ?? 0,
  });
}
