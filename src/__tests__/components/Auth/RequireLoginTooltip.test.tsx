import { render, screen, fireEvent } from "@testing-library/react";
import RequireLoginTooltip from "@/components/Auth/RequireLoginTooltip";
import { usePathname } from "next/navigation";

jest.mock("next/navigation", () => ({
  usePathname: jest.fn(),
}));

describe("RequireLoginTooltip", () => {
  const mockOnHideNeedLogin = jest.fn();
  const mockPathname = "/test-path";

  beforeEach(() => {
    (usePathname as jest.Mock).mockReturnValue(mockPathname);
    mockOnHideNeedLogin.mockClear();
  });

  it("renders nothing when show is false", () => {
    render(
      <RequireLoginTooltip
        show={false}
        onHideNeedLogin={mockOnHideNeedLogin}
      />,
    );

    expect(
      screen.queryByText("Sign in to save for later"),
    ).not.toBeInTheDocument();
  });

  it("renders tooltip content when show is true", () => {
    render(
      <RequireLoginTooltip show={true} onHideNeedLogin={mockOnHideNeedLogin} />,
    );

    expect(screen.getByText("Sign in to save for later")).toBeInTheDocument();
    expect(screen.getByText("Register")).toBeInTheDocument();
    expect(screen.getByText("Sign In")).toBeInTheDocument();
  });

  it("calls onHideNeedLogin when clicking outside", () => {
    render(
      <RequireLoginTooltip show={true} onHideNeedLogin={mockOnHideNeedLogin} />,
    );

    fireEvent.mouseDown(document.body);

    expect(mockOnHideNeedLogin).toHaveBeenCalledWith(false);
  });

  it("does not call onHideNeedLogin when clicking inside the tooltip", () => {
    render(
      <RequireLoginTooltip show={true} onHideNeedLogin={mockOnHideNeedLogin} />,
    );

    const tooltip = screen.getByText("Sign in to save for later");
    fireEvent.mouseDown(tooltip);

    expect(mockOnHideNeedLogin).not.toHaveBeenCalled();
  });

  it("renders overlay when tooltip is shown", () => {
    render(
      <RequireLoginTooltip show={true} onHideNeedLogin={mockOnHideNeedLogin} />,
    );

    const overlay = document.querySelector(
      ".fixed.left-0.right-0.top-0.bottom-0",
    );
    expect(overlay).toBeInTheDocument();
  });

  it("maintains correct z-index layering", () => {
    render(
      <RequireLoginTooltip show={true} onHideNeedLogin={mockOnHideNeedLogin} />,
    );

    const overlay = document.querySelector(".tooltip-overlay");
    const tooltip = document.querySelector(".tooltip");

    expect(overlay).toBeInTheDocument();
    expect(tooltip).toBeInTheDocument();
  });
});
