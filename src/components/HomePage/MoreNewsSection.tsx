import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import Link from "next/link";
import { Icon } from "@/components/Icon";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";
import { Article } from "@/sanity/queries/Trending/trending";
import AuthorsList from "../Author/AuthorsList";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";

interface MoreNewsSectionProps {
  articles: Article[];
}

interface ArticleCardProps {
  article: Article;
  className?: string;
}

export function LargerArticleCard({
  article,
  className = "",
}: ArticleCardProps) {
  return (
    <article className={className}>
      <div className="pb-4 mb-4 border-b border-b-grey-200 sm:flex sm:gap-8 sm:items-start">
        {article.media?.length && (
          <div className="w-full flex-shrink-0 aspect-[16/9] lg:aspect-[16/12] xl:aspect-[16/9] relative sm:w-1/2 md:order-2 md:w-1/3">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="transition-opacity hover:opacity-85"
            >
              <Image
                src={getMediaImage(article.media[0])}
                alt={article.title}
                fill
                className="object-cover rounded-2xl w-full h-full"
              />

              <div className="absolute bottom-2 left-2 flex gap-2">
                {article.media.length > 1 && (
                  <div className="w-9 h-9 rounded-2xl bg-black/70 flex items-center justify-center z-10">
                    <Icon icon="gallery" color="white" className="w-5" />
                  </div>
                )}

                {article.media?.find((media) => media.type == "video") && (
                  <div className="w-9 h-9 rounded-2xl bg-black/70 flex items-center justify-center z-10">
                    <Icon icon="play" className="w-3  " />
                  </div>
                )}
              </div>
            </Link>
          </div>
        )}

        <div className="sm:w-1/2 md:w-2/3">
          <p className="uppercase text-[13px] mt-4 mb-2 sm:mt-0">
            <Link
              href={`/${article.category.slug}`}
              className="hover-underline-animation"
            >
              {article.category.title}
            </Link>
          </p>

          <h2 className="font-semibold font-titles text-[24px] leading-[28px] lg:text-[28px] lg:leading-[36px]">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="hover-underline-animation"
            >
              {truncateTitle(article.title)}
            </Link>
          </h2>

          <p className="hidden md:block mt-5 text-16px leading-[24px]">
            {truncateDescription(article.excerpt)}
          </p>

          <div className="text-[12px] mt-4">
            {article.authors && (
              <AuthorsList
                authors={article.authors}
                publishedDate={article.publishedDate ?? article.created_at}
              ></AuthorsList>
            )}
          </div>
        </div>
      </div>
    </article>
  );
}
export function SmallerArticleCard({ article }: ArticleCardProps) {
  return (
    <article className="pr-4 mr-4 border-r border-grey-200 md:border-r-0 md:border-b md:pb-4 md:pr-0 md:mr-0">
      <p className="uppercase text-[11px] text-gray-500 mb-1">
        <Link
          href={`/${article.category.slug}`}
          className="hover-underline-animation"
        >
          {article.category.title}
        </Link>
      </p>

      <div className="flex items-start gap-4">
        <div className="flex-1">
          <h3 className="font-semibold text-[20px] leading-snug mb-0">
            <Link
              href={getArticleUrl(
                article.category?.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="hover-underline-animation font-titles"
            >
              {truncateTitle(article.title)}
            </Link>
          </h3>
        </div>

        {article.media?.length > 0 && (
          <div className="w-[86px] h-[86px] flex-shrink-0 relative mt-[2px]">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="block w-full h-full"
            >
              <Image
                src={getMediaImage(article.media[0])}
                alt={article.title}
                fill
                className="object-cover rounded-md"
              />
            </Link>
          </div>
        )}
      </div>
      <p className="text-[13px] text-gray-700 line-clamp-3 mt-2">
        {truncateDescription(article.excerpt)}
      </p>
    </article>
  );
}

export default function MoreNewsSection({ articles }: MoreNewsSectionProps) {
  const mainArticles = articles.slice(0, Math.min(4, articles.length));
  const secondaryArticles = articles.length > 4 ? articles.slice(4, 8) : [];
  return (
    <section className="overflow-hidden xl:pb-4">
      <div className="container mx-auto px-4">
        <div className="border-t py-4 xl:pt-6">
          <header className="mb-8 relative">
            <div className="flex gap-4 items-center mb-2">
              <h2 className="text-[18px] lg:text-[20px] font-bold uppercase">
                More News
              </h2>
            </div>

            <p className="text-[16px] lg:text-[18px] mb-3 xl:mb-6">
              A Deeper Look at the Top Stories
            </p>
          </header>

          <div className="md:grid md:grid-cols-12 md:gap-8">
            <div className="md:col-span-9">
              {mainArticles.map((article) => {
                return <LargerArticleCard key={article.id} article={article} />;
              })}
            </div>
            <div className="hidden md:block md:col-span-3">
              <div className="flex flex-col gap-6">
                {secondaryArticles.map((article) => (
                  <SmallerArticleCard
                    key={article.id}
                    article={article}
                    className="flex flex-col border-b border-b-grey-200 pb-4"
                  ></SmallerArticleCard>
                ))}
              </div>
            </div>
          </div>
          <div className="relative mt-4 md:hidden">
            <div className="-mx-4 pl-4 sm:-mr-40">
              <Swiper
                modules={[Navigation]}
                navigation
                loop={true}
                slidesPerView={"auto"}
                centeredSlides={false}
                className="relative news-swiper"
              >
                {secondaryArticles.map((article) => (
                  <SwiperSlide
                    key={article.id}
                    className="!w-[90%] sm:!w-[40.6%] md:!w-[42%]"
                  >
                    <div>
                      <SmallerArticleCard article={article} />
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
