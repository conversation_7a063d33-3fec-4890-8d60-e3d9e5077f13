"use client";

import {
  WithContext,
  WebSite,
  NewsArticle,
  WebPage,
  Person,
  VideoObject,
} from "schema-dts";

export default function JsonLD({
  jsonLd,
}: {
  jsonLd: WithContext<WebSite | WebPage | NewsArticle | VideoObject | Person>;
}) {
  return (
    <script
      id="json-ld"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(jsonLd).replace(/</g, "\\u003c"),
      }}
    />
  );
}
