import FaithCard from "@/components/OurFaith/FaithCard";
import PageHeader from "@/components/Layout/PageHeader";
import {
  OurFaithPageType,
  ourFaithPageQuery,
  FaithCardDisplay,
} from "@/sanity/queries/OutFaithPage/ourFaith";
import { sanityFetch } from "@/sanity/client";
import { Metadata } from "next";

interface OurFaithPageData {
  title: string;
  description: string;
  cards: FaithCardDisplay[];
  error: boolean;
}

const defaultPageInfo = {
  title: "Our Faith",
  description:
    "Discover the prayers, readings, saints, and spiritual tools that help Catholics live and grow in their faith every day.",
};

export async function generateMetadata(): Promise<Metadata> {
  try {
    const data = await sanityFetch<OurFaithPageType>({
      query: ourFaithPageQuery,
    });

    const title = data?.title
      ? `${data.title} | EWTN News`
      : (process.env.NEXT_PUBLIC_OUR_FAITH_TITLE ?? "Our Faith | EWTN News");
    const description =
      data?.description ??
      process.env.NEXT_PUBLIC_OUR_FAITH_DESCRIPTION ??
      defaultPageInfo.description;

    const ogImage =
      data?.faithCards?.[0]?.imageUrl ??
      process.env.NEXT_PUBLIC_OUR_FAITH_OG_IMAGE ??
      "/ewtn_logo.png";

    return {
      title,
      description,
      openGraph: {
        title,
        description,
        images: [
          {
            url: ogImage,
            width: 1200,
            height: 630,
            alt:
              data?.title ??
              process.env.NEXT_PUBLIC_OUR_FAITH_TITLE ??
              "Our Faith",
          },
        ],
        locale: "en_US",
        type: "website",
        siteName: "EWTN News",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [ogImage],
        creator: "@EWTNNews",
      },
    };
  } catch (error) {
    console.error("Error fetching metadata for Our Faith page:", error);
    return {
      title: "Our Faith | EWTN News",
      description: defaultPageInfo.description,
      openGraph: {
        title: "Our Faith | EWTN News",
        description: defaultPageInfo.description,
        images: ["/ewtn_logo.png"],
        locale: "en_US",
        type: "website",
        siteName: "EWTN News",
      },
      twitter: {
        card: "summary_large_image",
        title: "Our Faith | EWTN News",
        description: defaultPageInfo.description,
        images: ["/ewtn_logo.png"],
        creator: "@EWTNNews",
      },
    };
  }
}
export default async function OurFaithTest() {
  let processedData: OurFaithPageData;

  try {
    const fetchedData = await sanityFetch<OurFaithPageType>({
      query: ourFaithPageQuery,
    });

    if (fetchedData) {
      processedData = {
        title: fetchedData.title || defaultPageInfo.title,
        description: fetchedData.description || defaultPageInfo.description,
        cards: fetchedData.faithCards,
        error: false,
      };
    } else {
      processedData = {
        title: defaultPageInfo.title,
        description: defaultPageInfo.description,
        cards: [],
        error: true,
      };
    }
  } catch (e) {
    console.error("Error fetching Our Faith page data:", e);
    processedData = {
      title: defaultPageInfo.title,
      description: defaultPageInfo.description,
      cards: [],
      error: true,
    };
  }

  return (
    <div className="w-full">
      <PageHeader
        title={processedData.title}
        description={processedData.description}
      />

      {processedData.error && (
        <div className="container mx-auto px-4 py-5 mb-5">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 text-amber-800">
            <h3 className="font-medium">Content loading issue</h3>
            <p>
              We&apos;re experiencing difficulties retrieving the latest
              content. You&apos;re seeing default information until we resolve
              this issue.
            </p>
          </div>
        </div>
      )}

      {processedData.cards.length === 0 && !processedData.error && (
        <div className="container mx-auto px-4 py-5 mb-5">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-blue-800">
            <h3 className="font-medium">No faith cards available</h3>
            <p>There are currently no faith cards published in this section.</p>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-10">
        <div className="grid grid-cols-2 gap-4 md:grid-cols-2 md:gap-10">
          {processedData.cards.map((card, index) => (
            <FaithCard
              key={index}
              title={card.title}
              description={card.description}
              imageSrc={card.imageUrl}
              link={card.link}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
