import { Metadata } from "next";
import ContactUsClient from "./ContactUsClient";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata("/contact-us", "Contact Us", "Contact Us Page");
}

export default function ContactUs() {
  const jsonLd = buildWebPageJsonLd("Contact Us", "/contact-us");
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <main className="container mx-auto px-4 lg:px-8 py-8 mb-6">
        <h2 className="font-titles text-[25px] lg:text-[34px] font-semibold">
          Contact us
        </h2>
        <ContactUsClient />
      </main>
    </>
  );
}
