import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import LatestArticles, {
  ArticleCard,
} from "@/components/Author/LatestArticles";
import { Article, Author } from "@/sanity/queries/Author/author";

jest.mock("next/image", () => ({
  __esModule: true,
  default: ({ src, alt }: { src: string; alt: string }) => (
    <img src={src} alt={alt} data-testid="article-image" />
  ),
}));

jest.mock("@/utils/utils", () => ({
  getMediaImage: jest.fn((media) => media.url),
}));

jest.mock("@/components/Timestamp", () => ({
  __esModule: true,
  default: ({ date }: { date: string }) => (
    <span data-testid="timestamp">{new Date(date).toLocaleDateString()}</span>
  ),
}));

jest.mock("@/components/ui/Button", () => ({
  __esModule: true,
  default: ({
    children,
    onClick,
    state,
    className,
  }: {
    children: React.ReactNode;
    onClick: () => void;
    state: string;
    className?: string;
  }) => (
    <button
      onClick={onClick}
      disabled={state === "disabled"}
      className={className}
      data-testid="load-more-button"
    >
      {children}
    </button>
  ),
}));

jest.mock("@/components/Icon", () => ({
  Icon: ({ icon, className }: { icon: string; className?: string }) => (
    <span data-testid={`icon-${icon}`} className={className}>
      Icon: {icon}
    </span>
  ),
}));

jest.mock("@/utils/utils", () => ({
  getMediaImage: jest.fn((media) => media?.url || ""),
  getFirstMediaImageUrl: jest.fn((media) => media?.url || ""),
  getArticleUrl: (
    categorySlug: string,
    articleSlug: string,
    subcategorySlug?: string,
  ) => {
    if (subcategorySlug) {
      return `/${categorySlug}/${subcategorySlug}/${articleSlug}`;
    }
    return `/${categorySlug}/${articleSlug}`;
  },
  truncateTitle: jest.fn((text: string) =>
    text && text.length > 100 ? `${text.substring(0)}...` : text || "",
  ),
  truncateDescription: jest.fn((text: string) =>
    text && text.length > 200 ? `${text.substring(0, 200)}...` : text || "",
  ),
  truncateText: jest.fn((text: string, maxLength: number) =>
    text && text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text || "",
  ),
}));

global.fetch = jest.fn();

const mockArticle: Article = {
  id: "article-123",
  title: "Test Article Title",
  slug: "test-article",
  publishedDate: "2023-01-15T00:00:00Z",
  description: "This is a test article description",
  authors: [
    {
      name: "Author Name",
      slug: "author-slug",
    },
  ],
  category: {
    id: "category-1",
    title: "News",
    slug: "news",
  },
  media: {
    url: "/images/test-article.jpg",
    type: "image",
  },
  created_at: "2023-01-15T00:00:00Z",
  _updatedAt: "2023-01-15T00:00:00Z",
};

const mockAuthor: Partial<Author> = {
  id: "author-123",
  name: "Jane Smith",
  slug: "jane-smith",
  articles: [mockArticle],
  articlesCount: 15,
};

describe("ArticleCard Component", () => {
  it("renders article card correctly", () => {
    render(<ArticleCard author={mockAuthor as Author} article={mockArticle} />);

    expect(screen.getByText("Test Article Title")).toBeInTheDocument();
    expect(screen.getByText("News")).toBeInTheDocument();
    expect(
      screen.getByText("This is a test article description"),
    ).toBeInTheDocument();
  });

  it("links to the correct article URL", () => {
    render(<ArticleCard author={mockAuthor as Author} article={mockArticle} />);

    const articleLink = screen.getByText("Test Article Title").closest("a");
    expect(articleLink).toHaveAttribute("href", "/news/test-article");
  });

  it("links to the correct category URL", () => {
    render(<ArticleCard author={mockAuthor as Author} article={mockArticle} />);

    const categoryLink = screen.getByText("News").closest("a");
    expect(categoryLink).toHaveAttribute("href", "/news");
  });
});

describe("LatestArticles Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders initial articles correctly", () => {
    render(<LatestArticles author={mockAuthor as Author} />);

    expect(screen.getByText("Test Article Title")).toBeInTheDocument();
  });

  it("does not show load more button when articlesCount <= itemsPerPage", () => {
    const authorWithFewArticles = {
      ...mockAuthor,
      articlesCount: 5,
    };

    render(<LatestArticles author={authorWithFewArticles as Author} />);

    expect(screen.queryByTestId("load-more-button")).not.toBeInTheDocument();
  });

  it("shows load more button when articlesCount > itemsPerPage", () => {
    const authorWithManyArticles = {
      ...mockAuthor,
      articlesCount: 15,
    };

    render(<LatestArticles author={authorWithManyArticles as Author} />);

    expect(screen.getByTestId("load-more-button")).toBeInTheDocument();
    expect(screen.getByText("Load More")).toBeInTheDocument();
  });

  it("loads more articles when button is clicked", async () => {
    const mockResponse = {
      json: jest.fn().mockResolvedValue({
        data: {
          items: [
            {
              ...mockArticle,
              id: "article-456",
              title: "Second Test Article",
            },
          ],
          totalCount: 15,
        },
      }),
      ok: true,
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    render(<LatestArticles author={mockAuthor as Author} />);

    fireEvent.click(screen.getByTestId("load-more-button"));

    expect(global.fetch).toHaveBeenCalledWith(
      "/api/authors/author-123/articles?page=2&pageSize=10",
    );

    await waitFor(() => {
      expect(screen.getByText("Second Test Article")).toBeInTheDocument();
    });
  });

  it("shows loading state while fetching more articles", async () => {
    const mockResponse = {
      json: jest.fn().mockResolvedValue({
        data: {
          items: [
            {
              ...mockArticle,
              id: "article-456",
              title: "Second Test Article",
            },
          ],
          totalCount: 15,
        },
      }),
      ok: true,
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    render(<LatestArticles author={mockAuthor as Author} />);

    fireEvent.click(screen.getByTestId("load-more-button"));

    expect(screen.getByTestId("icon-loading")).toBeInTheDocument();
    expect(screen.getByTestId("icon-loading")).toHaveClass("animate-spin");

    await waitFor(() => {
      expect(screen.queryByTestId("icon-loading")).not.toBeInTheDocument();
    });
  });

  it("handles API errors gracefully", async () => {
    const mockErrorResponse = {
      json: jest.fn().mockResolvedValue({
        error: "Error loading articles",
        message: "Something went wrong",
      }),
      ok: false,
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockErrorResponse);

    const originalConsoleError = console.error;
    console.error = jest.fn();

    render(<LatestArticles author={mockAuthor as Author} />);

    fireEvent.click(screen.getByTestId("load-more-button"));

    await waitFor(() => {
      expect(console.error).toHaveBeenCalled();
    });

    expect(screen.getByText("Test Article Title")).toBeInTheDocument();

    console.error = originalConsoleError;
  });
});
