"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import Link from "next/link";
import { Icon } from "@/components/Icon";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";
import AuthorsList from "../Author/AuthorsList";
import { HomeSection } from "@/sanity/queries/HomePage/homePage";
import { Article } from "@/sanity/queries/Trending/trending";

interface CategoryArticlesSectionProps {
  section: Extract<HomeSection, { _type: "categorySection" }>;
}

interface ArticleCardProps {
  article: Article;
  className?: string;
}

export function ArticleCard({ article, className = "" }: ArticleCardProps) {
  return (
    <article
      className={`pr-4 mr-4 border-r border-grey-200 md:border-r-0 md:border-b md:pb-4 md:pr-0 md:mr-0 ${className}`}
    >
      <div className="flex gap-4 justify-between">
        <div>
          <h3 className="font-semibold font-titles text-[18px]">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="hover-underline-animation"
            >
              {truncateTitle(article.title)}
            </Link>
          </h3>

          <AuthorsList
            authors={article.authors}
            publishedDate={article.publishedDate ?? article.created_at}
          />
        </div>

        {article.media && article.media[0] && (
          <div className="w-20 flex-shrink-0">
            <Link
              href={getArticleUrl(
                article.category.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="transition-opacity hover:opacity-85"
            >
              <Image
                src={getMediaImage(article.media[0])}
                alt={article.title}
                width={80}
                height={80}
                className="object-cover rounded-2xl w-20 h-20"
              />
            </Link>
          </div>
        )}
      </div>
    </article>
  );
}

export default function CategoryArticlesSection({
  section,
}: CategoryArticlesSectionProps) {
  if (!section || !section.category || !section.category.articles) {
    return null;
  }
  const { articles } = section.category;
  const mainArticle = articles[0];
  const secondaryArticles = articles.slice(1);
  if (!mainArticle) return null;

  return (
    <section className="pb-2 xl:pb-4 pt-8 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="border-t py-4 xl:pt-6">
          <header className="mb-4 relative">
            <h2 className="text-[18px] lg:text-[20px] font-bold mb-2 uppercase">
              {mainArticle.category?.title} News
            </h2>

            <p className="text-[16px] lg:text-[18px] mb-3 xl:mb-6">
              {mainArticle.category?.description}
            </p>
          </header>

          <div className="md:grid md:grid-cols-12 xl:gap-4">
            <div className="md:col-span-6 xl:col-span-8 md:pr-4 xl:pr-0">
              <article className="xl:grid xl:grid-cols-12 xl:gap-4">
                <div className="xl:col-span-8 xl:order-2">
                  <div className="xl:border-r xl:border-grey-200 xl:pr-4">
                    <div className="relative aspect-video mb-4 rounded-2xl overflow-hidden">
                      {mainArticle?.media && mainArticle.media[0] && (
                        <>
                          <div className="w-full">
                            <Link
                              href={getArticleUrl(
                                mainArticle.category?.slug,
                                mainArticle.slug,
                                mainArticle.subcategory?.slug,
                              )}
                              className="transition-opacity hover:opacity-85"
                            >
                              <Image
                                src={getMediaImage(mainArticle?.media[0])}
                                alt={mainArticle.title}
                                fill
                                className="object-cover rounded-2xl w-20 h-20"
                              />

                              {mainArticle?.media[0]?.type == "video" && (
                                <div className="absolute bottom-2 md:bottom-4 left-2 lg:left-4 w-10 lg:w-16 h-10 lg:h-16 rounded-md lg:rounded-xl bg-black/70 flex items-center justify-center z-10">
                                  <Icon icon="play" className="w-3 lg:w-5" />
                                </div>
                              )}
                            </Link>
                          </div>
                        </>
                      )}
                    </div>

                    {mainArticle?.media[0]?.text && (
                      <div className="hidden lg:block text-[12px] text-grey-400 pb-2 border-b border-grey-200 mb-4 -mt-2 xl:mb-0">
                        {mainArticle?.media[0]?.text}
                      </div>
                    )}
                  </div>
                </div>

                <div className="xl:col-span-4">
                  <div className="mb-0">
                    {mainArticle.subcategory ? (
                      <Link
                        href={`/${mainArticle.category?.slug}/${mainArticle.subcategory.slug}`}
                        className="hover-underline-animation text-[13px] uppercase"
                      >
                        {mainArticle.subcategory.title}
                      </Link>
                    ) : (
                      mainArticle.tags?.length && (
                        <Link
                          href={`/tags/${mainArticle.tags[0]?.slug}`}
                          className="hover-underline-animation text-[13px] uppercase"
                        >
                          {mainArticle.tags[0]?.title}
                        </Link>
                      )
                    )}
                  </div>

                  <h2 className="text-[24px] lg:text-[32px] leading-[28px] lg:leading-[40px] font-semibold font-titles mb-3 ">
                    <Link
                      href={getArticleUrl(
                        mainArticle.category?.slug,
                        mainArticle.slug,
                        mainArticle.subcategory?.slug,
                      )}
                      className="hover-underline-animation"
                    >
                      {truncateTitle(mainArticle.title)}
                    </Link>
                  </h2>

                  {mainArticle.excerpt && (
                    <p className="text-[16px] leading-[18px] mb-4">
                      {truncateDescription(mainArticle.excerpt)}
                    </p>
                  )}

                  <AuthorsList
                    authors={mainArticle.authors}
                    publishedDate={
                      mainArticle.publishedDate ?? mainArticle.created_at
                    }
                    showImages={true}
                  ></AuthorsList>
                </div>
              </article>
            </div>

            <div className="col-span-6 space-y-4 xl:col-span-4 hidden md:block md:pl-4 xl:pl-0 md:border-l md:border-grey-200 xl:border-l-0">
              {secondaryArticles
                .filter((article, index) => index < 4)
                .map((article, index) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    className={`${index === 3 ? "xl:hidden" : ""}`}
                  />
                ))}

              <div>
                <Link
                  href={`/${mainArticle.category.slug}`}
                  className="text-[12px] inline-block hover-underline-animation relative"
                >
                  View All
                  <Icon
                    icon="arrow_forward"
                    className="!text-sm absolute left-full ml-1 -top-px"
                  />
                </Link>
              </div>
            </div>
          </div>

          <div className="relative mt-4 md:hidden">
            <div className="-mx-4 pl-4 sm:-mr-40">
              <Swiper
                modules={[Navigation]}
                navigation
                loop={true}
                slidesPerView={"auto"}
                centeredSlides={false}
                className="relative news-swiper"
              >
                {secondaryArticles.map((article) => (
                  <SwiperSlide
                    key={article.id}
                    className="!w-[90%] sm:!w-[40.6%] md:!w-[42%]"
                  >
                    <ArticleCard article={article} />
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>

            <div className="mt-4">
              <Link
                href={`/${mainArticle.category.slug}`}
                className="text-[12px] inline-block hover-underline-animation relative"
              >
                View All
                <Icon
                  icon="arrow_forward"
                  className="!text-sm absolute left-full ml-1 -top-px"
                />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
