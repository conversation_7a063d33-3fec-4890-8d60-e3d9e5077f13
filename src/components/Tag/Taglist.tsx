import React from "react";
import Tag from "./Tag"; // Import the Tag component
import { Article } from "@/sanity/queries/dailyStory";

interface TagsListProps {
  tags: Article["tags"];
}

const TagsList: React.FC<TagsListProps> = ({ tags }) => {
  if (!tags || tags.length === 0) return null;

  return (
    <div className="flex flex-wrap gap-3 mt-4">
      {tags.map((tag) => (
        <Tag key={tag.slug} label={tag.title} slug={tag.slug} />
      ))}
    </div>
  );
};

export default TagsList;
