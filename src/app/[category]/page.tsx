import { client } from "@/sanity/client";
import { categoryValidationQuery } from "@/sanity/queries/layout";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import CategoryClient from "@/app/[category]/CategoryClient";
import { sanityFetch } from "@/sanity/live";
import { categoryQuery } from "@/sanity/queries/Category/category";
import { Article } from "@/sanity/queries/Trending/trending";
import {
  fetchTrendingArticlesFromAnalytics,
  getTrendingWithPinnedArticles,
} from "@/app/api/trending";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

async function fetchCategory(options: {
  slug: string;
  ignoreArticlesIds: string[];
}) {
  return await sanityFetch({
    query: categoryQuery,
    params: {
      slug: options.slug,
      subcategoryId: null,
      ignoreArticlesIds: options.ignoreArticlesIds,
    },
  });
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ category: string }>;
}): Promise<Metadata> {
  const { category } = await params;
  const validCategory = await client.fetch(categoryValidationQuery, {
    category,
  });

  if (!validCategory) {
    return notFound();
  }

  return generatePageMetadata(
    `/${category}`,
    validCategory.seoMetadata?.title ?? `Section - ${validCategory.title}`,
    validCategory.seoMetadata?.description ?? validCategory.description,
  );
}

export default async function CategoryPage({
  params,
}: {
  params: Promise<{ category: string }>;
}) {
  const { category: categorySlug } = await params;
  const validCategory = await client.fetch(categoryValidationQuery, {
    category: categorySlug,
  });

  if (!validCategory) {
    return notFound();
  }

  const trendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: categorySlug,
    subcategorySlug: null,
    periodEnd: undefined,
    periodStart: undefined,
  });
  const trendingWithPinnedArticles: Article[] = getTrendingWithPinnedArticles(
    validCategory.pinnedArticles,
    trendingArticles,
  );
  const ignoreArticlesIds = trendingWithPinnedArticles.map(
    (trendingArticle: Article) => trendingArticle.id,
  );

  const { data: category } = await fetchCategory({
    slug: categorySlug,
    ignoreArticlesIds: ignoreArticlesIds,
  });

  if (!category) {
    return notFound();
  }

  const generalTrendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: null,
    subcategorySlug: null,
    periodEnd: undefined,
    periodStart: undefined,
  });

  const jsonLd = buildWebPageJsonLd(
    validCategory.seoMetadata?.title ?? `Section - ${validCategory.title}`,
    `/${category}`,
    validCategory.seoMetadata?.description ?? validCategory.description,
  );
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <CategoryClient
        category={category}
        subcategory={null}
        trendingArticles={trendingWithPinnedArticles}
        generalTrendingArticles={generalTrendingArticles}
        ignoreArticlesIds={ignoreArticlesIds}
      />
    </>
  );
}
