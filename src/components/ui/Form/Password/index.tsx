import { useId, useState } from "react";
import Button from "../../Button";

interface PasswordInputProps {
  label: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: boolean | string;
}

const PasswordInput = ({
  label,
  placeholder,
  value,
  onChange,
  error,
}: PasswordInputProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const id = useId();
  const inputId = `input-${id}`;

  return (
    <div>
      <label htmlFor={inputId} className="block text-[16px] mb-3">
        {label}
      </label>
      <div className="relative">
        <input
          id={inputId}
          type={isVisible ? "text" : "password"}
          className={`
                        w-full border rounded-xl ps-3.5 py-3 outline-none 
                        ${typeof error === "string" || error ? "border-red-700 bg-red-100" : "border-slate-300 bg-white"}
                    `}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
        />
        <Button
          as="icon"
          type="button"
          variant="outlinedBlack"
          iconName={isVisible ? "visibility_off" : "visibility"}
          className="absolute top-1 right-4 flex items-center z-20 !p-0 h-10 border-0"
          onClick={() => setIsVisible((prev) => !prev)}
          aria-label={isVisible ? "Hide password" : "Show password"}
          aria-pressed={isVisible}
          aria-controls="password"
        />
      </div>
      {typeof error === "string" && (
        <p className="mt-1 text-red-700 text-sm">{error}</p>
      )}
    </div>
  );
};

export default PasswordInput;
