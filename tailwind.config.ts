import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        titles: ["var(--font-ivyPresto)"],
        texts: ["var(--font-roboto)"],
        roboto: ["var(--font-roboto)"],
      },
      fontSize: {
        "2xl": ["24px", { lineHeight: "32px" }],
        "3xl": ["32px", { lineHeight: "40px" }],
        "4xl": ["42px", { lineHeight: "48px" }],
      },
      screens: {
        sm: "650px",
        md: "990px",
        lg: "1275px",
        xl: "1420px",
      },
      colors: {
        selection: "#EA133D",
        selectionText: "#ffffff",
      },
    },
    screens: {
      sm: "650px",
      md: "990px",
      lg: "1300px",
      xl: "1420px",
    },
    colors: {
      transparent: "transparent",
      current: "currentColor",
      white: "#ffffff",
      black: "#000000",
      liturgical: {
        white: "#F8F9FA",
        red: "#FF5252",
        green: "#4CAF50",
        purple: "#7E57C2",
        pink: "#E1A9BC",
        black: "#424242",
        gold: "#D6A208",
        grey: "#BBC3C6",
      },
      grey: {
        100: "#F4F4F4",
        200: "#E0E0E0",
        300: "#BCBCBC",
        400: "#555555",
        500: "#A8A8A8",
      },
      red: {
        100: "#FBE6E6",
        200: "#F7CCCC",
        300: "#F3B3B3",
        400: "#E66666",
        500: "#E24D4D",
        600: "#D82D2D",
        700: "#D60000",
        800: "#B90101",
      },
      blue: {
        100: "#E8F2FA",
        200: "#D2E4F4",
        300: "#BCD7EE",
        400: "#68A5DB",
        500: "#61A1D9",
        600: "#4B94D3",
        700: "#3586CD",
        800: "#1E79C8",
      },
      "alt-red": {
        100: "#FEEEEC",
        200: "#FEDED8",
        300: "#FDCEC5",
        400: "#FB9C8B",
        500: "#FA8C78",
        600: "#F97B65",
        700: "#F96B51",
        800: "#F85A3E",
      },
      "alt-blue": {
        100: "#E7EFF5",
        200: "#D0DEEB",
        300: "#B9CEE1",
        400: "#729DC3",
        500: "#5B8CB9",
        600: "#437CAF",
        700: "#2B6BA5",
        800: "#145B9B",
      },
    },
  },
  plugins: [
    require("tailwind-scrollbar"),
    require("tailwind-scrollbar-hide"),
    function ({
      addBase,
      theme,
    }: {
      addBase: (base: Record<string, any>) => void;
      theme: (path: string) => any;
    }) {
      addBase({
        "::selection": {
          backgroundColor: theme("colors.red.700"),
          color: theme("colors.white"),
        },
      });
    },
  ],
};

export default config;
