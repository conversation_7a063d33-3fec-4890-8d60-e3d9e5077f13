"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Button from "../../Button";

type SearchInputProps = {
  onSearchNavigate?: (query: string) => void;
};

const SearchInput = ({ onSearchNavigate }: SearchInputProps) => {
  const [query, setQuery] = useState("");
  const router = useRouter();

  const handleSearch = () => {
    const sanitized = query.replace(/[^a-zA-Z0-9 ]/g, "");
    if (!sanitized.trim()) return;

    if (onSearchNavigate) {
      onSearchNavigate(sanitized);
    }

    router.push(`/search?q=${encodeURIComponent(sanitized)}`);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className="w-full max-w-md flex items-center">
      <input
        className="border border-grey-200 border-r-0 py-[11px] px-4 rounded-xl rounded-e-none flex-grow font-normal w-full"
        type="text"
        placeholder="Search"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={handleKeyDown}
      />
      <Button
        variant="primaryBlack"
        iconName="searchWhite"
        className="!rounded-s-none text-white w-12 h-12 !rounded-xl"
        as="icon"
        onClick={handleSearch}
      />
    </div>
  );
};

export default SearchInput;
