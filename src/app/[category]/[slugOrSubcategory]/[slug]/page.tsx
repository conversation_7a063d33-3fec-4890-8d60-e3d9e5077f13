import { client } from "@/sanity/client";
import { sanityFetch } from "@/sanity/live";
import { articleQuery } from "@/sanity/queries/article";
import DailyStoryClient from "../DailyStoryClient";
import LiveBlogPageClient from "../LiveBlogPageClient";
import { notFound, redirect, RedirectType } from "next/navigation";
import { categoryValidationQuery } from "@/sanity/queries/layout";
import { Metadata } from "next";
import { buildArticleJsonLd, generateArticleMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";
import { fetchDisqusCommentCount } from "@/utils/disqus";

interface ArticlePageProps {
  params: Promise<{
    category: string;
    slugOrSubcategory: string;
    slug: string;
  }>;
  searchParams: Promise<Record<string, string | string[]>>;
}

async function fetchArticle(options: { slug: string; category: string }) {
  return await sanityFetch({
    query: articleQuery,
    params: {
      slug: options.slug,
      category: options.category,
    },
  });
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{
    category: string;
    slugOrSubcategory: string;
    slug: string;
  }>;
}): Promise<Metadata> {
  const { category, slugOrSubcategory, slug } = await params;
  const { data: article } = await fetchArticle({
    slug: slug,
    category: category,
  });

  if (!article) {
    return notFound();
  }

  if (!article.subcategory.slug) {
    return redirect(`/${category}/${slug}`, RedirectType.replace);
  }

  if (article.subcategory.slug !== slugOrSubcategory) {
    return redirect(
      `
      /${category}/${article.subcategory.slug}/${slug}
    `,
      RedirectType.replace,
    );
  }

  return generateArticleMetadata(article, category);
}

export default async function ArticlePage({
  params,
  searchParams,
}: ArticlePageProps) {
  const { category, slugOrSubcategory, slug } = await params;
  const { data: article } = await fetchArticle({
    slug: slug,
    category: category,
  });

  const q = await searchParams;
  const qs = new URLSearchParams(q as Record<string, string>).toString();
  const withQs = (path: string) => (qs ? `${path}?${qs}` : path);

  const isValidCategory: boolean = await client.fetch(categoryValidationQuery, {
    category,
  });

  if (!isValidCategory || !article) {
    return notFound();
  }

  if (!article.subcategory.slug) {
    return redirect(withQs(`/${category}/${slug}`), RedirectType.replace);
  }

  if (article.subcategory.slug !== slugOrSubcategory) {
    return redirect(
      withQs(`
      /${category}/${article.subcategory.slug}/${slug}
    `),
      RedirectType.replace,
    );
  }

  const jsonLd = buildArticleJsonLd(
    article,
    category,
    article.subcategory.slug,
  );

  const commentCount = await fetchDisqusCommentCount(article.id);

  if (article._type === "liveBlogArticle") {
    return (
      <>
        <JsonLD jsonLd={jsonLd} />
        <LiveBlogPageClient liveBlogArticle={article} />
      </>
    );
  }

  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <DailyStoryClient article={article} commentCount={commentCount} />
    </>
  );
}
