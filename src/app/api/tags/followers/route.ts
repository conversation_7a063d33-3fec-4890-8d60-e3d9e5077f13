import { errorResponse, successResponseWithData } from "@/app/api/util/errors";
import { getAuth0UserId } from "@/app/api/util";
import { getUserFollowing } from "@/app/api/services/tags/followers";

/**
 * Handles the GET request to retrieve follower information for a specific tag.
 *
 * @param params.tagId - The ID of the tag for which to retrieve follower information.
 * @returns A JSON response containing the follower count and whether the user follows the tag.
 *
 */
export async function GET() {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return successResponseWithData({ followedTagIds: [] }, 200);
    }

    const followedTags = await getUserFollowing(auth0UserId);
    const followedTagIds = followedTags.map((row) => row.tag_id);
    return successResponseWithData({ followedTagIds }, 200);
  } catch (error) {
    return errorResponse(error);
  }
}
