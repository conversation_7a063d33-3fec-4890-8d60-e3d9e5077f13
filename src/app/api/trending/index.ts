import { sanityFetch } from "@/sanity/client";
import {
  Article,
  ArticlesResponse,
  latestArticlesQuery,
  trendingArticlesQuery,
} from "@/sanity/queries/Trending/trending";
import { fetchTrendingArticles } from "../util/parsely/parsely";
import { HomePageResult } from "@/sanity/queries/HomePage/homePage";
import { SectionType } from "@/types/homePage";
import { format, subDays } from "date-fns";

type FetchTrendingArticlesParams = {
  listSize: number;
  categorySlug?: string | null; // using null since Sanity works with null values
  subcategorySlug?: string | null; // using null since Sanity works with null values
  periodStart?: string;
  periodEnd?: string;
};

export async function fetchTrendingArticlesFromAnalytics({
  listSize,
  categorySlug,
  subcategorySlug,
  periodStart,
  periodEnd,
}: FetchTrendingArticlesParams) {
  const trendingArticleSlugs = await fetchTrendingArticles({
    limit: listSize,
    categorySlug,
    subcategorySlug,
    periodStart,
    periodEnd,
  });

  const slugs = [
    ...new Set<string>(
      trendingArticleSlugs.map((article: { slug: string }) => article.slug),
    ),
  ];

  const trendingNews: Article[] = [];

  // Fetch from Sanity
  const query = `{
    "trendingArticles": ${trendingArticlesQuery},
    "latestArticles": ${latestArticlesQuery}
  }`;

  const sanityResponse = await sanityFetch<ArticlesResponse>({
    query,
    params: {
      slugs,
      categorySlug,
      subcategorySlug,
      limit: listSize,
    },
  });

  const fetchedTrendingArticles = sanityResponse?.trendingArticles || [];
  const sortedTrendingArticles = sortArticlesBySlugOrder(
    fetchedTrendingArticles,
    slugs,
  );
  trendingNews.push(...sortedTrendingArticles);

  // Slugs we already have (both trending and latest)
  const usedSlugs = new Set<string>(
    sortedTrendingArticles.map((article) => article.slug),
  );

  // Push latest articles (up to remaining needed)
  const latestArticles: Article[] = (
    sanityResponse?.latestArticles || []
  ).filter((article: Article) => !usedSlugs.has(article.slug));

  const stillNeeded = listSize - trendingNews.length;
  trendingNews.push(...latestArticles.slice(0, stillNeeded));
  latestArticles.slice(0, stillNeeded).forEach((a) => usedSlugs.add(a.slug));

  // If still short, refetch additional latest articles excluding all previous slugs
  if (trendingNews.length < listSize) {
    const finalBatch = await sanityFetch<Article[]>({
      query: latestArticlesQuery,
      params: {
        slugs: [], // This filter ensures no exclusion by slug
        categorySlug,
        subcategorySlug,
        limit: listSize,
      },
    });

    const additionalArticles = finalBatch.filter(
      (article: { slug: string }) => !usedSlugs.has(article.slug),
    );

    trendingNews.push(
      ...additionalArticles.slice(0, listSize - trendingNews.length),
    );
  }

  return trendingNews;
}

export function sortArticlesBySlugOrder(
  articles: Article[],
  slugs: string[],
): Article[] {
  const slugOrderMap: Record<string, number> = Object.fromEntries(
    slugs.map((slug, index) => [slug, index]),
  );

  return articles.sort(
    (a, b) =>
      (slugOrderMap[a.slug] ?? Infinity) - (slugOrderMap[b.slug] ?? Infinity),
  );
}

export function getTrendingWithPinnedArticles(
  pinnedArticles: Article[],
  trendingArticles: Article[],
  size = 5,
) {
  let trendingWithPinnedArticles: Article[] = [];
  if (pinnedArticles?.length) {
    const seen = new Set<string>();
    for (const pinned of pinnedArticles) {
      if (seen.size >= size) break;
      if (!seen.has(pinned.id)) {
        trendingWithPinnedArticles.push(pinned);
        seen.add(pinned.id);
      }
    }
    for (const art of trendingArticles) {
      if (seen.size >= size) break;
      if (!seen.has(art.id)) {
        trendingWithPinnedArticles.push(art);
        seen.add(art.id);
      }
    }
  } else {
    trendingWithPinnedArticles = trendingArticles;
  }
  return trendingWithPinnedArticles;
}

export async function fillHomeSectionsWithTrendingArticles(
  homePageData: HomePageResult,
) {
  const sections = homePageData?.sections || [];
  const updatedSections = await Promise.all(
    sections.map(async (section) => {
      switch (section._type) {
        case SectionType.Category: {
          const trendingArticles = await fetchTrendingArticlesFromAnalytics({
            listSize: 5,
            categorySlug: section.category?.slug,
            subcategorySlug: null,
            periodEnd: undefined,
            periodStart: undefined,
          });
          const trendingWithPinnedArticles: Article[] =
            getTrendingWithPinnedArticles(
              section.category?.pinnedArticles,
              trendingArticles,
            );
          return {
            ...section,
            category: {
              ...section.category,
              articles: trendingWithPinnedArticles,
            },
          };
        }
        case SectionType.CategoryWithSubcategories: {
          const subcategories = section.category?.subcategories || [];
          const updatedSubcategories = await Promise.all(
            subcategories.map(async (subcategory) => {
              const trendingArticles = await fetchTrendingArticlesFromAnalytics(
                {
                  listSize: 5,
                  categorySlug: section.category?.slug,
                  subcategorySlug: subcategory.slug,
                  periodEnd: undefined,
                  periodStart: undefined,
                },
              );
              const trendingWithPinnedArticles: Article[] =
                getTrendingWithPinnedArticles(
                  subcategory?.pinnedArticles,
                  trendingArticles,
                );
              return {
                ...subcategory,
                articles: trendingWithPinnedArticles,
              };
            }),
          );
          return {
            ...section,
            category: {
              ...section.category,
              subcategories: updatedSubcategories,
            },
          };
        }
        case SectionType.ThisWeekInPhotos: {
          // photos from last week without including today
          const periodEnd = subDays(new Date(), 1); // yesterday
          const periodStart = subDays(new Date(), 7); // 7 days ago

          const formatDate = (date: Date) => format(date, "yyyy-MM-dd");

          const trendingArticles = await fetchTrendingArticlesFromAnalytics({
            listSize: 20,
            categorySlug: null,
            subcategorySlug: null,
            periodStart: formatDate(periodStart),
            periodEnd: formatDate(periodEnd),
          });

          return {
            ...section,
            articles: trendingArticles,
          };
        }

        default:
          return section;
      }
    }),
  );
  return {
    ...homePageData,
    sections: updatedSections,
  };
}
