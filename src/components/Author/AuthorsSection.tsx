import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { useRef, useState, useEffect } from "react";
import { Author } from "@/sanity/queries/dailyStory";
import Button from "../ui/Button";
import Timestamp from "../Timestamp";
import Link from "next/link";
import Image from "next/image";
import type { Swiper as SwiperType } from "swiper";

interface AuthorsSectionProps {
  authors: Author[];
  publishedDate: string | undefined;
  _updatedAt: string | undefined;
}

export default function AuthorsSection({
  authors,
  publishedDate,
  _updatedAt,
}: AuthorsSectionProps) {
  const swiperRef = useRef<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  // Use Swiper only if there are more than 2 authors
  const useSwiper = authors.length > 2;

  useEffect(() => {
    if (swiperRef.current) {
      setTimeout(() => {
        setIsBeginning(swiperRef.current?.isBeginning ?? false);
        setIsEnd(swiperRef.current?.isEnd ?? false);
      }, 100);
    }
  }, [authors.length]);

  useEffect(() => {
    const checkSwiperState = () => {
      if (swiperRef.current) {
        setIsBeginning(swiperRef.current.isBeginning);
        setIsEnd(swiperRef.current.isEnd);
      }
    };

    if (swiperRef.current) {
      swiperRef.current.on("slideChange", checkSwiperState);
      swiperRef.current.on("reachEnd", checkSwiperState);
    }

    return () => {
      if (swiperRef.current) {
        swiperRef.current.off("slideChange", checkSwiperState);
        swiperRef.current.off("reachEnd", checkSwiperState);
      }
    };
  }, []);

  return (
    <div className="max-w-full relative overflow-visible pb-2">
      {useSwiper ? (
        <div className="relative pb-8">
          <div className="relative bg-white border border-grey-300 rounded-xl py-2 overflow-visible">
            {/* Left Blur (Appears only when not at the beginning) */}
            {!isBeginning && (
              <div className="absolute left-0 top-0 bottom-0 w-10 bg-gradient-to-r from-white to-transparent pointer-events-none z-10 rounded-l-xl" />
            )}

            <Swiper
              spaceBetween={0}
              slidesPerView="auto"
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
                setTimeout(() => {
                  setIsBeginning(swiper.isBeginning);
                  setIsEnd(swiper.isEnd);
                }, 100);
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              allowTouchMove={true}
              navigation={false}
              className="flex items-center"
            >
              {authors.map((author, index) => (
                <SwiperSlide
                  key={`${author.slug}-${index}`}
                  className="!w-fit px-1"
                >
                  <div className="flex flex-row items-center gap-2 px-2 py-1">
                    {/* Profile Image */}
                    <div className="w-[54px] h-[54px] flex-shrink-0 rounded-full bg-red-300 flex items-center justify-center overflow-hidden">
                      <Image
                        src={
                          author?.image
                            ? author.image.url
                            : "/images/default-author.png"
                        }
                        alt={author.name}
                        className="w-full h-full object-cover rounded-full hover:opacity-85 transition-all"
                        width={54}
                        height={54}
                      />
                    </div>

                    {/* ✅ Text Section: Name */}
                    <div className="flex flex-col">
                      <strong>
                        <Link
                          href={`/author/${author.slug}`}
                          className="hover-underline-animation"
                        >
                          {author.name}
                        </Link>
                      </strong>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Right Blur (Appears only when not at the end) */}
            {!isEnd && (
              <div className="absolute right-0 top-0 bottom-0 w-10 bg-gradient-to-l from-white to-transparent pointer-events-none z-10 rounded-r-xl" />
            )}

            {/* Custom Navigation Buttons - Fully Visible, Half Inside, Half Outside */}
            {!isBeginning && (
              <Button
                as="icon"
                onClick={() => swiperRef.current?.slidePrev()}
                iconName="arrow_back"
                variant="secondary"
                className="w-[33px] h-[32px] flex absolute top-1/2 -translate-y-1/2 left-[-17px] z-20"
              />
            )}
            {!isEnd && (
              <Button
                as="icon"
                onClick={() => swiperRef.current?.slideNext()}
                iconName="arrow_forward"
                variant="secondary"
                className="w-[33px] h-[32px] flex absolute top-1/2 -translate-y-1/2 right-[-17px] z-20"
              />
            )}
            {/* Published Date (Absolute Below Swiper) */}
            {useSwiper && (
              <div className="absolute w-full left-0 -bottom-8 text-left text-xs pb-2">
                <Timestamp date={publishedDate} /> · Updated{" "}
                <Timestamp date={_updatedAt} />
              </div>
            )}
          </div>
        </div>
      ) : authors.length === 2 ? (
        // Display exactly two authors side by side
        <div className="flex items-center gap-2">
          <div className="flex -space-x-3">
            {authors.map((author, index) => (
              <div
                key={`${author.slug}-${index}`}
                className="w-[54px] h-[54px] rounded-full  bg-red-300 flex items-center justify-center overflow-hidden"
              >
                <Image
                  src={
                    author?.image
                      ? author.image.url
                      : "/images/default-author.png"
                  }
                  alt={author.name}
                  className="w-full h-full object-cover rounded-full hover:opacity-85 transition-all relative z-10"
                  width={54}
                  height={54}
                />
              </div>
            ))}
          </div>

          {/* Author Names */}
          <div className="text-[14px]">
            By{" "}
            <strong>
              {authors.map((author, index) => (
                <span key={`${author.slug}-name`}>
                  <Link
                    href={`/author/${author.slug}`}
                    className="hover-underline-animation"
                  >
                    {author.name}
                  </Link>
                  {index < authors.length - 1 ? ", " : ""}
                </span>
              ))}
            </strong>
            <div className="text-xs">
              <Timestamp date={publishedDate} /> · Updated{" "}
              <Timestamp date={_updatedAt} />
            </div>
          </div>
        </div>
      ) : (
        // Static display for a single author
        <div className="flex gap-4">
          {authors.map((author, index) => (
            <div key={`${author.slug}-${index}`} className="flex items-center">
              {/* Profile Image */}
              <div className="w-[54px] h-[54px] rounded-full bg-red-300 flex items-center justify-center overflow-hidden mr-3">
                <Image
                  src={
                    author?.image
                      ? author.image.url
                      : "/images/default-author.png"
                  }
                  alt={author.name}
                  className="w-full h-full object-cover rounded-full hover:opacity-85 transition-all"
                  width={54}
                  height={54}
                />
              </div>
              <div className="text-[12px]">
                By{" "}
                <strong>
                  <Link
                    href={`/author/${author.slug}`}
                    className="hover-underline-animation"
                  >
                    {author.name}
                  </Link>
                </strong>
                <div>
                  <Timestamp date={publishedDate} /> · Updated{" "}
                  <Timestamp date={_updatedAt} />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
