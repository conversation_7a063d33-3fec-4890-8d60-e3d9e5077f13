"use client";

import VideoPlaylist from "@/components/Watch/VideoPlayList";
import Link from "next/link";
import Image from "next/image";
import { Icon } from "@/components/Icon";
import { getArticleUrl, getMediaImage, truncateTitle } from "@/utils/utils";
import { Article } from "@/sanity/queries/Trending/trending";
import AuthorsByName from "@/components/Author/AuthorsByName";
import Timestamp from "@/components/Timestamp";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import type { Swiper as SwiperType } from "swiper";
import { useRef, useState } from "react";
import "swiper/css";
import "swiper/css/navigation";
import Button from "@/components/ui/Button";
import { VideoCard } from "@/components/Watch/VideoCard";
import { VideoItem } from "../../mocks";

type Props = {
  playlistVideos: VideoItem[];
  relatedArticles: Article[];
  relatedVideos: VideoItem[];
  moreVideos: VideoItem[];
  category: string;
};

export const VideoClient: React.FC<Props> = ({
  playlistVideos,
  relatedArticles,
  relatedVideos,
  moreVideos,
  category,
}) => {
  const swiperRefRelatedArticles = useRef<SwiperType | null>(null);
  const swiperRefRelatedVideos = useRef<SwiperType | null>(null);
  const swiperRefMoreVideos = useRef<SwiperType | null>(null);
  const [isBeginningRelatedArticles, setIsBeginningRelatedArticles] =
    useState(true);
  const [isBeginningRelatedVideos, setIsBeginningRelatedVideos] =
    useState(true);
  const [isBeginningMoreVideos, setIsBeginningMoreVideos] = useState(true);
  const [isEndRelatedArticles, setIsEndRelatedArticles] = useState(false);
  const [isEndRelatedVideos, setIsEndRelatedVideos] = useState(false);
  const [isEndMoreVideos, setIsEndMoreVideos] = useState(false);

  return (
    <div className="bg-black text-white">
      <div className="container mx-auto pt-8 px-4 h-auto">
        <header>
          <nav className="mt-2">
            <ol className="flex items-center gap-2 text-[13px] lg:text-[14px]">
              <li className="underline hover:text-red-700 transition-all">
                <Link href="/">Home</Link>
              </li>
              <li>·</li>
              <li className="underline hover:text-red-700 transition-all">
                <Link href="/watch">Watch</Link>
              </li>
              <li>·</li>
              <li className="text-grey-300">{playlistVideos[0].title}</li>
            </ol>
          </nav>
          <div className="flex flex-col items-start lg:flex-row lg:gap-8 pt-6 lg:pt-8">
            <div>
              <h1 className="font-titles font-semibold text-[32px] lg:text-[40px] leading-none mt-4 lg:mt-0">
                Watch
              </h1>
              <p className="text-grey-500 py-4">
                The Best Videos, Handpicked for You
              </p>
            </div>
          </div>
        </header>

        <VideoPlaylist videos={playlistVideos} homeMode={false} />

        {/* Related Articles Section */}
        {relatedArticles.length > 0 && (
          <section className="mt-12 border-b border-t border-grey-500 pt-3 pb-5">
            <h2 className="text-[18px] lg:text-[20px] font-bold mb-4 uppercase">
              RELATED ARTICLES
            </h2>

            {/* Mobile carousel */}
            <div className="block lg:hidden relative">
              <Swiper
                spaceBetween={20}
                modules={[Navigation]}
                breakpoints={{
                  0: { slidesPerView: 1.3 },
                  768: { slidesPerView: 2 },
                  1024: { slidesPerView: 3 },
                  1280: { slidesPerView: 4 },
                }}
                onSwiper={(swiper) => {
                  swiperRefRelatedArticles.current = swiper;
                  setIsBeginningRelatedArticles(swiper.isBeginning);
                  setIsEndRelatedArticles(swiper.isEnd);
                }}
                onSlideChange={(swiper) => {
                  setIsBeginningRelatedArticles(swiper.isBeginning);
                  setIsEndRelatedArticles(swiper.isEnd);
                }}
                className="overflow-visible"
              >
                {relatedArticles.slice(0, 4).map((article) => (
                  <SwiperSlide key={article.slug}>
                    <div className="w-full pb-4">
                      <div className="relative mb-3.5 aspect-[16/9] w-full">
                        {article.media?.[0] && (
                          <Link
                            href={getArticleUrl(
                              article.category?.slug,
                              article.slug,
                              article.subcategory?.slug,
                            )}
                            className="transition-opacity hover:opacity-85"
                          >
                            <Image
                              src={getMediaImage(article.media[0])}
                              alt={article.title}
                              fill
                              className="object-cover rounded-2xl"
                            />

                            <div className="absolute bottom-2 left-2 z-10">
                              {article.media?.some(
                                (m) => m.type === "video",
                              ) && (
                                <div className="bg-black bg-opacity-60 p-2.5 rounded-2xl">
                                  <Icon
                                    icon="play"
                                    className="w-4 h-4 text-white"
                                  />
                                </div>
                              )}
                              {article.media?.length > 1 && (
                                <div className="mt-1 bg-black bg-opacity-60 p-2.5 rounded-2xl">
                                  <Icon
                                    icon="gallery"
                                    className="w-4 h-4 text-white"
                                  />
                                </div>
                              )}
                            </div>
                          </Link>
                        )}
                      </div>

                      <h3 className="text-[18px] font-semibold font-titles">
                        <Link
                          href={getArticleUrl(
                            article.category?.slug,
                            article.slug,
                            article.subcategory?.slug,
                          )}
                          className="hover-underline-animation-white"
                        >
                          {truncateTitle(article.title)}
                        </Link>
                      </h3>

                      <div className="text-[12px] mt-4">
                        {article.authors && (
                          <AuthorsByName
                            color="white"
                            authors={article.authors}
                          />
                        )}{" "}
                        · <Timestamp date={article.created_at} />
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>

              {!isBeginningRelatedArticles && (
                <Button
                  as="icon"
                  onClick={() => swiperRefRelatedArticles.current?.slidePrev()}
                  iconName="arrow_back"
                  variant="secondary"
                  className="hidden sm:flex w-[50px] h-[50px] absolute top-[calc(35%-60px)] left-0 -ml-[30px] z-10"
                />
              )}

              {!isEndRelatedArticles && (
                <Button
                  as="icon"
                  onClick={() => swiperRefRelatedArticles.current?.slideNext()}
                  iconName="arrow_forward"
                  variant="secondary"
                  className="hidden sm:flex w-[50px] h-[50px] absolute top-[calc(35%-60px)] right-0 -mr-[30px] z-10"
                />
              )}
            </div>

            {/* Desktop grid */}
            <div className="hidden lg:grid grid-cols-1 md:grid-cols-4 gap-6">
              {relatedArticles.slice(0, 4).map((article) => (
                <div key={article.slug} className="w-full pb-4">
                  <div className="relative mb-3.5 aspect-[16/9] w-full">
                    {article.media?.[0] && (
                      <Link
                        href={getArticleUrl(
                          article.category?.slug,
                          article.slug,
                          article.subcategory?.slug,
                        )}
                        className="transition-opacity hover:opacity-85"
                      >
                        <Image
                          src={getMediaImage(article.media[0])}
                          alt={article.title}
                          fill
                          className="object-cover rounded-2xl"
                        />
                      </Link>
                    )}

                    <div className="absolute bottom-2 left-2 z-10">
                      {article.media?.some((m) => m.type === "video") && (
                        <div className="bg-black bg-opacity-60 p-2.5 rounded-2xl">
                          <Icon icon="play" className="w-4 h-4 text-white" />
                        </div>
                      )}
                      {article.media?.length > 1 && (
                        <div className="mt-1 bg-black bg-opacity-60 p-2.5 rounded-2xl">
                          <Icon icon="gallery" className="w-4 h-4 text-white" />
                        </div>
                      )}
                    </div>
                  </div>

                  <h3 className="text-[18px] font-semibold font-titles">
                    <Link
                      href={getArticleUrl(
                        article.category?.slug,
                        article.slug,
                        article.subcategory?.slug,
                      )}
                      className="hover-underline-animation-white"
                    >
                      {truncateTitle(article.title)}
                    </Link>
                  </h3>
                  <div className="text-[12px] mt-4">
                    {article.authors && (
                      <AuthorsByName color="white" authors={article.authors} />
                    )}{" "}
                    · <Timestamp date={article.created_at} />
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Related Videos Section */}
        {relatedVideos.length > 0 && (
          <section className="relative border-b border-grey-500 pt-3 pb-5 !overflow-visible">
            <h2 className="text-[18px] lg:text-[20px] font-bold mb-4 uppercase">
              More From {category}
            </h2>
            <div className="mx-auto relative relative !overflow-visible">
              <div className="-mx-4 sm:-mx-6 px-4 sm:px-6 !overflow-hidden">
                <Swiper
                  spaceBetween={25}
                  modules={[Navigation]}
                  navigation={{
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                  }}
                  onSwiper={(swiper) => {
                    swiperRefRelatedVideos.current = swiper;
                    setIsBeginningRelatedVideos(swiper.isBeginning);
                    setIsEndRelatedVideos(swiper.isEnd);
                  }}
                  onSlideChange={(swiper) => {
                    setIsBeginningRelatedVideos(swiper.isBeginning);
                    setIsEndRelatedVideos(swiper.isEnd);
                  }}
                  breakpoints={{
                    0: { slidesPerView: 1.3 },
                    768: { slidesPerView: 2 },
                    1024: { slidesPerView: 3 },
                    1280: { slidesPerView: 4 },
                  }}
                  className="!overflow-visible mt-2"
                >
                  {relatedVideos.map((video, index) => (
                    <SwiperSlide key={index}>
                      <VideoCard
                        showDescription={true}
                        video={video}
                        thumbnail={video.imageUrl}
                        mode="column"
                        showCaption={false}
                        showTimestamp={false}
                      />
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>

              {!isBeginningRelatedVideos && (
                <Button
                  as="icon"
                  onClick={() => swiperRefRelatedVideos.current?.slidePrev()}
                  iconName="arrow_back"
                  variant="secondary"
                  className="hidden sm:flex w-[50px] h-[50px] absolute top-[calc(35%-60px)] left-0 -ml-[30px] z-10"
                />
              )}
              {!isEndRelatedVideos && (
                <Button
                  as="icon"
                  onClick={() => swiperRefRelatedVideos.current?.slideNext()}
                  iconName="arrow_forward"
                  variant="secondary"
                  className="hidden sm:flex w-[50px] h-[50px] absolute top-[calc(35%-60px)] right-0 -mr-[30px] z-10"
                />
              )}
            </div>
          </section>
        )}

        {/* More Videos Section */}
        {moreVideos.length > 0 && (
          <section className="pt-3 pb-5">
            <h2 className="text-[18px] lg:text-[20px] font-bold mb-4 uppercase">
              YOU MAY ALSO LIKE
            </h2>

            {/* Mobile carousel */}
            <div className="block lg:hidden relative">
              <Swiper
                spaceBetween={20}
                modules={[Navigation]}
                breakpoints={{
                  0: { slidesPerView: 1.3 },
                  768: { slidesPerView: 2 },
                  1024: { slidesPerView: 3 },
                  1280: { slidesPerView: 4 },
                }}
                onSwiper={(swiper) => {
                  swiperRefMoreVideos.current = swiper;
                  setIsBeginningMoreVideos(swiper.isBeginning);
                  setIsEndMoreVideos(swiper.isEnd);
                }}
                onSlideChange={(swiper) => {
                  setIsBeginningMoreVideos(swiper.isBeginning);
                  setIsEndMoreVideos(swiper.isEnd);
                }}
                className="overflow-visible"
              >
                {moreVideos.map((video, index) => (
                  <SwiperSlide key={index}>
                    <VideoCard
                      video={video}
                      thumbnail={video.imageUrl}
                      showDescription={true}
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
              {!isBeginningMoreVideos && (
                <Button
                  as="icon"
                  onClick={() => swiperRefMoreVideos.current?.slidePrev()}
                  iconName="arrow_back"
                  variant="secondary"
                  className="hidden sm:flex w-[50px] h-[50px] absolute top-[calc(35%-60px)] left-0 -ml-[30px] z-10"
                />
              )}
              {!isEndMoreVideos && (
                <Button
                  as="icon"
                  onClick={() => swiperRefMoreVideos.current?.slideNext()}
                  iconName="arrow_forward"
                  variant="secondary"
                  className="hidden sm:flex w-[50px] h-[50px] absolute top-[calc(35%-60px)] right-0 -mr-[30px] z-10"
                />
              )}
            </div>

            {/* Desktop grid */}
            <div className="hidden lg:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {moreVideos.map((video, index) => (
                <VideoCard
                  key={index}
                  video={video}
                  thumbnail={video.imageUrl}
                />
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};
