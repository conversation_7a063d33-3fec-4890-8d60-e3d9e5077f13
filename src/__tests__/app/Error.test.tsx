import { render } from "@testing-library/react";
import GlobalError from "@/app/error";
import { useRouter } from "next/navigation";

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

describe("GlobalError", () => {
  it("should redirect to /not-found on mount", () => {
    const replaceMock = jest.fn();
    (useRouter as jest.Mock).mockReturnValue({ replace: replaceMock });

    render(<GlobalError />);

    expect(replaceMock).toHaveBeenCalledWith("/not-found");
  });

  it("should render null", () => {
    (useRouter as jest.Mock).mockReturnValue({ replace: jest.fn() });
    const { container } = render(<GlobalError />);
    expect(container.firstChild).toBeNull();
  });
});
