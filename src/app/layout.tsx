import "./../styles/globals.css";
import "material-symbols";
import "react-toastify/dist/ReactToastify.css";
import Script from "next/script";
import { ToastContainer } from "react-toastify";
import { ReactNode } from "react";
import { ivy<PERSON>resto, roboto } from "@/lib/fonts";
import { sanityFetch } from "@/sanity/client";
import { SanityLive } from "@/sanity/live";
import { draftMode } from "next/headers";
import { DisableDraftMode } from "@/components/DisableDraftMode";
import {
  navigationMenuQuery,
  socialMediaQuery,
  complianceLinkQuery,
  NavigationMenuQueryResult,
  SocialMediaQueryResult,
  ComplianceLinkQueryResult,
  DonationLinkQueryResult,
  EwtnNetworkLinksQueryResult,
  ewtnNetworkLinksQuery,
  watchNavigationMenuQuery,
} from "@/sanity/queries/layout";
import { allShowsQuery, AllShowsQueryResult } from "@/sanity/queries/Show/show";
import ClientLayout from "@/components/ClientLayout";
import { workspace } from "@/sanity/env";

import { Metadata } from "next";
import { VisualEditing } from "next-sanity";

export const metadata: Metadata = {
  title: "Catholic News Agency",
  description:
    "Daily news about Pope Francis, the Vatican and the Catholic Church",
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon.ico",
    apple: "/apple-touch-icon.png",
    other: [
      { rel: "icon", url: "/favicon.svg", type: "image/svg+xml" },
      { rel: "icon", url: "/favicon-96x96.png", sizes: "96x96" },
    ],
  },
  manifest: "/site.webmanifest",
};

export default async function RootLayout({
  children,
}: {
  children: ReactNode;
}) {
  const layoutData = await fetchLayoutData();
  const parselySiteId = process.env.NEXT_PUBLIC_PARSELY_SITE_ID;
  const iubendaSiteId = process.env.NEXT_PUBLIC_IUBENDA_SITE_ID;
  const iubendaPolicyId = process.env.NEXT_PUBLIC_IUBENDA_POLICY_ID;

  return (
    <html lang="en">
      <head>
        <Script id="iubenda-config" strategy="lazyOnload">
          {`
            var _iub = _iub || [];
            _iub.csConfiguration = {
              siteId: "${iubendaSiteId}",
              cookiePolicyId: "${iubendaPolicyId}",
              lang: "en",
              storage: { useSiteId: true },
              floatingPreferencesButtonDisplay: "bottom-left",
              floatingPreferencesButtonZIndex: 999999,
              banner: {
                zIndex: 999999,
              },
              "callback": {
                "onPreferenceExpressed": function(preference) {
                  window.dispatchEvent(new CustomEvent('iubenda_cs_consent_given'));
                }
              }
            };
          `}
        </Script>
        <Script
          src={`https://cs.iubenda.com/autoblocking/${iubendaSiteId}.js`}
          strategy="lazyOnload"
        />
        <Script src="//cdn.iubenda.com/cs/gpp/stub.js" strategy="lazyOnload" />
        <Script
          src="//cdn.iubenda.com/cs/iubenda_cs.js"
          strategy="lazyOnload"
        />
        <Script id="iubenda-loader" strategy="lazyOnload">
          {`
          (function (w,d) {
            var loader = function () {
              var s = d.createElement("script"),
                  tag = d.getElementsByTagName("script")[0];
              s.src = "https://cdn.iubenda.com/iubenda.js";
              tag.parentNode.insertBefore(s, tag);
            };
            if (w.addEventListener) {
              w.addEventListener("load", loader, false);
            } else if (w.attachEvent) {
              w.attachEvent("onload", loader);
            } else {
              w.onload = loader;
            }
          })(window, document);
        `}
        </Script>
      </head>
      <body
        className={`${roboto.variable} ${ivyPresto.variable} pt-[102px] md:pt-[211px]`}
      >
        <div id="fb-root"></div>
        {/* Pass data to the client component */}
        <ClientLayout layoutData={layoutData}>{children}</ClientLayout>
        <SanityLive />
        {(await draftMode()).isEnabled && (
          <>
            <VisualEditing />
            <DisableDraftMode />
          </>
        )}
        <ToastContainer />
        <Script
          id="parsely-cfg"
          src={`//cdn.parsely.com/keys/${parselySiteId}/p.js`}
          strategy="lazyOnload"
        />
      </body>
    </html>
  );
}

async function fetchLayoutData() {
  const query = `{
    "navigationMenu": ${navigationMenuQuery},
    "watchMenu": ${watchNavigationMenuQuery},
    "socialMediaItems": ${socialMediaQuery},
    "complianceLinkItems": ${complianceLinkQuery},
    "donationLink": *[_type == "donationLink" && agency == '${workspace}'][0],
    "ewtnNetworkLinks": ${ewtnNetworkLinksQuery},
    "allShows": ${allShowsQuery},
  }`;

  try {
    const {
      navigationMenu,
      watchMenu,
      socialMediaItems,
      complianceLinkItems,
      donationLink,
      ewtnNetworkLinks,
      allShows,
    } = await sanityFetch<{
      navigationMenu: NavigationMenuQueryResult;
      watchMenu: NavigationMenuQueryResult;
      socialMediaItems: SocialMediaQueryResult;
      complianceLinkItems: ComplianceLinkQueryResult;
      donationLink: DonationLinkQueryResult;
      ewtnNetworkLinks: EwtnNetworkLinksQueryResult;
      allShows: AllShowsQueryResult;
    }>({ query, params: {}, tags: [] });

    return {
      navigationMenu,
      watchMenu,
      socialMediaItems,
      complianceLinkItems,
      donationLink: donationLink || { label: "Donate", url: "" },
      ewtnNetworkLinks,
      allShows: allShows || [],
    };
  } catch (error) {
    console.error("Error fetching navigation menu:", error); // Added for now to bypass Vercel error until it's implemented
    return {
      navigationMenu: { title: "", sections: [] },
      watchMenu: { title: "", sections: [] },
      socialMediaItems: [],
      complianceLinkItems: [],
      donationLink: { label: "Donate", url: "" },
      ewtnNetworkLinks: [],
      allShows: [],
    };
  }
}
