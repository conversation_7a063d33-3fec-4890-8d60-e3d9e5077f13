import Timestamp from "@/components/Timestamp";
import { NewsCardProps } from "@/types/interests";
import { truncateTitle } from "@/utils/utils";
import Image from "next/image";
import Link from "next/link";

export const NewsCardOld: React.FC<NewsCardProps> = ({ item }) => (
  <div
    className="flex gap-4 border-b border-grey-200 pb-4"
    data-testid="mock-news-card"
  >
    <div className="flex-1">
      <p className="text-[12px] lg:text-[13px] text-gray-600 mb-1">
        <Link href={`#`} className="hover-underline-animation">
          {item.category}
        </Link>
      </p>

      <h3 className="font-titles text-[17px] lg:text-[19px] font-bold mb-2 leading-none">
        <Link href={`#`} className="hover-underline-animation">
          {truncateTitle(item.title)}
        </Link>
      </h3>

      <div className="text-[12px] w-full">
        By{" "}
        <strong>
          <Link
            href={`/author/${item.author.name}`}
            className="hover-underline-animation"
          >
            {item.author.name}
          </Link>
        </strong>{" "}
        · <Timestamp date={new Date().toISOString()} />
      </div>
    </div>

    <div className="w-20 h-20 relative shrink-0">
      <Link href={`#`} className="hover:opacity-85 transition-all">
        <Image
          src="/account-interests-newscard.png"
          alt={item.title}
          fill
          className="w-full h-full object-cover rounded"
        />
      </Link>
    </div>
  </div>
);

export default NewsCardOld;
