import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import BreakingNews from "@/components/HomePage/BreakingNews";
import { BreakingNewsResult } from "@/sanity/queries/HomePage/breakingNews";
import { VideoPlayerProps } from "@/components/ui/VideoPlayer";

jest.mock("@/components/Timestamp", () => ({
  __esModule: true,
  default: ({ date }: { date: string }) => (
    <span data-testid="timestamp">{date}</span>
  ),
}));

jest.mock("@/components/Icon", () => ({
  Icon: ({ icon }: { icon: string }) => <span data-testid={`icon-${icon}`} />,
}));

jest.mock("@/components/ui/VideoPlayer", () => ({
  __esModule: true,
  default: ({ src, controls, playing, role }: VideoPlayerProps) => (
    <div
      role={role}
      data-testid="videoPlayer"
      data-src={src}
      data-controls={controls}
      data-playing={playing}
    >
      Mock Video Player
    </div>
  ),
}));

const mockBreakingNews: NonNullable<BreakingNewsResult> = {
  _type: "breakingNews",
  _id: "123",
  title: "Test Breaking News",
  active: true,
  lastUpdate: "2024-02-06T12:00:00Z",
  relatedLiveBlogArticle: {
    _id: "id",
    _updatedAt: "2024-02-06T12:00:00Z",
    title: "title",
    slug: {
      current: "article-slug",
    },
    category: {
      slug: {
        current: "category-slug",
      },
    },
    subcategory: {
      slug: {
        current: "subcategory-slug",
      },
    },
    media: {
      url: "https://example.com/image.jpg",
      type: "image",
      text: "image-text",
    },
  },
  media: {
    url: "https://example.com/image.jpg",
    type: "image",
    format: "jpg",
    public_id: "image123",
  },
};

const mockBreakingNewsVideo: NonNullable<BreakingNewsResult> = {
  ...mockBreakingNews,
  media: {
    url: "https://example.com/video.mp4",
    type: "video",
    format: "mp4",
    public_id: "video123",
  },
};

describe("BreakingNews Component", () => {
  it("renders video correctly", () => {
    render(<BreakingNews breakingNews={mockBreakingNewsVideo} />);

    const video = screen.getByTestId("videoPlayer");
    expect(video).toBeInTheDocument();
    expect(video).toHaveAttribute("data-src", mockBreakingNewsVideo.media.url);
  });

  it("expands video when clicked", () => {
    render(<BreakingNews breakingNews={mockBreakingNewsVideo} />);

    const videoButton = screen.getByLabelText(/play video/i);
    fireEvent.click(videoButton);

    const video = screen.getByTestId("videoPlayer");
    expect(video).toHaveAttribute("data-controls", "true");
  });
});
