// Select/index.tsx
import { useId } from "react";
import { Icon } from "@/components/Icon";
import { SelectProps, SelectSize } from "./types";

const Select = ({
  label,
  placeholder,
  value,
  options,
  onChange,
  error,
  disabled,
  size = "sm",
  ...props
}: SelectProps) => {
  const id = useId();
  const selectId = `select-${id}`;

  const sizeClasses: Record<SelectSize, string> = {
    xs: "text-xs",
    sm: "text-[16px]",
  };

  return (
    <div className="w-full">
      {label ? (
        <label className="text-[16px]" htmlFor={selectId}>
          {label}
        </label>
      ) : (
        ""
      )}

      <div
        className={`
            relative bg-white w-full border rounded-xl
            ${disabled ? "cursor-not-allowed opacity-70" : "cursor-pointer"}
            ${typeof error === "string" || error ? "border-red-700 bg-red-100" : "border-grey-200"}
        `}
      >
        <select
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className={`
              w-full pr-8 pl-4 py-4 bg-transparent appearance-none relative z-10 leading-none
              focus:outline-none
              ${sizeClasses[size]}
            `}
          {...props}
          role="combobox"
          aria-label={label ? label : placeholder}
        >
          <option value="" disabled>
            {placeholder}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <div
          className={`
              flex justify-center items-center pointer-events-none z-0 absolute right-2 top-4 bottom-4
              ${size === "xs" ? "border-l border-grey-200 h-3" : ""}
            `}
        >
          <Icon icon="open" className={`${size === "sm" ? "!text-4xl" : ""}`} />
        </div>
      </div>

      {typeof error === "string" && (
        <p className="mt-1 text-red-700 text-sm">{error}</p>
      )}
    </div>
  );
};

export default Select;
