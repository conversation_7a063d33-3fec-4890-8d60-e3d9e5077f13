import { handleCopy } from "@/utils/utils";
import { Icon } from "../Icon";

interface ClipboardButtonProps {
  shareUrl: string;
  size?: number;
  bgColor?: string;
  hoverBgColor?: string;
  iconColor?: string;
  iconSize?: number;
}

const ClipboardButton = ({
  shareUrl,
  size = 34,
  bgColor = "transparent",
  hoverBgColor = "#E3E3E3",
  iconColor = "#000",
  iconSize = 24,
}: ClipboardButtonProps) => {
  return (
    <div
      data-testid="clipboard-button"
      className="flex items-center justify-center rounded-full transition-colors cursor-pointer"
      style={{
        width: size,
        height: size,
      }}
      onClick={() => handleCopy(shareUrl)}
      onMouseEnter={(e) =>
        (e.currentTarget.style.backgroundColor = hoverBgColor)
      }
      onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = bgColor)}
    >
      <Icon icon="clipboard" color={iconColor} size={iconSize} />
    </div>
  );
};

export default ClipboardButton;
