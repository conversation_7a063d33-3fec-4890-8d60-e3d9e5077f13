"use client";
import Link from "next/link";
import Image from "next/image";
import { Icon, IconProps } from "../Icon";
import Button from "../ui/Button";
import { otherLinksData } from "./footerData";
import React, { useEffect, useState } from "react";
import { NavigationMenuQueryResult } from "@/sanity/queries/layout";
import { FooterMobileProps } from "./footerMobile";
import PrivacyPolicyLink from "@/components/Layout/PrivacyPolicyLink";
import CookiePolicyLink from "@/components/Layout/CookiePolicyLink";
import { COPYRIGHT_TEXT, LEGAL_NOTICE } from "@/constants";

export type Subsection = {
  name?: string;
  href: string;
  icon: IconProps["icon"];
  isExternal?: boolean;
  isOnReducedFooter?: boolean;
};

export interface FooterDesktopProps extends FooterMobileProps {
  navigationMenu: NavigationMenuQueryResult;
  isFooterReduced: boolean;
}

const FooterDesktop = ({
  socialMediaItems,
  complianceLinkItems,
  donationLink,
  isFooterReduced,
}: FooterDesktopProps) => {
  const socialMediaLinks = socialMediaItems || [];
  const bottomLinks = complianceLinkItems || [];
  const otherLinks: Subsection[] = otherLinksData;
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      const viewportHeight = window.innerHeight;

      setShowBackToTop(scrolled > viewportHeight * 2);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <footer className="text-sm mb-8">
      {isFooterReduced ? (
        <div className="container mx-auto px-4 pt-4">
          {/* Reduced Footer */}
          <div className="pt-5 border-t border-grey-200 flex flex-col items-center gap-6">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 text-center">
              <p className="mr-5">
                © EWTN {new Date().getFullYear()}. All rights reserved.
              </p>
              {bottomLinks
                .filter((x) => x.isOnReducedFooter === true)
                .map((link, index) => (
                  <React.Fragment key={index}>
                    <Link
                      href={`/${link.slug.current}`}
                      className="text-gray-600 hover-underline-animation"
                    >
                      {link.title}
                    </Link>
                  </React.Fragment>
                ))}
              <React.Fragment>
                <PrivacyPolicyLink />
              </React.Fragment>
              <React.Fragment>
                <CookiePolicyLink />
              </React.Fragment>
            </div>
          </div>
        </div>
      ) : (
        <div className="container mx-auto px-4 pt-4">
          {/* Top Section */}
          <div className="border-t pt-4 flex justify-between gap-6">
            <div className="flex items-center space-x-4">
              <Link href="/" passHref>
                <Image
                  src="/ewtn_logo.png"
                  alt="EWTN Logo"
                  width={173}
                  height={38}
                />
              </Link>
            </div>
            <div className="w-64">
              <Link href={donationLink.url} passHref target="_blank">
                <Button variant="primary" iconName="donate" className="w-full">
                  {donationLink.label}
                </Button>
              </Link>
            </div>
          </div>
          {/* Social Icons */}
          <div className="py-4 border-b border-grey-200 w-full flex flex-col gap-2">
            <span>Follow us</span>
            <div className="w-full flex flex-col md:flex-row justify-between items-start md:items-center">
              {/* Social Icons */}
              <div className="flex flex-wrap space-x-6 mb-4 md:mb-0">
                {socialMediaLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={`Follow us on ${social.icon}`}
                  >
                    <Icon icon={social.icon} />
                  </a>
                ))}
              </div>

              {/* Other Links */}
              <div className="font-semibold">
                <ul className="flex flex-col md:flex-row md:space-x-6 space-y-2 md:space-y-0">
                  {otherLinks.map((link, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <Icon icon={link.icon || ""} />
                      <Link
                        href={link.href}
                        className="hover-underline-animation"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          {/* Bottom Section */}
          <div className="bg-gray-200 py-4">
            <div className="mx-auto grid grid-cols-1 md:grid-cols-[5fr,1fr] lg:grid-cols-[6fr,1fr] gap-6">
              <div className="flex items-center space-x-4">
                {bottomLinks.map((link, index) => (
                  <React.Fragment key={index}>
                    {link.externalUrl ? (
                      <a
                        href={link.externalUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover-underline-animation"
                      >
                        {link.title}
                      </a>
                    ) : (
                      <Link
                        href={`/${link.slug.current}`}
                        className="text-gray-600 hover-underline-animation"
                      >
                        {link.title}
                      </Link>
                    )}
                    {index < bottomLinks.length - 1 && (
                      <span className="text-grey-200">|</span>
                    )}
                  </React.Fragment>
                ))}
                <span className="text-grey-200">|</span>
                <React.Fragment>
                  <PrivacyPolicyLink />
                </React.Fragment>
                <span className="text-grey-200">|</span>
                <React.Fragment>
                  <CookiePolicyLink />
                </React.Fragment>
              </div>
            </div>
          </div>

          {/* Copyright Section */}
          <div className="container mx-auto text-left py-4">
            <p>{COPYRIGHT_TEXT}</p>
            <p>{LEGAL_NOTICE}</p>
          </div>
        </div>
      )}

      {showBackToTop && (
        <div className="fixed left-0 right-0 bottom-24 w-full z-50">
          <div className="container mx-auto px-4 relative">
            <Button
              className="px-4 absolute right-4 shadow-lg"
              variant="secondary"
              iconName="arrow_up"
              iconPosition="right"
              onClick={scrollToTop}
            >
              Back to top
            </Button>
          </div>
        </div>
      )}
    </footer>
  );
};

export default FooterDesktop;
