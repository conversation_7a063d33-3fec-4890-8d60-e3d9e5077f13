import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const termsAndConditionsQuery = defineQuery(`
  *[
    _type == "staticColumnPage" &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) && 
    slug.current == "terms-and-conditions"
  ][0]{
    title,
    layout,
    content
  }
`);

export type TermsAndConditionsQueryResult = {
  title: string;
  layout: "single" | "double";
  content: {
    textBody: Array<{
      _key: string;
      _type: string;
      style?: string;
      markDefs: Array<{
        _type: string;
        _key: string;
        href?: string;
      }>;
      children: Array<{
        _key: string;
        _type: string;
        marks: string[];
        text: string;
      }>;
      textBody: {
        style: string;
        _key: string;
        _type: string;
        markDefs: Array<{ _type: string; href?: string; _key: string }>;
        children: Array<{
          _type: string;
          marks: string[];
          text: string;
          _key: string;
        }>;
      }[];
    }>;
  };
};
