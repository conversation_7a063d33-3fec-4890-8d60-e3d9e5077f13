import { fetchTrendingArticlesFromAnalytics } from "@/app/api/trending";
import { mockVideoItems } from "../mocks";
import { TrendingClient } from "./TrendingClient";

// TODO: Add metadata

export default async function TopicPage() {
  const videos = [
    ...[...mockVideoItems].sort(() => Math.random() - 0.5),
    ...[...mockVideoItems].sort(() => Math.random() - 0.5).slice(0, 2),
  ];

  const generalTrendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: null,
    subcategorySlug: null,
    periodStart: undefined,
    periodEnd: undefined,
  });

  // TODO: Add jsonLD
  // const jsonLd = buildVideoPageJsonLd();

  return (
    <>
      {/*
        // TODO: Add jsonLD
        <JsonLD jsonLd={jsonLd} />
      */}

      <TrendingClient
        initialVideos={videos}
        generalTrendingArticles={generalTrendingArticles}
      />
    </>
  );
}
