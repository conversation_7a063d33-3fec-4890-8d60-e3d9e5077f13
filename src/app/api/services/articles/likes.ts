import { createClient, errorCodes } from "@/app/api/util/db/supabase";
import { workspace } from "@/sanity/env";

export async function addLike(
  user_id: string,
  article_id: string,
): Promise<void> {
  const supabase = await createClient();
  const { error, status } = await supabase
    .from("article_likes")
    .insert([{ user_id, article_id, agency: workspace }]);

  if (error) {
    console.log(error);
    if (status === errorCodes.ALREADY_EXIST_STATUS) {
      throw new Error("Already likeed");
    }
    if (status === errorCodes.FORBIDDEN) {
      throw new Error("Forbidden");
    }
    throw new Error("Unexpected database error");
  }
}

export async function removeLike(
  user_id: string,
  article_id: string,
): Promise<void> {
  const supabase = await createClient();
  const { error, status } = await supabase
    .from("article_likes")
    .delete()
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .eq("article_id", article_id);

  if (error) {
    if (status === errorCodes.NOT_FOUND) {
      throw new Error("Not found");
    }
    throw new Error("Unexpected database error");
  }
}

export async function getLikeCount(
  article_id: string,
): Promise<{ like_count: number }> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("article_like_counts")
    .select("like_count")
    .eq("agency", workspace)
    .eq("article_id", article_id)
    .maybeSingle();

  if (error) {
    throw new Error("Unexpected database error");
  }
  return data || { like_count: 0 };
}

export async function getUserLike(
  user_id: string,
  article_id: string,
): Promise<{ id: string; user_id: string; article_id: string }> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("article_likes")
    .select("*")
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .eq("article_id", article_id)
    .maybeSingle();

  if (error) {
    throw new Error("Unexpected database error");
  }

  return data;
}

export async function getUserLikes(
  user_id: string,
): Promise<
  { id: string; user_id: string; article_id: string; created_at: Date }[]
> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("article_likes")
    .select("*")
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .maybeSingle();

  if (error) {
    throw new Error("Unexpected database error");
  }

  return data;
}
