"use client";
import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Icon } from "../Icon";
import Button from "../ui/Button";
import {
  SocialMediaQueryResult,
  ComplianceLinkQueryResult,
  DonationLinkQueryResult,
} from "@/sanity/queries/layout";
import PrivacyPolicyLink from "@/components/Layout/PrivacyPolicyLink";
import CookiePolicyLink from "@/components/Layout/CookiePolicyLink";
import { COPYRIGHT_TEXT, LEGAL_NOTICE } from "@/constants";

export interface FooterMobileProps {
  socialMediaItems: SocialMediaQueryResult;
  complianceLinkItems: ComplianceLinkQueryResult;
  donationLink: DonationLinkQueryResult;
  isFooterReduced: boolean;
}

const FooterMobile = ({
  socialMediaItems,
  complianceLinkItems,
  donationLink,
  isFooterReduced,
}: FooterMobileProps) => {
  const socialMediaLinks = socialMediaItems || [];
  const bottomLinks = complianceLinkItems || [];

  return (
    <footer className="container mx-auto px-4 pt-4">
      <div className="border-t border-black text-sm pt-6 pb-20">
        {isFooterReduced ? (
          <>
            <div className="py-2">
              <div className="flex flex-col items-center gap-6">
                {/* Text on one line */}
                <div className="text-center">
                  <p>
                    © EWTN {new Date().getFullYear()}. All rights reserved.
                  </p>
                </div>
                {/* Links on another line */}
                <div className="flex flex-wrap justify-center gap-4">
                  {bottomLinks
                    .filter((x) => x.isOnReducedFooter === true)
                    .map((link, index) => (
                      <React.Fragment key={index}>
                        <Link
                          href={`/${link.slug.current}`}
                          className="text-gray-600 hover:text-gray-900 hover-underline-animation"
                        >
                          {link.title}
                        </Link>
                      </React.Fragment>
                    ))}
                  <React.Fragment>
                    <PrivacyPolicyLink />
                  </React.Fragment>
                  <React.Fragment>
                    <CookiePolicyLink />
                  </React.Fragment>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Logo and Dropdown */}
            <div className="flex flex-col items-center space-y-4 mb-4 border-b-2 pb-4 border-grey-200 ">
              <Link href="/" passHref>
                <Image
                  src="/ewtn_logo.png"
                  alt="EWTN Logo"
                  width={173}
                  height={38}
                />
              </Link>
            </div>
            <div className="flex justify-center gap-4 mb-4 border-b-2 pb-4 border-grey-200">
              <div className="w-full md:w-56">
                <Link href={donationLink.url} passHref target="_blank">
                  <Button
                    variant="primary"
                    iconName="donate"
                    className="w-full"
                  >
                    {donationLink.label}
                  </Button>
                </Link>
              </div>
            </div>

            {/* Bottom Links */}
            <div className="text-left mb-4 border-b-2 pb-4 border-grey-200">
              <div className="grid grid-cols-2 sm:hidden">
                {/* Left Column */}
                <div className="pr-4 pl-5 space-y-2">
                  {bottomLinks
                    .slice(0, Math.ceil(bottomLinks.length / 2)) // First half of links
                    .map((link, index) => (
                      <div key={index}>
                        <Link
                          href={`/${link.slug.current}`}
                          className="text-gray-600 hover:text-gray-900 hover-underline-animation"
                        >
                          {link.title}
                        </Link>
                      </div>
                    ))}
                  <div>
                    <PrivacyPolicyLink />
                  </div>
                </div>
                {/* Right Column */}
                <div className="pl-4 border-l border-grey-200 space-y-2">
                  {bottomLinks
                    .slice(Math.ceil(bottomLinks.length / 2)) // Second half of links
                    .map((link, index) => (
                      <div key={index}>
                        <Link
                          href={`/${link.slug.current}`}
                          className="text-gray-600 hover:text-gray-900 hover-underline-animation"
                        >
                          {link.title}
                        </Link>
                      </div>
                    ))}
                  <div>
                    <CookiePolicyLink />
                  </div>
                </div>
              </div>
              <div className="gap-4 hidden sm:flex justify-center">
                {bottomLinks.map((link, index) => (
                  <div key={index}>
                    <Link
                      href={`/${link.slug.current}`}
                      className="text-gray-600 hover:text-gray-900 hover-underline-animation"
                    >
                      {link.title}
                    </Link>
                  </div>
                ))}
                <div>
                  <PrivacyPolicyLink />
                </div>
                <div>
                  <CookiePolicyLink />
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div className="mb-6 text-center border-b-2 pb-5 border-grey-200">
              <p>Follow us</p>
              <div className="flex justify-center space-x-4 mt-2">
                {socialMediaLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.link}
                    aria-label={social.icon}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Icon icon={social.icon} />
                  </a>
                ))}
              </div>
            </div>

            {/* Copyright */}
            <div className="text-center text-xs text-gray-600 pb-2">
              <p>{COPYRIGHT_TEXT}</p>
              <p>{LEGAL_NOTICE}</p>
            </div>
          </>
        )}
      </div>
    </footer>
  );
};

export default FooterMobile;
