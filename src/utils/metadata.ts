import { Metadata } from "next";
import { truncateText } from "./utils";
import {
  CollectionPage,
  NewsArticle,
  Person,
  VideoObject,
  WebPage,
  WithContext,
} from "schema-dts";
import { ShowData } from "@/app/watch/show/[slug]/ShowClient";

const siteName = "Catholic News Agency";

interface Media {
  type: string;
  url: string;
  text?: string;
}

interface Article {
  _type: string;
  slug: string;
  publishedDate: string;
  authors?: { slug: string; name: string }[];
  updatedAt?: string;
  subcategory?: { slug: string };
  media: Media[];
  seoMetadata?: {
    title?: string;
    description?: string;
  };
  socialMediaMetadata?: {
    facebookTitle?: string;
    facebookDescription?: string;
    twitterTitle?: string;
    twitterDescription?: string;
  };
  title: string;
  description: string;
  tags?: { slug: string }[];
}

export function generateArticleMetadata(
  article: Article,
  category: string,
): Metadata {
  if (!article) return {}; // Return empty metadata if no article found

  let url = `${process.env.NEXT_PUBLIC_URL}/${category}/${article.slug}`;
  if (article.subcategory) {
    url = `${process.env.NEXT_PUBLIC_URL}/${category}/${article.subcategory.slug}/${article.slug}`;
  }

  const images = article.media
    ? article.media
        .filter((m: Media) => m.type?.startsWith("image"))
        .map((img: Media) => ({
          url: img.url,
          width: 1200,
          height: 630,
          alt: img.text || "",
        }))
    : [];

  const videos = article.media
    ? article.media
        .filter((m: Media) => m.type?.startsWith("video"))
        .map((vid: Media) => ({
          url: vid.url,
          secureUrl: vid.url,
          type: vid.type,
          width: 1280,
          height: 720,
        }))
    : [];

  let parselyTags = article.tags
    ?.map((tag: { slug: string }) => tag.slug)
    .join(", ");

  if (article.subcategory) {
    parselyTags = `${article.subcategory.slug}, ${parselyTags}`;
  }

  if (article._type === "dailyStoryArticle") {
    parselyTags = `${parselyTags}, dailyStory`;
  }

  const parselyAuthors =
    article.authors?.map((author) => [`parsely-author`, author.slug]) || [];

  return {
    title: article.seoMetadata?.title || article.title,
    description: article.seoMetadata?.description || article.description,
    alternates: {
      canonical: url,
    },
    openGraph: {
      siteName: siteName,
      title: article.socialMediaMetadata?.facebookTitle || article.title,
      description:
        article.socialMediaMetadata?.facebookDescription || article.description,
      url: url,
      type: "article",
      images: images,
      videos: videos,
    },
    keywords: article.tags?.map((tag: { slug: string }) => tag.slug) || [],
    twitter: {
      card: "summary_large_image",
      title: article.socialMediaMetadata?.twitterTitle || article.title,
      description:
        article.socialMediaMetadata?.twitterDescription || article.description,
      images: images.length > 0 ? [images[0].url] : [],
    },
    other: {
      "parsely-title": article.seoMetadata?.title || article.title,
      "parsely-link": url,
      "parsely-type": "post",
      "parsely-image-url": images.length > 0 ? images[0].url : "",
      "parsely-pub-date": article.publishedDate,
      "parsely-section": category,
      "parsely-tags": parselyTags || "",
      ...Object.fromEntries(parselyAuthors),
    },
  };
}

export function generatePageMetadata(
  url: string,
  title: string,
  description: string,
  image?: string,
): Metadata {
  const siteUrl = `${process.env.NEXT_PUBLIC_URL}${url}`;

  return {
    title: title,
    description: truncateText(description, 120, "..."),
    alternates: {
      canonical: siteUrl,
    },
    openGraph: {
      siteName: siteName,
      title: title,
      description: description,
      url: siteUrl,
      type: "website",
      images: image ? [image] : [],
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: description,
      images: image ? [image] : [],
    },
    other: {
      "parsely-title": title,
      "parsely-type": "index",
      "parsely-link": siteUrl,
      "parsely-image-url": image ?? "",
    },
  };
}

export function generateShowMetadata(slug: string, show: ShowData): Metadata {
  const siteUrl = `${process.env.NEXT_PUBLIC_URL}/show/${slug}`;

  const images = show.imageUrl
    ? [
        {
          url: show.imageUrl,
          width: 1200,
          height: 630,
          alt: show.name,
        },
      ]
    : [];

  return {
    title: show.name,
    description: show.shortDescription || show.description,
    alternates: {
      canonical: siteUrl,
    },
    openGraph: {
      siteName: siteName,
      title: show.name,
      description: show.shortDescription || show.description,
      url: siteUrl,
      type: "video.other",
      images,
    },
    twitter: {
      card: "summary_large_image",
      title: show.name,
      description: show.shortDescription || show.description,
      images: images.length ? [images[0].url] : [],
    },
  };
}

export function buildArticleJsonLd(
  article: Article,
  categorySlug: string,
  subcategorySlug?: string,
): WithContext<NewsArticle> {
  const images = article.media
    ? article.media
        .filter((m: Media) => m.type?.startsWith("image"))
        .map((img: Media) => ({
          url: img.url,
          width: 1200,
          height: 630,
          alt: img.text || "",
        }))
    : [];
  const url = subcategorySlug
    ? `${process.env.NEXT_PUBLIC_URL}/${categorySlug}/${subcategorySlug}/${article.slug}`
    : `${process.env.NEXT_PUBLIC_URL}/${categorySlug}/${article.slug}`;
  const jsonLd: WithContext<NewsArticle> = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    headline: article.title,
    description: article.description,
    url,
    datePublished: article.publishedDate,
    dateModified: article.updatedAt,
    keywords: article.tags?.map((tag: { slug: string }) => tag.slug) || [],
    isAccessibleForFree: true,
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": url,
    },
  };
  if (images.length > 0) {
    jsonLd.image = images.map((img) => ({
      "@type": "ImageObject",
      url: img.url,
    }));
  }
  if (article.authors && article.authors.length > 0) {
    jsonLd.author = article.authors.map((author) => ({
      "@type": "Person",
      name: author.name,
      url: `${process.env.NEXT_PUBLIC_URL}/authors/${author.slug}`,
    }));
  }
  return jsonLd;
}

export function buildWebPageJsonLd(
  title: string,
  urlPath: string,
  description?: string,
): WithContext<WebPage> {
  const jsonLd: WithContext<WebPage> = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    headline: title,
    description: description,
    url: process.env.NEXT_PUBLIC_URL + urlPath,
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": process.env.NEXT_PUBLIC_URL + urlPath,
    },
    publisher: {
      "@type": "NewsMediaOrganization",
      name: "EWTN",
    },
  };
  return jsonLd;
}

export function buildWebsitePageJsonLd(
  title: string,
  urlPath: string,
  description?: string,
): WithContext<WebPage> {
  const jsonLd: WithContext<WebPage> = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: title,
    description: description,
    url: process.env.NEXT_PUBLIC_URL + urlPath,
    isPartOf: {
      "@type": "WebSite",
      "@id": process.env.NEXT_PUBLIC_URL + urlPath,
    },
    publisher: {
      "@type": "NewsMediaOrganization",
      name: "EWTN",
    },
  };
  return jsonLd;
}

export function buildAuthorJsonLd(
  name: string,
  slug: string,
  description?: string,
  image?: string,
): WithContext<Person> {
  const jsonLd: WithContext<Person> = {
    "@context": "https://schema.org",
    "@type": "Person",
    name: name,
    url: `${process.env.NEXT_PUBLIC_URL}/authors/${slug}`,
  };
  if (description) {
    jsonLd.description = description;
  }
  if (image) {
    jsonLd.image = {
      "@type": "ImageObject",
      url: image,
    };
  }
  return jsonLd;
}

export function buildTagJsonLd(
  title: string,
  slug: string,
  description?: string,
): WithContext<CollectionPage> {
  const jsonLd: WithContext<CollectionPage> = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: title,
    url: `${process.env.NEXT_PUBLIC_URL}/tags/${slug}`,
    description: description,
  };
  if (description) {
    jsonLd.description = description;
  }
  return jsonLd;
}

export function buildVideoPageJsonLd(
  title: string,
  urlPath: string,
  embedUrl: string,
  thumbnailUrl: string,
  uploadDate: string,
  duration: string,
  description?: string,
): WithContext<VideoObject> {
  const jsonLd: WithContext<VideoObject> = {
    "@context": "https://schema.org",
    "@type": "VideoObject",
    name: title,
    description,
    url: `${process.env.NEXT_PUBLIC_URL}${urlPath}`,
    embedUrl,
    thumbnailUrl,
    uploadDate,
    duration,
  };
  return jsonLd;
}
