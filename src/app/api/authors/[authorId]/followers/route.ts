import { NextRequest } from "next/server";
import {
  addFollower,
  getFollowerCount,
  getUserFollows,
  removeFollower,
} from "@/app/api/services/authors/followers";
import {
  unauthorizedResponse,
  badRequestResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { getAuth0UserId, extractParam } from "@/app/api/util";

/**
 * Handles the POST request to follow an author.
 *
 * @param params.authorId - The ID of the author to follow.
 * @returns A JSON response containing a success or error message.
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ authorId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const authorId = await extractParam(params, "authorId");
    if (!authorId) {
      return badRequestResponse("Missing authorId in request params");
    }

    await addFollower(auth0UserId, authorId);
    return successResponse("Author followed", 201);
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the GET request to retrieve follower information for a specific author.
 *
 * @param params.authorId - The ID of the author for which to retrieve follower information.
 * @returns A JSON response containing the follower count and whether the user follows the author.
 *
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ authorId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    const authorId = await extractParam(params, "authorId");
    if (!authorId) {
      return badRequestResponse("Missing authorId in request params");
    }
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const [countData, userData] = await Promise.all([
      getFollowerCount(auth0UserId),
      getUserFollows(auth0UserId, authorId),
    ]);

    return successResponseWithData(
      {
        followerCount: countData?.follower_count || 0,
        userFollows: !!userData,
      },
      200,
    );
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the DELETE request to remove a saved author.
 *
 * @param params.authorId - The ID of the author.
 * @returns A JSON response containing a success or error message.
 *
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ authorId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const authorId = await extractParam(params, "authorId");
    if (!authorId) {
      return badRequestResponse("Missing authorId in request params");
    }

    await removeFollower(auth0UserId, authorId);
    return successResponse("Author follower removed", 200);
  } catch (error) {
    return errorResponse(error);
  }
}
