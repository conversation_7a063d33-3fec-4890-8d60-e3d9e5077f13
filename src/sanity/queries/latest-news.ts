import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const latestNewsQuery = defineQuery(`
{
  "articles": *[
    _type in ["dailyStoryArticle", "liveBlogArticle"] && 
    agency == '${workspace}' && 
    !(_id in path("drafts.**"))
  ]
    | order(dateTime(_updatedAt) desc) [0...10]{  
      title,  
      "slug": slug.current,
      description,  
      "category": category-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "media": featuredMedia[0] {
      "url": secure_url,
      "type": resource_type
    },
    "updated_at": dateTime(_updatedAt),  
      authors[]->{_id, "name": fullName, "slug": slug.current},
    },
  "articlesCount": count(
    *[
      _type in ["dailyStoryArticle", "liveBlogArticle"] && 
      agency == '${workspace}' && 
      !(_id in path("drafts.**"))
    ]
  )
}
`);

export const latestArticlesHomePageQuery = defineQuery(`
  *[
    _type == "dailyStoryArticle" &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    defined(publishedDate)
  ] | order(publishedDate desc)[0...10] {
    "id": _id,
    title,
    "slug": slug.current,
    "isLive": false,
    description,
    publishedDate,
    "sortDate": publishedDate,
    "category": category->{
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory->{
      "id": _id,
      title,
      "slug": slug.current
    },
    "updated_at": _updatedAt,
    authors[]->{
      _id,
      "name": fullName,
      "slug": slug.current,
      image { "url": secure_url }
    }
  }
`);

export const latestLiveBlogsHomePageQuery = defineQuery(`
  *[
    _type == "liveBlogArticle" &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    defined(_updatedAt)
  ] | order(_updatedAt desc)[0...10] {
    "id": _id,
    title,
    "slug": slug.current,
    "isLive": true,
    description,
    publishedDate,
    "sortDate": _updatedAt,
    "category": category->{
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory->{
      "id": _id,
      title,
      "slug": slug.current
    },
    "updated_at": _updatedAt,
    authors[]->{
      _id,
      "name": fullName,
      "slug": slug.current,
      image { "url": secure_url }
    }
  }
`);

export const moreLatestNewsQuery = defineQuery(`
{
  "items": *[
    _type in ["dailyStoryArticle", "liveBlogArticle"] &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**"))
  ]  
    | order(dateTime(_updatedAt) desc) [$start...$end]{  
      title,  
      "slug": slug.current,
      description,  
      "category": category-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "media": featuredMedia[0] {
      "url": secure_url,
      "type": resource_type
    },
    "updated_at": dateTime(_updatedAt),  
      authors[]->{_id, "name": fullName, "slug": slug.current},
    },
  "totalCount": count(
    *[
      _type in ["dailyStoryArticle", "liveBlogArticle"] &&
      agency == '${workspace}' 
      && !(_id in path("drafts.**"))
    ]
  )
}
`);

type Article = {
  updated_at: string;
  title: string;
  slug: string;
  description: string;
  category: {
    id: string;
    title: string;
    slug: string;
  };
  subcategory: {
    id: string;
    title: string;
    slug: string;
  };
  media: {
    url: string;
    type: string;
    created_at: string;
  };
  authors: {
    _id: string;
    name: string;
    slug: string;
    image?: {
      url: string;
    };
  }[];
};

type HomePageArticle = {
  updated_at: string;
  title: string;
  slug: string;
  description: string;
  isLive: boolean;
  id: string;
  publishedDate: string;
  sortDate: string;
  category: {
    id: string;
    title: string;
    slug: string;
  };
  subcategory: {
    id: string;
    title: string;
    slug: string;
  };
  authors: {
    _id: string;
    name: string;
    slug: string;
    image?: {
      url: string;
    };
  }[];
};

type LatestNewsHomePageResponse = {
  articles: HomePageArticle[];
};

type LatestNewsResponse = {
  articlesCount: number;
  articles: Article[];
};

export type {
  Article,
  LatestNewsResponse,
  LatestNewsHomePageResponse,
  HomePageArticle,
};
