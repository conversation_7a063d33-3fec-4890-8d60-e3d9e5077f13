"use client";

import React, {
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
} from "react";

export interface VideoPlayerProps {
  src: string;
  onPause?: () => void;
  controls?: boolean;
  loop?: boolean;
  playing?: boolean;
  pauseOnClickOutside?: boolean;
  hideFullscreen?: boolean;
  className?: string;
}

const VideoPlayer = forwardRef<HTMLVideoElement, VideoPlayerProps>(
  (
    {
      src,
      onPause,
      controls = true,
      loop = false,
      playing = false,
      pauseOnClickOutside = true,
      hideFullscreen = false,
      className = "",
    },
    ref,
  ) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const videoRef = useRef<HTMLVideoElement>(null);

    // Expose videoRef to parent via forwarded ref
    useImperativeHandle(ref, () => videoRef.current as HTMLVideoElement, []);

    const handleClickOutside = (event: MouseEvent) => {
      if (
        pauseOnClickOutside &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        if (videoRef.current && !videoRef.current.paused) {
          videoRef.current.pause();
        }
      }
    };

    useEffect(() => {
      if (videoRef.current) {
        if (playing) {
          videoRef.current
            .play()
            .catch((err) => console.error("Error al reproducir video:", err));
        }
      }
    }, []);

    useEffect(() => {
      if (pauseOnClickOutside) {
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
          document.removeEventListener("mousedown", handleClickOutside);
        };
      }
    }, [pauseOnClickOutside]);

    useEffect(() => {
      if (videoRef.current) {
        if (playing && videoRef.current.paused) {
          videoRef.current
            .play()
            .catch((err) => console.error("Error al reproducir video:", err));
        } else if (!playing && !videoRef.current.paused) {
          videoRef.current.pause();
        }
      }
    }, [playing]);

    const handlePause = () => {
      if (onPause) onPause();
    };

    return (
      <div ref={containerRef} className="w-full h-full">
        <video
          ref={videoRef}
          src={src}
          controls={controls}
          loop={loop}
          autoPlay={playing}
          muted={false}
          onPause={handlePause}
          controlsList="nodownload"
          data-testid="videoPlayer"
          width="100%"
          height="100%"
          className={`w-full h-full ${className}`}
        />
        {hideFullscreen && (
          <style jsx global>{`
            video::-webkit-media-controls-fullscreen-button {
              display: none !important;
            }
          `}</style>
        )}
      </div>
    );
  },
);

VideoPlayer.displayName = "VideoPlayer"; // needed when using forwardRef
export default VideoPlayer;
