"use client";

import React, { useEffect, useState } from "react";
import WhatsappButton from "./Whatsapp";
import FacebookButton from "./Facebook";
import TwitterButton from "./Twitter";
import MailButton from "./Mail";
import ClipboardButton from "./Clipboard";
import PlusShareButton from "./Plus";
import LikeButton from "./LikeButton";
import CommentButton from "./CommentButton";

type ShareButtonsProps = {
  articleTitle?: string;
  articleImageUrl?: string;
  likeCount: number | null;
  userLiked: boolean | null;
  isLoading: boolean;
  handleLikeClick: () => void;
  isLiveBlog?: boolean;
  commentCount?: number | null;
  commentCountLoading?: boolean;
  onOpenComments?: () => void;
  showCommentsButton?: boolean;
};

const ShareButtons = ({
  articleTitle,
  articleImageUrl,
  likeCount,
  userLiked,
  isLoading,
  handleLikeClick,
  isLiveBlog = false,
  commentCount,
  commentCountLoading = false,
  onOpenComments,
  showCommentsButton = false,
}: ShareButtonsProps) => {
  const [shareUrl, setShareUrl] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setShareUrl(process.env.NEXT_PUBLIC_URL + window.location.pathname);
    }
  }, []);

  if (!shareUrl) {
    return null;
  }

  return (
    <div
      className={`relative z-[40] overflow-visible flex flex-col items-center space-y-2 ${
        isLiveBlog ? "bg-grey-100 pb-6" : "sticky top-20 bg-white py-6 -mt-6"
      }`}
    >
      <FacebookButton shareUrl={shareUrl} />
      <TwitterButton shareUrl={shareUrl} />
      <MailButton shareUrl={shareUrl} />
      <WhatsappButton shareUrl={shareUrl} />
      <ClipboardButton shareUrl={shareUrl} />
      <PlusShareButton
        shareUrl={shareUrl}
        articleTitle={articleTitle}
        articleImageUrl={articleImageUrl}
      />
      <LikeButton
        likeCount={likeCount}
        userLiked={userLiked}
        isLoading={isLoading}
        handleLikeClick={handleLikeClick}
      />
      {showCommentsButton && (
        <CommentButton
          commentCount={commentCount ?? 0}
          commentCountLoading={commentCountLoading}
          onOpenComments={onOpenComments}
          isFloating
        />
      )}
    </div>
  );
};

export default ShareButtons;
