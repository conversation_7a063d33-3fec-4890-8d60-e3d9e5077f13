"use client";

import RequireLoginTooltip from "@/components/Auth/RequireLoginTooltip";
import Button from "@/components/ui/Button";
import { Author } from "@/sanity/queries/Author/author";
import { useUser } from "@auth0/nextjs-auth0";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import LatestArticles from "../../../components/Author/LatestArticles";
import About from "../../../components/Author/About";
import { imageLoaderUtility } from "@/utils/utils";

type Props = {
  author: Author;
};

export default function AuthorClient({ author }: Props) {
  const [isFollowed, setIsFollowed] = useState<boolean>(false);
  const [showNeedLogin, setShowNeedLogin] = useState<boolean>(false);
  const { user } = useUser();
  const [isFollowedCheckComplete, setIsFollowedCheckComplete] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [actualTab, setActualTab] = useState<string>("Latest");
  const latestRef = useRef<HTMLButtonElement>(null);
  const aboutRef = useRef<HTMLButtonElement>(null);
  const [showReachFollowLimitTooltip, setReachFollowLimitTooltip] =
    useState<boolean>(false);
  const [showLimitTooltipClicked, setShowLimitTooltipClicked] = useState(false);
  const [indicatorStyle, setIndicatorStyle] = useState({
    transform: "translateX(0px)",
  });

  useEffect(() => {
    const updateIndicator = () => {
      if (latestRef && latestRef.current) {
        const offset =
          actualTab === "Latest"
            ? 0
            : latestRef.current.getBoundingClientRect().width - 1;

        setIndicatorStyle({
          transform: `translateX(${offset}px)`,
        });
      }
    };

    // Update immediately
    updateIndicator();

    // Also add a resize listener
    window.addEventListener("resize", updateIndicator);
    return () => window.removeEventListener("resize", updateIndicator);
  }, [actualTab]);

  useEffect(() => {
    const checkIfAuthorIsFollowed = async () => {
      if (!user) {
        setIsFollowedCheckComplete(true);
        return;
      }

      try {
        const res = await fetch(`/api/authors/${author.id}/followers`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!res.ok) {
          console.error("Failed to fetch followed status");
          return;
        }
        const data = await res.json();
        if (data?.followerCount >= 5) {
          setReachFollowLimitTooltip(true);
        } else {
          setReachFollowLimitTooltip(false);
        }

        setIsFollowed(data?.userFollows ?? false);
      } catch (err) {
        console.error("Error checking bookmark:", err);
      } finally {
        setIsFollowedCheckComplete(true);
      }
    };

    checkIfAuthorIsFollowed();
  }, [user, author.id]);

  const handleFollow = async () => {
    if (!user) {
      setShowNeedLogin(true);
      return;
    }

    try {
      const response = await fetch(`/api/authors/${author.id}/followers`, {
        method: isFollowed ? "DELETE" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const data = await response.json();

        if (
          response.status === 400 ||
          data?.error?.toLowerCase().includes("limit")
        ) {
          setShowLimitTooltipClicked(true);
        } else {
          console.error("Failed to update followers status", data?.error);
        }

        return;
      }

      setIsFollowed(!isFollowed);
    } catch (error) {
      console.error("Error updating follow:", error);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setShowLimitTooltipClicked(false);
      }
    };

    if (showLimitTooltipClicked) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showLimitTooltipClicked]);

  return (
    <div className="container mx-auto p-4">
      <header>
        <nav className="mt-2">
          <ol className="flex items-center gap-2 text-[13px] lg:text-[14px]">
            <li className="after:content-['·'] after:ml-2">
              <Link href="/">Home</Link>
            </li>
            <li>{author.name}</li>
          </ol>
        </nav>

        <div className="flex flex-col items-center lg:items-start lg:flex-row lg:gap-8 pt-6 lg:pt-8">
          {author?.image && (
            <figure className="relative w-32 h-32 lg:w-[137px] lg:h-[137px] rounded-full overflow-hidden flex-none">
              <Image
                loader={imageLoaderUtility}
                src={author.image.url}
                alt={author.name}
                fill
              />
            </figure>
          )}

          <div className="flex flex-col items-center lg:items-start">
            <div className="flex flex-col items-center lg:flex-row lg:gap-4 lg:items-end">
              <h1 className="font-titles font-semibold text-[32px] lg:text-[40px] leading-none mt-4 lg:mt-0 mb-4 lg:mb-0">
                {author.name}
              </h1>

              <div className="relative z-20" ref={wrapperRef}>
                {isFollowedCheckComplete && (
                  <>
                    <Button
                      variant="outlinedBlack"
                      size="xs"
                      className={`font-normal ${isFollowed ? "!border-black !bg-black !text-white" : ""}`}
                      onClick={() => {
                        if (showReachFollowLimitTooltip && !isFollowed) {
                          setShowLimitTooltipClicked(true);
                          return;
                        }
                        setShowLimitTooltipClicked(false);
                        if (user) {
                          handleFollow();
                        } else {
                          setShowNeedLogin(true);
                        }
                      }}
                    >
                      {isFollowed ? "Following" : "Follow +"}
                    </Button>

                    {showLimitTooltipClicked && !isFollowed && (
                      <div
                        ref={tooltipRef}
                        className="absolute top-full mt-2 left-1/2 -translate-x-1/2 w-[240px] bg-black text-white text-sm p-3 rounded-xl z-30 shadow-lg"
                      >
                        <div className="text-center font-semibold text-[15px]">
                          You’ve reached the limit of 5 followed authors.
                        </div>
                        <div className="mt-1 text-center">
                          Unfollow one to follow a new author.
                        </div>
                        <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-black" />
                      </div>
                    )}
                  </>
                )}
                <RequireLoginTooltip
                  show={showNeedLogin}
                  centerOnMobile={true}
                  onHideNeedLogin={(show: boolean) => setShowNeedLogin(show)}
                />
              </div>
            </div>

            <div className="flex items-center gap-2 mt-4 text-[12px]">
              {author.jobTitle && (
                <div className="after:content-['·'] after:ml-2">
                  {author.jobTitle}
                </div>
              )}
              <div>{author.location}</div>
            </div>

            <p className="mt-4 text-[16px] lg:text-[18px]">{author.longBio}</p>
          </div>
        </div>
      </header>

      <div className="mt-6 flex justify-center lg:justify-start">
        <div className="relative border border-grey-200 rounded-[20px] flex text-[14px]">
          <button
            ref={latestRef}
            className={`pl-6 pr-5 py-2`}
            onClick={() => setActualTab("Latest")}
          >
            Latest
          </button>
          {author.about && (
            <button
              ref={aboutRef}
              className={`pr-6 pl-5 py-2`}
              onClick={() => setActualTab("About me")}
            >
              About me
            </button>
          )}
          <div
            className="absolute top-0 bottom-0 text-white flex items-center bg-black rounded-[20px] transition-transform duration-200 px-6"
            style={{ transform: indicatorStyle.transform }}
          >
            {actualTab}
          </div>
        </div>
      </div>

      <div className="lg:grid lg:grid-cols-12 lg:gap-4 mt-4 pt-4 border-t border-grey-200">
        <div className="lg:col-span-9">
          {actualTab == "Latest" ? (
            <LatestArticles author={author} />
          ) : (
            <About author={author} />
          )}
        </div>
      </div>
    </div>
  );
}
