import { render, screen } from "@testing-library/react";
import { Trending } from "@/components/HomePage/Trending";
import "@testing-library/jest-dom";
import { ImageProps } from "next/image";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
import { Article as TrendingArticle } from "@/sanity/queries/Trending/trending";

// Mock next/image to prevent Next.js-specific errors in tests
jest.mock("next/image", () => ({
  __esModule: true,
  default: ({ alt, src }: ImageProps & { src: string | StaticImport }) => (
    <img alt={alt} src={src as string} />
  ), // Use ImageProps from next/image for proper typing
}));

const generalTrendingArticles: TrendingArticle[] = [
  {
    id: "1",
    title: "Main Trending Article",
    slug: "main-trending-article",
    created_at: "2023-10-01",
    updated_at: "2023-10-02",
    publishedDate: "2023-10-03",
    excerpt: "This is the main trending article.",
    category: {
      id: "cat1",
      title: "Category 1",
      slug: "category-1",
      description: "Description of Category 1",
    },
    subcategory: {
      id: "subcat1",
      title: "Subcategory 1",
      slug: "subcategory-1",
    },
    tags: [],
    media: [
      {
        url: "/path/to/image.jpg",
        type: "image/jpeg",
        text: "Image description",
      },
    ],
    authors: [
      {
        slug: "author1",
        name: "Author One",
      },
    ],
  },
  {
    id: "2",
    title: "Trending Article 2",
    slug: "trending-article-2",
    created_at: "2023-10-01",
    updated_at: "2023-10-02",
    publishedDate: "2023-10-03",
    excerpt: "This is the second trending article.",
    category: {
      id: "cat2",
      title: "Category 2",
      slug: "category-2",
      description: "Description of Category 2",
    },
    subcategory: {
      id: "subcat2",
      title: "Subcategory 2",
      slug: "subcategory-2",
    },
    tags: [],
    media: [
      {
        url: "/path/to/image2.jpg",
        type: "image/jpeg",
        text: "Image description 2",
      },
    ],
    authors: [
      {
        slug: "author2",
        name: "Author Two",
      },
    ],
  },
  {
    id: "3",
    title: "Trending Article 3",
    slug: "trending-article-3",
    created_at: "2023-10-01",
    updated_at: "2023-10-02",
    publishedDate: "2023-10-03",
    excerpt: "This is the third trending article.",
    category: {
      id: "cat3",
      title: "Category 3",
      slug: "category-3",
      description: "Description of Category 3",
    },
    subcategory: {
      id: "subcat3",
      title: "Subcategory 3",
      slug: "subcategory-3",
    },
    tags: [],
    media: [
      {
        url: "/path/to/image3.jpg",
        type: "image/jpeg",
        text: "Image description 3",
      },
    ],
    authors: [
      {
        slug: "author3",
        name: "Author Three",
      },
    ],
  },
  {
    id: "4",
    title: "Trending Article 4",
    slug: "trending-article-4",
    created_at: "2023-10-01",
    updated_at: "2023-10-02",
    publishedDate: "2023-10-03",
    excerpt: "This is the fourth trending article.",
    category: {
      id: "cat4",
      title: "Category 4",
      slug: "category-4",
      description: "Description of Category 4",
    },
    subcategory: {
      id: "subcat4",
      title: "Subcategory 4",
      slug: "subcategory-4",
    },
    tags: [],
    media: [
      {
        url: "/path/to/image4.jpg",
        type: "image/jpeg",
        text: "Image description 4",
      },
    ],
    authors: [
      {
        slug: "author4",
        name: "Author Four",
      },
    ],
  },
  {
    id: "5",
    title: "Trending Article 5",
    slug: "trending-article-5",
    created_at: "2023-10-01",
    updated_at: "2023-10-02",
    publishedDate: "2023-10-03",
    excerpt: "This is the fifth trending article.",
    category: {
      id: "cat5",
      title: "Category 5",
      slug: "category-5",
      description: "Description of Category 5",
    },
    subcategory: {
      id: "subcat5",
      title: "Subcategory 5",
      slug: "subcategory-5",
    },
    tags: [],
    media: [
      {
        url: "/path/to/image5.jpg",
        type: "image/jpeg",
        text: "Image description 5",
      },
    ],
    authors: [
      {
        slug: "author5",
        name: "Author Five",
      },
    ],
  },
];

describe("Trending Component", () => {
  it("renders correctly", () => {
    render(<Trending trendingArticles={generalTrendingArticles} />);
    expect(screen.getByText("TRENDING NEWS")).toBeInTheDocument();
  });

  it("displays the correct number of news items", () => {
    render(<Trending trendingArticles={generalTrendingArticles} />);
    const newsItems = screen.getAllByRole("heading", { level: 3 }); // All news titles (h3)
    expect(newsItems.length).toBe(5); // 1 main + 4 smaller ones
  });

  it("renders numbers for all news items", () => {
    render(<Trending trendingArticles={generalTrendingArticles} />);
    for (let i = 1; i <= 5; i++) {
      expect(screen.getByText(i.toString())).toBeInTheDocument();
    }
  });

  /* TO BE EXPANDED WHEN IMPLEMENTING SANITY */
});
