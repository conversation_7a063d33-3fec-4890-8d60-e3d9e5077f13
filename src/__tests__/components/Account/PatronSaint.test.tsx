import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import PatronSaint from "@/components/Account/PatronSaint";

describe("PatronSaint", () => {
  beforeEach(() => {
    render(<PatronSaint />);
  });

  describe("Initial Form State", () => {
    it("should render initial form", () => {
      expect(
        screen.getByRole("heading", { name: /Discover Your Patron/i }),
      ).toBeInTheDocument();
      expect(screen.getByText(/Tell us your birthdate/i)).toBeInTheDocument();
    });

    it("should render all three selects in correct order", () => {
      const selects = screen.getAllByRole("combobox");

      expect(selects).toHaveLength(3);

      expect(selects[0]).toHaveAttribute("aria-label", "Year");
      expect(selects[1]).toHaveAttribute("aria-label", "Month");
      expect(selects[2]).toHaveAttribute("aria-label", "Day");
    });

    it("should have Month and Day selects initially disabled", () => {
      const monthSelect = screen.getByRole("combobox", { name: /month/i });
      const daySelect = screen.getByRole("combobox", { name: /day/i });

      expect(monthSelect).toBeDisabled();
      expect(daySelect).toBeDisabled();
    });
  });

  describe("Form Interactions", () => {
    it("should enable Month select when Year is selected", async () => {
      const yearSelect = screen.getByRole("combobox", { name: /year/i });
      await userEvent.selectOptions(yearSelect, "2024");

      const monthSelect = screen.getByRole("combobox", { name: /month/i });
      expect(monthSelect).not.toBeDisabled();
    });

    it("should enable Day select when Month is selected", async () => {
      const yearSelect = screen.getByRole("combobox", { name: /year/i });
      await userEvent.selectOptions(yearSelect, "2024");

      const monthSelect = screen.getByRole("combobox", { name: /month/i });
      await userEvent.selectOptions(monthSelect, "9");

      const daySelect = screen.getByRole("combobox", { name: /day/i });
      expect(daySelect).not.toBeDisabled();
    });

    it("should calculate correct days for February in leap year", async () => {
      const yearSelect = screen.getByRole("combobox", { name: /year/i });
      await userEvent.selectOptions(yearSelect, "2024");

      const monthSelect = screen.getByRole("combobox", { name: /month/i });
      await userEvent.selectOptions(monthSelect, "2");

      const daySelect = screen.getByRole("combobox", { name: /day/i });
      const days = Array.from(daySelect.getElementsByTagName("option")).filter(
        (option) => option.value !== "",
      );

      expect(days).toHaveLength(29);
    });
  });

  describe("Result View", () => {
    const completeDateSelection = async () => {
      const yearSelect = screen.getByRole("combobox", { name: /year/i });
      await userEvent.selectOptions(yearSelect, "2024");

      const monthSelect = screen.getByRole("combobox", { name: /month/i });
      await userEvent.selectOptions(monthSelect, "9");

      const daySelect = screen.getByRole("combobox", { name: /day/i });
      await userEvent.selectOptions(daySelect, "14");

      const submitButton = screen.getByRole("button", {
        name: /discover your patron saint/i,
      });
      await userEvent.click(submitButton);
    };

    it("should display saint information after form submission", async () => {
      await completeDateSelection();

      const description = screen.getByTestId("saint-description");
      expect(description).toBeInTheDocument();
    });

    it("should render audio player", async () => {
      await completeDateSelection();

      expect(screen.getByTestId("play")).toBeInTheDocument();
    });
  });
});
