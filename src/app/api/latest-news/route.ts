import { sanityFetch } from "@/sanity/live";
import { moreLatestNewsQuery } from "@/sanity/queries/latest-news";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");

    const validPage = isNaN(page) ? 1 : page;
    const validPageSize = isNaN(pageSize) ? 10 : pageSize;

    const start = (validPage - 1) * validPageSize;
    const end = start + validPageSize;

    const response = await sanityFetch({
      query: moreLatestNewsQuery,
      params: {
        start,
        end,
      },
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching articles:", error);

    return NextResponse.json(
      {
        error: "Error fetching articles",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
