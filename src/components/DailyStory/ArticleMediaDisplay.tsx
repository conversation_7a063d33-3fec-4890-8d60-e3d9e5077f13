import { useState, useRef, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import VideoPlayer from "@/components/ui/VideoPlayer";
import { Icon } from "@/components/Icon";
import type { Swiper as SwiperType } from "swiper";
import { Media } from "@/sanity/queries/dailyStory";

type ArticleMediaDisplayProps = {
  title: string;
  articleMedia: Media[];
  isLiveBlog?: boolean;
  showAsList?: boolean;
};

export default function ArticleMediaDisplay({
  title,
  articleMedia,
  isLiveBlog,
  showAsList,
}: ArticleMediaDisplayProps) {
  const [actualSwiperItem, setActualSwiperItem] = useState<number>(1);
  const [mediaExpanded, setMediaExpanded] = useState(false);
  const [videoIsPlaying, setVideoIsPlaying] = useState(false);
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [initialSlide, setInitialSlide] = useState<number>(0);

  const prevRef = useRef<HTMLButtonElement>(null);
  const nextRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (swiper && swiper.params.navigation) {
      if (typeof swiper.params.navigation === "object") {
        swiper.params.navigation.prevEl = prevRef.current;
        swiper.params.navigation.nextEl = nextRef.current;
        swiper.navigation.init();
        swiper.navigation.update();
      }
    }
  }, [swiper]);

  return (
    <div
      className={
        mediaExpanded
          ? "swiper-fullscreen fixed top-0 left-0 right-0 bottom-0 z-[100] flex justify-center items-center bg-black"
          : "relative aspect-[16/9] -mx-4 sm:mx-0"
      }
    >
      <div
        className={
          mediaExpanded
            ? "h-full box-border pt-20 pb-60 lg:pb-32 container mx-auto relative"
            : ""
        }
      >
        <div className={mediaExpanded ? "lg:px-20" : ""}>
          {mediaExpanded ? (
            <button
              aria-label="Collapse media"
              className="absolute top-8 right-4 lg:right-0 w-8 h-8 rounded-full text-white border border-grey-300 flex items-center justify-center group transition-all hover:border-white cursor-pointer z-50"
              onClick={() => {
                setMediaExpanded(false);
                setInitialSlide(0);
              }}
            >
              <Icon
                icon="collapseMediaSquare"
                className="w-4 text-grey-300 transition-all group-hover:text-white"
              />
            </button>
          ) : null}

          {showAsList && !mediaExpanded ? (
            <div className="flex flex-col gap-8">
              {articleMedia.map((media, index) => (
                <div key={index} className="relative w-full pb-6">
                  <div className="relative aspect-[16/9] w-full overflow-hidden rounded-xl">
                    {media.type === "video" ? (
                      <VideoPlayer
                        loop
                        src={media.url}
                        controls
                        hideFullscreen
                        playing={false}
                      />
                    ) : (
                      <Image
                        src={media.url}
                        alt={media.text || ""}
                        fill
                        priority
                        className="object-cover"
                      />
                    )}
                    <button
                      onClick={() => {
                        setInitialSlide(index);
                        setActualSwiperItem(index + 1);
                        setMediaExpanded(true);
                      }}
                      className="absolute top-2 right-2 w-8 h-8 rounded-md bg-black/70 flex items-center justify-center z-10"
                      aria-label="Expand media"
                    >
                      <Icon
                        icon="expandMediaSquare"
                        className="w-4 text-white"
                      />
                    </button>
                  </div>
                  {media.text && (
                    <p className="mt-2 text-[18px] text-grey-600">
                      {media.text}
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <>
              {!showAsList && !mediaExpanded && (
                <button
                  onClick={() => {
                    setInitialSlide(actualSwiperItem - 1);
                    setMediaExpanded(true);
                  }}
                  className="absolute top-2 right-2 z-10 w-8 h-8 rounded-md bg-black/70 flex items-center justify-center"
                  aria-label="Expand media"
                >
                  <Icon icon="expandMediaSquare" className="w-4 text-white" />
                </button>
              )}

              <Swiper
                onSwiper={(swiper) => setSwiper(swiper)}
                modules={[Navigation]}
                navigation={{
                  prevEl: prevRef.current,
                  nextEl: nextRef.current,
                }}
                initialSlide={initialSlide}
                loop={false}
                slidesPerView={1}
                centeredSlides={false}
                onSlideChange={(swiper) => {
                  setActualSwiperItem(swiper.activeIndex + 1);
                  setVideoIsPlaying(false);
                }}
                className="dailyStory-swipe sm:rounded-xl overflow-hidden"
              >
                {articleMedia.map((media, index) => (
                  <SwiperSlide key={index}>
                    <div>
                      <div
                        className={`relative w-full overflow-hidden ${
                          mediaExpanded
                            ? "h-[calc(100vh-20rem)] lg:h-[calc(100vh-13rem)] flex justify-center items-center"
                            : "aspect-[16/9]"
                        }`}
                      >
                        {media?.type == "video" ? (
                          <button
                            className="w-full outline-none block"
                            aria-label="Play video"
                            onClick={() =>
                              !videoIsPlaying && setVideoIsPlaying(true)
                            }
                          >
                            <VideoPlayer
                              loop={true}
                              src={media.url}
                              controls={videoIsPlaying}
                              pauseOnClickOutside={true}
                              hideFullscreen={true}
                              playing={
                                videoIsPlaying && actualSwiperItem === index + 1
                              }
                            />

                            {!videoIsPlaying && (
                              <div className="absolute bottom-2 lg:bottom-4 left-2 lg:left-4 w-6 lg:w-9 h-6 lg:h-9 rounded-md lg:rounded-xl bg-black/70 flex items-center justify-center">
                                <Icon icon="play" className="w-4" />
                              </div>
                            )}
                          </button>
                        ) : (
                          <Image
                            src={media.url}
                            alt={title}
                            fill
                            priority
                            className={`object-cover ${
                              mediaExpanded ? "!h-full" : ""
                            }`}
                          />
                        )}
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </>
          )}
          {(mediaExpanded || !showAsList) && (
            <div
              className={`flex items-center gap-4 mt-4 px-4 
            ${isLiveBlog ? "border-b-0" : ""}
            ${
              mediaExpanded
                ? "flex-col relative lg:px-0"
                : "text-grey-400 border-b-0 md:border-b border-grey-300 pb-4 mb-4 sm:px-0"
            }`}
            >
              <div
                className={`flex items-center gap-2 ${
                  mediaExpanded ? "justify-end" : ""
                }`}
              >
                <button
                  aria-label="Previous media"
                  ref={prevRef}
                  className={`border w-8 h-8 rounded-full transition-all border-grey-300
                  disabled:opacity-50 disabled:hover:!border-grey-300
                  ${
                    mediaExpanded
                      ? "text-white order-2 hover:border-white lg:absolute lg:-left-20 lg:-top-[(calc(100vh-10rem)/2)] z-50"
                      : isLiveBlog
                        ? "hover:border-white text-white"
                        : "hover:border-black"
                  }
                  flex items-center justify-center`}
                >
                  <Icon className="!text-[20px]" icon="arrow_back" />
                </button>

                <div
                  className={`whitespace-nowrap 
                ${isLiveBlog ? "text-grey-300" : "text-grey-400"}
                ${
                  mediaExpanded
                    ? "text-white order-1 absolute left-4 lg:left-0 lg:mt-4"
                    : "text-[12px]"
                }`}
                >
                  {actualSwiperItem} / {articleMedia.length}
                </div>

                <button
                  aria-label="Next media"
                  ref={nextRef}
                  className={`border w-8 h-8 rounded-full transition-all border-grey-300
                  disabled:opacity-50 disabled:hover:!border-grey-300
                  ${
                    mediaExpanded
                      ? "text-white order-2 hover:border-white lg:absolute lg:-right-20 lg:-top-[(calc(100vh-10rem)/2)] z-50"
                      : isLiveBlog
                        ? "hover:border-white text-white"
                        : "hover:border-black"
                  }
                  flex items-center justify-center`}
                >
                  <Icon className="!text-[20px]" icon="arrow_forward" />
                </button>
              </div>

              <div className={mediaExpanded ? "w-full" : ""}>
                {articleMedia.map((media, index) => (
                  <div
                    key={index}
                    className={`${
                      isLiveBlog ? "text-grey-300" : "text-grey-400"
                    } ${
                      mediaExpanded ? "text-white mt-1 lg:mt-4" : "text-[14px]"
                    } ${actualSwiperItem === index + 1 ? "flex" : "hidden"}`}
                  >
                    {media.text}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
