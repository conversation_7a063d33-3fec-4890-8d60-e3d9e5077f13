import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import Interest from "@/app/account/interests/interests";
import { SearchTag } from "@/sanity/queries/Tag/tag";
import { Article } from "@/app/api/articles/bookmarks/route";
import { MultiSelectProps } from "@/components/ui/Form/MultiSelect/types";
import { PaginationProps } from "@/components/ui/Pagination/types";

jest.mock("@/components/ui/Form/MultiSelect", () => {
  return function MockMultiSelect({
    selectedOptions,
    onSelect,
    onRemove,
  }: Pick<MultiSelectProps, "selectedOptions" | "onSelect" | "onRemove">) {
    return (
      <div data-testid="mock-multiselect">
        <button onClick={() => onSelect("tag1")}>Select Topic</button>
        <button onClick={() => onRemove("tag1")}>Remove Topic</button>
        <div>Selected count: {selectedOptions.length}</div>
      </div>
    );
  };
});

jest.mock("@/components/ui/Pagination", () => {
  return function MockPagination({
    onPageChange,
  }: Pick<PaginationProps, "onPageChange">) {
    return (
      <div data-testid="mock-pagination">
        <button onClick={() => onPageChange(2)}>Next Page</button>
      </div>
    );
  };
});

jest.mock("@/components/Account/Interests/newsCard", () => {
  return function MockNewsCard({ item }: { item: Article }) {
    return <div data-testid="mock-news-card">{item.title}</div>;
  };
});

const mockTags: SearchTag[] = [
  { id: "tag1", name: "News" },
  { id: "tag2", name: "Tech" },
];

const mockArticles: Article[] = [
  {
    id: "1",
    title: "Article 1",
    slug: "article-1",
    category: {
      id: "",
      slug: "test",
      title: "Category",
      description: "Category description",
    },
    media: {
      url: "https://example.com/image.jpg",
      type: "image",
    },
    authors: [],
    updated_at: "",
    subcategory: null,
  },
  {
    id: "2",
    title: "Article 2",
    slug: "article-2",
    category: {
      id: "",
      slug: "test",
      title: "Category",
      description: "Category description",
    },
    media: {
      url: "https://example.com/image.jpg",
      type: "image",
    },
    authors: [],
    updated_at: "",
    subcategory: null,
  },
];

// Mock fetch
(global.fetch as jest.Mock) = jest.fn((url: string) => {
  if (url === "/api/tags/followers") {
    return Promise.resolve({
      json: () => Promise.resolve({ followedTagIds: ["tag1"] }),
    });
  }

  if (url === "/api/articles/by-tags") {
    return Promise.resolve({
      json: () => Promise.resolve({ items: mockArticles }),
    });
  }

  return Promise.reject("not found");
});

describe("Interest Component", () => {
  it("handles topic selection", async () => {
    render(<Interest tags={mockTags} />);
    await waitFor(() => screen.getByTestId("mock-multiselect"));

    fireEvent.click(screen.getByText("Select Topic"));

    await waitFor(() => {
      expect(screen.getAllByTestId("mock-news-card")).toHaveLength(2);
    });
  });

  it("handles topic removal", async () => {
    render(<Interest tags={mockTags} />);
    await waitFor(() => screen.getByTestId("mock-multiselect"));

    fireEvent.click(screen.getByText("Remove Topic"));

    await waitFor(() => {
      // When all tags are removed, articles should disappear
      expect(screen.queryAllByTestId("mock-news-card")).toHaveLength(0);
    });
  });
});
