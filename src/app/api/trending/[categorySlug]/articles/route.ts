import { NextRequest, NextResponse } from "next/server";
import { fetchTrendingArticlesFromAnalytics } from "../..";
import { sanityFetch } from "@/sanity/client";
import { workspace } from "@/sanity/env";

export async function GET(
  request: NextRequest,
  {
    params,
  }: {
    params: Promise<{
      categorySlug: string;
      subcategorySlug?: string;
    }>;
  },
) {
  try {
    const url = new URL(request.url);

    const { categorySlug, subcategorySlug } = await params;

    const listSize = parseInt(url.searchParams.get("listSize") || "5");

    const withSubcategories = parseInt(
      url.searchParams.get("withSubcategories") || "0",
    );

    if (withSubcategories) {
      const subcategories: { slug: string }[] = await sanityFetch({
        query: `*[_type == "subcategory" && agency == '${workspace}' && defined(slug.current) && category->slug.current == $categorySlug] {
          "id": _id,
          "title": title,
          "slug": slug.current,
        }`,
        params: {
          categorySlug,
        },
      });
      const response = await Promise.all(
        subcategories.map(async (subcategory: { slug: string }) => {
          const trendingArticleBySubcategory =
            await fetchTrendingArticlesFromAnalytics({
              listSize,
              categorySlug,
              subcategorySlug: subcategory.slug,
              periodEnd: undefined,
              periodStart: undefined,
            });
          return {
            ...subcategory,
            articles: trendingArticleBySubcategory,
            articlesCount: trendingArticleBySubcategory.length,
          };
        }),
      );

      return NextResponse.json(response);
    } else {
      const trendingNews = await fetchTrendingArticlesFromAnalytics({
        listSize,
        categorySlug,
        subcategorySlug: subcategorySlug || null,
        periodEnd: undefined,
        periodStart: undefined,
      });

      return NextResponse.json(trendingNews);
    }
  } catch (error) {
    console.error("Error fetching articles:", error);

    return NextResponse.json(
      {
        error: "Error fetching articles",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
