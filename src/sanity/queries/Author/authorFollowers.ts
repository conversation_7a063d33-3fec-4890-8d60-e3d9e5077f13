import { workspace } from "@/sanity/env";

export const authorFollowersQuery = `
*[
  _type == "author" && 
  agency == '${workspace}' &&
  !(_id in path("drafts.**")) &&
  _id in $authorIds
][] {
  "id": _id,
  "name": fullName,
  "jobTitle": jobTitle->name,
  "slug": slug.current
}
`;

export const articlesByAuthorsQuery = `
*[
  _type == "dailyStoryArticle" &&
  agency == '${workspace}' &&
  !(_id in path("drafts.**")) &&
  references($authorsIds) &&
  count(authors[@._ref in $authorsIds]) > 0
] | order(publishedDate desc)[0...20] {
  "id": _id,
  title,
  "slug": slug.current,
  publishedDate,
  description,
  "category": category-> {
    "id": _id,
    title,
    "slug": slug.current
  },
  "media": featuredMedia[0] {
    "url": secure_url,
    "type": resource_type
  },
  "authors": authors[]-> {
    "slug": slug.current,
    "name": fullName,
    image {
      "url": secure_url
    }
  },
  "created_at": _createdAt,
  "updated_at": dateTime(_updatedAt),
  "subcategory": subcategory-> {
    "id": _id,
    title,
    "slug": slug.current
  }
}
`;

export type Author = {
  id: string;
  name: string;
  jobTitle: string;
  location: string;
  slug: string;
};
