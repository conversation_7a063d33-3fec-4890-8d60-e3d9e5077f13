import { Article } from "@/sanity/queries/Trending/trending";
import { getArticleUrl, truncateTitle } from "@/utils/utils";
import Link from "next/link";

export const TrendingBar = ({
  trendingArticles,
}: {
  trendingArticles: Article[];
}) => {
  if (!trendingArticles || trendingArticles.length === 0) {
    return null;
  }

  const top5Trending = trendingArticles.slice(0, 5);

  const itemsWithSeparators = top5Trending.flatMap((article, index) => {
    const articleItem = (
      <li key={`trending-article-${index}`} className="shrink-0">
        <Link
          href={getArticleUrl(
            article.category?.slug,
            article.slug,
            article.subcategory?.slug,
          )}
          className="whitespace-nowrap"
        >
          {article.truncatedTitle || truncateTitle(article.title, 25)}
        </Link>
      </li>
    );

    if (index < top5Trending.length - 1) {
      const separator = (
        <li
          key={`separator-${index}`}
          className="w-px h-4 bg-[#E0E0E0] self-center shrink-0"
        />
      );
      return [articleItem, separator];
    }

    return [articleItem];
  });

  return (
    <>
      {/* Desktop version: full scroll including "Trending" */}
      <div className="hidden lg:flex justify-center w-full overflow-x-auto scrollbar-hide">
        <ul
          className="flex items-center gap-x-4 pt-4 sm:pt-0 sm:pb-8 pb-4"
          style={{ whiteSpace: "nowrap" }}
        >
          <li className="text-[16px] font-bold shrink-0">Trending</li>
          {itemsWithSeparators}
        </ul>
      </div>

      {/* Mobile version: "Trending" fixed, only articles scroll */}
      <div className="block lg:hidden">
        <div className="flex items-center gap-4 w-full overflow-hidden pt-4 pb-4 sm:pb-8 sm:pt-0">
          <div className="text-[16px] font-bold shrink-0 pl-2">Trending</div>

          <div className="overflow-x-auto scrollbar-hide">
            <ul
              className="flex items-center gap-4 w-max pr-4"
              style={{ whiteSpace: "nowrap" }}
            >
              {itemsWithSeparators}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};
