import { useId } from "react";
import { TextareaProps } from "./types";

const Textarea = ({
  label,
  value,
  onChange,
  error,
  disabled,
  className,
  ...props
}: TextareaProps) => {
  const id = useId();
  const textareaId = `textarea-${id}`;

  return (
    <div>
      {label && (
        <label className="text-[16px]" htmlFor={textareaId}>
          {label}
        </label>
      )}

      <div className="mt-1 lg:mt-2">
        <textarea
          id={textareaId}
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className={`
            block border rounded-xl w-full pl-4 pr-8 py-2 bg-transparent appearance-none relative z-10 text-[16px] cursor-pointer
            disabled:cursor-not-allowed disabled:opacity-70
            ${typeof error === "string" || error ? "border-red-700 bg-red-100" : "border-grey-200 bg-white"}
            ${className}
          `}
          {...props}
          role="textarea"
        />
      </div>

      {typeof error === "string" && (
        <p className="mt-1 text-red-700 text-sm">{error}</p>
      )}
    </div>
  );
};

export default Textarea;
