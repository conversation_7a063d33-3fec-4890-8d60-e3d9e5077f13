import { render, screen, fireEvent } from "@testing-library/react";
import AboutUsClient from "@/app/about-us/AboutUsClient";
import { StaticTabbedPageQueryResult } from "@/sanity/queries/static-tabbed-page";

describe("AboutUsClient", () => {
  const mockPageData: StaticTabbedPageQueryResult = {
    title: "About Us",
    slug: { current: "about-us" },
    imageUrl: "/test.jpg",
    description: [
      {
        _type: "block",
        children: [
          {
            text: "Description text",
            _type: "span",
            marks: [],
            _key: "1",
          },
        ],
        _key: "block1",
      },
    ],
    content: {
      tabBody: [
        {
          title: "Tab 1",
          slug: { current: "tab-1" },
          textBody: [
            {
              _type: "block",
              children: [
                {
                  text: "Tab 1 content",
                  _type: "span",
                  marks: [],
                  _key: "2",
                },
              ],
              _key: "block2",
            },
          ],
          imageUrl: "/tab1.jpg",
        },
      ],
    },
  };

  beforeEach(() => {
    // Mock window.scrollTo
    window.scrollTo = jest.fn();
    // Clear URL hash before each test
    window.history.pushState({}, "", "#");
  });

  test("renders header", () => {
    render(<AboutUsClient pageData={mockPageData} />);
    expect(
      screen.getByRole("heading", { name: /about us/i }),
    ).toBeInTheDocument();
  });

  test("renders tab buttons", () => {
    render(<AboutUsClient pageData={mockPageData} />);
    expect(screen.getByRole("tab", { name: "Tab 1" })).toBeInTheDocument();
  });

  test("renders RichContent components", () => {
    render(<AboutUsClient pageData={mockPageData} />);
    const richContentElements = screen.getAllByTestId("rich-content");
    expect(richContentElements).toHaveLength(2);
  });

  test("handles tab button click", () => {
    render(<AboutUsClient pageData={mockPageData} />);
    const tabButton = screen.getByRole("tab", { name: "Tab 1" });

    fireEvent.click(tabButton);

    expect(window.location.hash).toBe("#tab-1");
    expect(window.scrollTo).toHaveBeenCalled();
  });
});
