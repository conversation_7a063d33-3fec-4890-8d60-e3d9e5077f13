import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ContactUs from "@/app/contact-us/page";

jest.setTimeout(60000);

describe("ContactUs Component", () => {
  beforeEach(() => {
    render(<ContactUs />);
  });

  it("renders initial form state correctly", () => {
    expect(
      screen.getByRole("textbox", { name: /^first name$/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("textbox", { name: /^last name$/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("textbox", { name: /^phone number$/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("textbox", { name: /^email$/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("textbox", { name: /^message$/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /send message/i }),
    ).toBeInTheDocument();
  });

  describe("Form Validation", () => {
    it("shows validation errors for empty fields", async () => {
      const user = userEvent.setup();

      await user.click(screen.getByRole("button", { name: /send message/i }));

      expect(
        screen.getByText("Please enter your first name"),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Please enter your last name"),
      ).toBeInTheDocument();
      expect(
        screen.getByText("Please enter your phone number"),
      ).toBeInTheDocument();
      expect(screen.getByText("Please enter your email")).toBeInTheDocument();
      expect(screen.getByText("Please enter your message")).toBeInTheDocument();
    });

    it("validates email format", async () => {
      const user = userEvent.setup();
      const emailInput = screen.getByRole("textbox", { name: /^email$/i });

      await user.type(emailInput, "invalid-email");
      await user.click(screen.getByRole("button", { name: /send message/i }));

      expect(
        screen.getByText("Please enter a valid email address"),
      ).toBeInTheDocument();
    });

    it("validates phone number format", async () => {
      const user = userEvent.setup();
      const phoneInput = screen.getByRole("textbox", {
        name: /^phone number$/i,
      });

      await user.type(phoneInput, "abc123");
      await user.click(screen.getByRole("button", { name: /send message/i }));

      expect(
        screen.getByText(
          "Phone number can only have numeric values, spaces and the following symbol: '+', '-', '(' and ')'",
        ),
      ).toBeInTheDocument();
    });

    it("validates maximum length constraints", async () => {
      const user = userEvent.setup();
      const messageInput = screen.getByRole("textbox", { name: /^message$/i });
      const longText = "a".repeat(2001);

      await user.type(messageInput, longText);
      await user.click(screen.getByRole("button", { name: /send message/i }));

      expect(
        screen.getByText("Message cannot exceed 2000 characters"),
      ).toBeInTheDocument();
    });
  });

  describe("Input Field Interactions", () => {
    it("allows valid phone number formats", async () => {
      const user = userEvent.setup();
      const validPhoneNumbers = [
        "+1 (*************",
        "************",
        "(*************",
        "+44 20 7123 4567",
      ];

      for (const phoneNumber of validPhoneNumbers) {
        await user.clear(
          screen.getByRole("textbox", { name: /^phone number$/i }),
        );
        await user.type(
          screen.getByRole("textbox", { name: /^phone number$/i }),
          phoneNumber,
        );
        await user.click(screen.getByRole("button", { name: /send message/i }));

        expect(
          screen.queryByText(/Phone number can only have/i),
        ).not.toBeInTheDocument();
      }
    });

    it("allows valid email formats", async () => {
      const user = userEvent.setup();
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      for (const email of validEmails) {
        await user.clear(screen.getByRole("textbox", { name: /^email$/i }));
        await user.type(
          screen.getByRole("textbox", { name: /^email$/i }),
          email,
        );
        await user.click(screen.getByRole("button", { name: /send message/i }));

        expect(
          screen.queryByText(/Please enter a valid email address/i),
        ).not.toBeInTheDocument();
      }
    });
  });
});
