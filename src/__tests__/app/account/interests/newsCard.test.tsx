import { render, screen } from "@testing-library/react";
import NewsCard from "@/components/Account/Interests/newsCard";

describe("NewsCard Component", () => {
  const mockNewsItem = {
    id: "1",
    title: "Article 1",
    slug: "article-1",
    category: {
      id: "",
      slug: "test",
      title: "LOREM",
      description: "Category description",
    },
    media: {
      url: "https://example.com/image.jpg",
      type: "image",
    },
    authors: [],
    updated_at: "",
    subcategory: null,
  };

  it("renders all news card elements correctly", () => {
    render(<NewsCard item={mockNewsItem} />);

    expect(screen.getByText("LOREM")).toBeInTheDocument();
    expect(screen.getByText("Article 1")).toBeInTheDocument();
  });
});
