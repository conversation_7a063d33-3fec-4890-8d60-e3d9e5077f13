import ExpandableList, {
  ExpandableListProps,
} from "@/components/ui/ExpandableList";
import { render, screen, fireEvent } from "@testing-library/react";

describe("Expandable List Component", () => {
  const defaultProps: ExpandableListProps = {
    section: {
      title: "Section Title",
      slug: {
        _type: "slug",
        current: "section-slug",
        source: "section-source",
      },
      menuItems: [
        {
          title: "Item Title",
          linkType: "internal",
          internalLink: {
            _type: "slug",
            current: "internal-link",
            source: "internal-source",
          },
          externalLink: "external-link",
        },
      ],
    },
  };

  test("renders the Expandable List component", () => {
    render(<ExpandableList {...defaultProps} />);
    const expandableListComponent = screen.getByTestId("expandable-list");
    expect(expandableListComponent).toBeInTheDocument();

    const expandableListTitle = screen.getByTestId("expandable-list-title");
    expect(expandableListTitle).toHaveTextContent("Section Title");
  });

  test("triggers the onClose callback function", () => {
    render(<ExpandableList {...defaultProps} />);
    const expandableListTitle = screen.getByTestId("expandable-list-title");
    expect(expandableListTitle).toBeInTheDocument();

    fireEvent.click(expandableListTitle);
    const expandableListItems = screen.getByTestId("expandable-list-items");
    expect(expandableListItems).toBeInTheDocument();
  });
});
