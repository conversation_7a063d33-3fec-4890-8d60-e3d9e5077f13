import { renderHook, act, waitFor } from "@testing-library/react";
import { usePagination } from "@/utils/usePagination";

global.fetch = jest.fn();

interface TestItem {
  id: number;
  title: string;
}

describe("usePagination", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockInitialItems: TestItem[] = [
    { id: 1, title: "Item 1" },
    { id: 2, title: "Item 2" },
  ];

  const mockNewItems: TestItem[] = [
    { id: 3, title: "Item 3" },
    { id: 4, title: "Item 4" },
  ];

  it("should initialize with the correct values", () => {
    const { result } = renderHook(() =>
      usePagination<TestItem>({
        initialItems: mockInitialItems,
        itemsPerPage: 2,
        totalCount: 6,
        apiUrl: "/api/items",
      }),
    );

    expect(result.current.items).toEqual(mockInitialItems);
    expect(result.current.page).toBe(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.hasMore).toBe(true);
  });

  it("should load more items when loadMore is called", async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          items: mockNewItems,
          totalCount: 6,
        },
      }),
    });

    const { result } = renderHook(() =>
      usePagination<TestItem>({
        initialItems: mockInitialItems,
        itemsPerPage: 2,
        totalCount: 6,
        apiUrl: "/api/items",
      }),
    );

    expect(result.current.items.length).toBe(2);

    await act(async () => {
      await result.current.loadMore();
    });

    expect(result.current.items.length).toBe(4);
    expect(result.current.items).toEqual([
      ...mockInitialItems,
      ...mockNewItems,
    ]);
    expect(result.current.page).toBe(2);
    expect(result.current.hasMore).toBe(true);

    expect(global.fetch).toHaveBeenCalledWith("/api/items?page=2&pageSize=2");
  });

  it("should set hasMore to false when all items are loaded", async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          items: mockNewItems,
          totalCount: 6,
        },
      }),
    });

    const finalItems: TestItem[] = [
      { id: 5, title: "Item 5" },
      { id: 6, title: "Item 6" },
    ];
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          items: finalItems,
          totalCount: 6,
        },
      }),
    });

    const { result } = renderHook(() =>
      usePagination<TestItem>({
        initialItems: mockInitialItems,
        itemsPerPage: 2,
        totalCount: 6,
        apiUrl: "/api/items",
      }),
    );

    await act(async () => {
      await result.current.loadMore();
    });

    expect(result.current.items.length).toBe(4);
    expect(result.current.hasMore).toBe(true);

    await act(async () => {
      await result.current.loadMore();
    });

    expect(result.current.items.length).toBe(6);
    expect(result.current.hasMore).toBe(false);
  });

  it("should handle API errors gracefully", async () => {
    console.error = jest.fn();

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({ message: "API Error" }),
    });

    const { result } = renderHook(() =>
      usePagination<TestItem>({
        initialItems: mockInitialItems,
        itemsPerPage: 2,
        totalCount: 6,
        apiUrl: "/api/items",
      }),
    );

    await act(async () => {
      await result.current.loadMore();
    });

    expect(result.current.items).toEqual(mockInitialItems);
    expect(result.current.page).toBe(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.hasMore).toBe(true);

    expect(console.error).toHaveBeenCalled();
  });

  it("should prevent concurrent loadMore calls when loading", async () => {
    let resolveFunction: (value: unknown) => void;
    const slowPromise = new Promise((resolve) => {
      resolveFunction = resolve;
    });

    (global.fetch as jest.Mock).mockImplementationOnce(() => {
      return slowPromise.then(() => ({
        ok: true,
        json: async () => ({
          data: {
            items: mockNewItems,
            totalCount: 6,
          },
        }),
      }));
    });

    const { result } = renderHook(() =>
      usePagination<TestItem>({
        initialItems: mockInitialItems,
        itemsPerPage: 2,
        totalCount: 6,
        apiUrl: "/api/items",
      }),
    );

    let loadMorePromise: Promise<void> | undefined;

    act(() => {
      loadMorePromise = result.current.loadMore();
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(true);
    });

    act(() => {
      result.current.loadMore();
    });

    expect(global.fetch).toHaveBeenCalledTimes(1);

    act(() => {
      resolveFunction();
    });

    if (loadMorePromise) {
      await loadMorePromise;
    }

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.items.length).toBe(4);
      expect(result.current.page).toBe(2);
    });
  });

  it("should handle empty response correctly", async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          items: [],
          totalCount: 2,
        },
      }),
    });

    const { result } = renderHook(() =>
      usePagination<TestItem>({
        initialItems: mockInitialItems,
        itemsPerPage: 2,
        totalCount: 4,
        apiUrl: "/api/items",
      }),
    );

    expect(result.current).not.toBeNull();

    let loadMorePromise: Promise<void> | undefined;

    act(() => {
      loadMorePromise = result.current.loadMore();
    });

    if (loadMorePromise) {
      await loadMorePromise;
    }

    await waitFor(() => {
      expect(result.current.items).toEqual(mockInitialItems);
      expect(result.current.hasMore).toBe(false);
    });
  });
});
