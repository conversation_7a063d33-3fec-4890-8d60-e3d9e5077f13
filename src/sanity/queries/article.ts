import { Content } from "@/components/ui/RichContent";
import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";
import { AuthorSocialMedia } from "./Author/author";

export const articleQuery = defineQuery(`
    *[
      _type in ["dailyStoryArticle", "liveBlogArticle"] && 
      agency == '${workspace}' &&
      !(_id in path("drafts.**")) &&
      defined(category) &&
      category->_id != null &&
      category->slug.current == $category && 
      slug.current == $slug
    ][0] {
      _type,
      "id": _id,
      title,
      description,
      summary,
      _updatedAt,
      "updatedAt": _updatedAt,
      "slug": slug.current,
      publishedDate,
      "createdAt": _createdAt,
      "media": featuredMedia[] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "authors": authors[]-> {
        "id": _id,
        "slug": slug.current,
        "name": fullName,
        shortBio,
        "jobTitle": jobTitle->name,  
        "location": location->name, 
        email,
        image {
          "url": secure_url
        },
        "socialMedia": socialMedia[] {
          icon,
          link,
          nickname
        },
      },
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "tags": tags[]-> {
        "id": _id,
        title,
        "slug": slug.current
      },
    
      // ** Daily story fields **
      "body": body[] {
        _key,
        _type,
        "children": children[] {
          _key,
          _type,
          "text": text,
          "marks": marks[]  
        },
        "style": style,
        "markDefs": markDefs[],
        
        // Extract Cloudinary asset fields for images
        "public_id": select(_type == "cloudinary.asset" => public_id),
        "url": select(_type == "cloudinary.asset" => url),
        "secure_url": select(_type == "cloudinary.asset" => secure_url),
        "resource_type": select(_type == "cloudinary.asset" => resource_type),
        "format": select(_type == "cloudinary.asset" => format),
        "width": select(_type == "cloudinary.asset" => width),
        "height": select(_type == "cloudinary.asset" => height),
        "bytes": select(_type == "cloudinary.asset" => bytes),
        "duration": select(_type == "cloudinary.asset" => duration),
        "context": select(_type == "cloudinary.asset" => context),
        "caption": select(_type == "cloudinary.asset" => context.custom.caption),
        
        "listItem": listItem,
        
        // Filter only the bullet items and extract their text
        "bulletText": select(
          listItem == "bullet" => children[].text
        ),
        
        // Inline Related Articles Extraction
        "inlineRelatedArticles": select(
          _type == "inlineRelatedArticles" => {
            _key,
            _type,
            "articles": articles[]-> {
              "id": _id,
              title,
              "slug": slug.current,
              "category": category-> {
                "id": _id,
                title,
                "slug": slug.current,
                description
              },
              "subcategory": subcategory-> {
                "id": _id,
                title,
                "slug": slug.current
              },
              publishedDate,
              "media": featuredMedia[] {
                "url": secure_url,
                "type": resource_type,
                "text": context.custom.caption
              }
            }
          }
        ),
        
        // Extract Highlighted Blockquote
        ...select(
          _type == "highlightedBlockquote" => {
            _key,
            _type,
            "content": content
          },
          true => {}
        ),
        
        // Extract Twitter Embeds
        "twitterEmbed": select(
          _type == "twitterEmbed" => {
            _key,
            _type,
            "url": url
          }
        ),

        // Extract Instagram Embeds
        "instagramEmbed": select(
          _type == "instagramEmbed" => {
            _key,
            _type,
            "url": url
          }
        ),

        // Extract Instagram Embeds
        "facebookEmbed": select(
          _type == "facebookEmbed" => {
            _key,
            _type,
            "url": url
          }
        ),

        // Extract Iframe Embeds
        "iframeEmbed": select(
          _type == "iframeEmbed" => {
            _key,
            _type,
            "url": url
          }
        ),

        ...select(
          _type == "richTextBlock" => {
            _key,
            _type,
            "content": content[]{
              _key,
              _type,
              "text": children[].text,
              "children": children[],
              "marks": children[].marks,
              style,
              markDefs
            }
          },
          true => {}
        ),

      ...select(
        _type == "imageCarousel" => {
          _type,
          _key,
          "images": images[]{
            "url": secure_url,
            "type": "image",
            "text": context.custom.caption
          }
        },
        true => {}
      )
      },
    
      // ** Retrieve only the first 4 relatedArticles **
      "relatedArticles": relatedArticles[0...4]-> {
        _type,
        "isLiveBlog": _type == "liveBlogArticle",
        "id": _id,
        title,
        "slug": slug.current,
        "category": category-> {
          "id": _id,
          title,
          "slug": slug.current,
          description
        },
        "subcategory": subcategory-> {
          "id": _id,
          title,
          "slug": slug.current
        },
        "media": featuredMedia[0] {
          "url": secure_url,
          "type": resource_type,
          "text": context.custom.caption
        }
      },
    
      // ** Live blog fields **
      isBreakingNews,
      "liveUpdates": liveUpdates[] | order(timestamp desc) {
        _id,
        _key,
        title,
        timestamp,
        "slug": slug.current,
        content,
        "featuredMedia": featuredMedia {
          "url": secure_url,
          "type": resource_type,
          "text": context.custom.caption,
        },
        "authors": authors[]-> {
          "id": _id,
          "slug": slug.current,
          "name": fullName,
          shortBio,
          "jobTitle": jobTitle->name,  
          "location": location->name, 
          email,
          image {
            "url": secure_url
          },
          "socialMedia": socialMedia[] {
            icon,
            link,
            nickname
          },
        },
        "relatedArticle": relatedArticle-> {
          _id,
          title,
          "slug": slug.current,
          category-> {
            "slug": slug.current
          },
          subcategory-> {
            "slug": slug.current
          }
        }
      },
      "totalLiveUpdatesWithMedia": count(liveUpdates[defined(featuredMedia)]),
      "allLiveUpdateMedia": liveUpdates[defined(featuredMedia)] | order(timestamp desc)[]{
        "url": featuredMedia.secure_url,
        "type": featuredMedia.resource_type,
        "text": featuredMedia.context.custom.caption
      },
      socialMediaMetadata,
      seoMetadata
    }
`);

export type SocialMediaMetadata = {
  facebookTitle?: string;
  facebookDescription?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterTweetContent?: string;
};

export type SEOMetadata = {
  title?: string;
  description?: string;
};

export type Media = {
  url: string;
  resource_type: string;
  text?: string;
  type: string;
};

export type Tag = {
  id: string;
  title: string;
  slug: string;
};

export type Author = {
  id: string;
  slug: string;
  name: string;
  shortBio?: string;
  location?: string;
  jobTitle?: string;
  email: string;
  socialMedia: AuthorSocialMedia[];
  image?: Media;
};

export type Category = {
  id: string;
  title: string;
  slug: string;
  description?: string;
};

export type Subcategory = {
  id: string;
  title: string;
  slug: string;
};

export type Article = {
  id: string;
  title: string;
  slug: string;
  description: string;
  _updatedAt?: string;
  publishedDate: string;
  created_at: string;
  media: Media[];
  subcategory?: Subcategory;
  authors: Author[];
  category: Category;
  tags?: Tag[];
  socialMediaMetadata?: SocialMediaMetadata;
  seoMetadata?: SEOMetadata;
  body: Content[];
  relatedArticles: RelatedArticle[];
};

export type RelatedArticle = {
  id: string;
  title: string;
  slug: string;
  media: Media;
  category: Category;
  subcategory?: Subcategory;
};

export type LiveUpdate = {
  _key: string;
  title: string;
  timestamp: string;
  slug: string;
  content: Content[];
  featuredMedia?: FeaturedMedia;
  authors: Author[];
  relatedArticle: {
    _id: string | null;
    title: string;
    slug: string;
    image?: {
      url: string;
    };
  } | null;
};

export type FeaturedMedia = {
  url: string;
  type: string;
  resource_type: string;
  text: string;
};

export type SummaryItem = {
  text: string;
  _key: string;
};

export type LiveBlogArticle = {
  _id: string;
  title: string;
  description: string;
  slug: string;
  publishedDate: string;
  updatedAt: string;
  media: Media[];
  author: Author[];
  category: Category;
  subcategory?: Subcategory;
  liveUpdates: LiveUpdate[];
  summary: SummaryItem[];
};
