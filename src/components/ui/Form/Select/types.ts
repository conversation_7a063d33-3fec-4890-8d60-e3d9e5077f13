import { SelectHTMLAttributes } from "react";

export type SelectSize = "xs" | "sm";

export interface SelectOption {
  value: string;
  label: string;
}

export interface SelectProps
  extends Omit<
    SelectHTMLAttributes<HTMLSelectElement>,
    "onChange" | "value" | "size"
  > {
  label?: string;
  placeholder?: string;
  options: SelectOption[];
  error?: boolean | string;
  value: string | null;
  onChange: (value: string) => void;
  size?: SelectSize;
}
