"use client";

import { useState } from "react";
import ContactUsForm from "@/components/ContactForm/page";
import { FormValues } from "@/types/contact-us";
import { IMAGES_PATH } from "@/constants";
import Button from "@/components/ui/Button";
import Image from "next/image";

export default function ContactUsClient() {
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  const handleFormSubmit = async (data: FormValues) => {
    setIsFormSubmitting(true);
    try {
      const response = await fetch("/api/email", {
        method: "POST",
        body: JSON.stringify(data),
      });
      console.log("Response from API:", response);
      if (!response.ok || response.status !== 200) {
        console.error("Failed to submit form");
        setIsFormSubmitting(false);
        setShowError(true);
        return;
      } else {
        setIsFormSubmitting(false);
        setShowSuccess(true);
        setShowError(false);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setIsFormSubmitting(false);
      setShowError(true);
      return;
    }
  };

  return (
    <div className="max-w-[630px] mx-auto">
      {!showSuccess && !showError ? (
        <ContactUsForm
          onSubmit={handleFormSubmit}
          isSubmitting={isFormSubmitting}
        />
      ) : null}

      {showSuccess ? (
        <div className="max-w-96 mx-auto flex flex-col items-center gap-4">
          <Image
            src={`${IMAGES_PATH}/account/contact-us-success.png`}
            alt="Success"
            width={212}
            height={212}
            className="max-w-full"
          />
          <p className="text-sm text-center">
            We appreciate your interest in contacting us. Your message is
            important to us. We will be in touch with you soon to assist you
            with whatever you need.
          </p>
          <Button href="/" className="w-80 max-w-full">
            Back to Home page
          </Button>
        </div>
      ) : null}

      {showError ? (
        <div className="max-w-96 mx-auto flex flex-col items-center gap-4">
          <Image
            src={`${IMAGES_PATH}/account/contact-us-error.png`}
            alt="Error"
            width={307}
            height={247}
            className="max-w-full"
          />
          <p className="text-sm text-center">
            Oops! It seems there was an issue with your contact submission.
            Please try resending it or reach out to <NAME_EMAIL>
            for further assistance.
          </p>
        </div>
      ) : null}
    </div>
  );
}
