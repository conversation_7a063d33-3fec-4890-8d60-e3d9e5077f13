"use client";

import { useEffect, useRef, useState } from "react";

declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _iub?: any;
  }
}

interface InstagramEmbedProps {
  url: string;
}

const InstagramEmbed: React.FC<InstagramEmbedProps> = ({ url }) => {
  const [canRender, setCanRender] = useState(false);
  const [iframeHeight, setIframeHeight] = useState<number | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const handleConsent = () => {
      if (window._iub?.cs?.isConsentGiven?.()) {
        setCanRender(true);
      }
    };

    if (window._iub?.cs?.isConsentGiven?.()) {
      setCanRender(true);
    } else {
      window.addEventListener("iubenda_cs_consent_given", handleConsent);
    }

    return () => {
      window.removeEventListener("iubenda_cs_consent_given", handleConsent);
    };
  }, []);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (
        event.data.type === `instagram-resize-${url}` &&
        iframeRef.current &&
        !iframeHeight
      ) {
        console.log(`instagram-resize-${url}`, event.data);
        setIframeHeight(parseInt(event.data.height) + 20);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => window.removeEventListener("message", handleMessage);
  }, [canRender]);

  if (!canRender) {
    return;
  }

  return (
    <iframe
      ref={iframeRef}
      src={`/social-embed/instagram?url=${url}`}
      style={{
        border: "none",
        width: "100%",
        height: iframeHeight ? `${iframeHeight}px` : "0px",
        maxWidth: "100%",
      }}
    />
  );
};

export default InstagramEmbed;
