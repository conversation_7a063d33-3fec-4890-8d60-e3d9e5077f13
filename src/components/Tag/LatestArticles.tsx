"use client";

import Image from "next/image";
import Link from "next/link";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";
import Button from "../ui/Button";
import { Icon } from "../Icon";
import { usePagination } from "@/utils/usePagination";
import { Article } from "@/sanity/queries/Tag/tag";
import { Tag } from "@/sanity/queries/Tag/tag";
import AuthorsList from "../Author/AuthorsList";

type LatestArticlesProps = {
  tag: Tag;
};

interface ArticleCardProps {
  article: Article;
  className?: string;
}

export function ArticleCard({ article }: ArticleCardProps) {
  return (
    <article>
      <div className="pb-4 mb-4 border-b border-b-grey-200 sm:flex sm:gap-4 lg:gap-8 sm:items-start">
        {article.media && (
          <div className="w-full flex-shrink-0 aspect-[16/9] lg:aspect-[16/12] xl:aspect-[16/9] relative sm:w-1/2 lg:order-2 lg:w-1/3">
            <Link
              href={getArticleUrl(
                article.category?.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="transition-opacity hover:opacity-85"
            >
              <Image
                src={getMediaImage(article.media)}
                alt={article.title}
                fill
                className="object-cover rounded-2xl w-full h-full"
              />
            </Link>
          </div>
        )}

        <div className="sm:w-1/2 lg:w-2/3">
          <p className="uppercase text-[13px] mt-4 mb-2 sm:mt-0">
            <Link
              href={`/${article.category.slug}`}
              className="hover-underline-animation"
            >
              {article.category.title}
            </Link>
          </p>

          <h2 className="font-semibold font-titles text-[24px] leading-[28px] lg:text-[28px] lg:leading-[36px]">
            <Link
              href={getArticleUrl(
                article.category?.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="hover-underline-animation"
            >
              {truncateTitle(article.title)}
            </Link>
          </h2>

          <p className="hidden lg:block mt-5 text-16px leading-[24px]">
            {truncateDescription(article.description)}
          </p>
          {article.authors && (
            <AuthorsList
              authors={article.authors}
              publishedDate={article.publishedDate}
            ></AuthorsList>
          )}
        </div>
      </div>
    </article>
  );
}

export default function LatestArticles({ tag }: LatestArticlesProps) {
  const itemsPerPage = 10;

  const {
    items: articles,
    isLoading,
    hasMore,
    loadMore,
  } = usePagination<Article>({
    initialItems: tag.articles,
    itemsPerPage,
    totalCount: tag.articlesCount,
    apiUrl: `/api/tag/${tag.id}/articles`,
  });

  return (
    <section className="overflow-visible xl:pb-4">
      {articles?.map((article) => (
        <ArticleCard key={article.id} article={article} />
      ))}

      {tag.articlesCount > itemsPerPage && hasMore && (
        <div className="flex justify-center mt-8">
          <div className="w-52">
            <Button
              data-testid="load-more-button"
              onClick={loadMore}
              state={isLoading ? "disabled" : "enabled"}
              className="w-full"
            >
              {isLoading ? (
                <Icon
                  icon="loading"
                  color="white"
                  className="animate-spin inline-block -mb-[5px]"
                />
              ) : (
                "Load More"
              )}
            </Button>
          </div>
        </div>
      )}
    </section>
  );
}
