import SavedArticlesClient from "./SavedArticlesClient";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import { auth0 } from "@/lib/auth0";

export const metadata: Metadata = {
  title: "Account - Saved Articles",
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_URL}/account/saved-articles`,
  },
  other: {
    "parsely-title": "Account - Saved Articles",
    "parsely-type": "index",
  },
};

export default async function SavedArticlesPage() {
  const session = await auth0.getSession();

  if (!session?.user) {
    redirect("/auth/login?returnTo=/account");
  }

  return <SavedArticlesClient />;
}
