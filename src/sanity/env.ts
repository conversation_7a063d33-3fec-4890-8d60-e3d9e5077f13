export const apiVersion = process.env.SANITY_API_VERSION || "2025-01-01";

export const projectId = assertValue(
  process.env.SANITY_STUDIO_SANITY_PROJECT_ID,
  "Missing environment variable: SANITY_STUDIO_SANITY_PROJECT_ID",
);

export const dataset = assertValue(
  process.env.SANITY_STUDIO_SANITY_DATASET,
  "Missing environment variable: SANITY_STUDIO_SANITY_DATASET",
);

export const workspace = assertValue(
  process.env.SANITY_STUDIO_SANITY_WORKSPACE,
  "Missing environment variable: SANITY_STUDIO_SANITY_WORKSPACE",
);

export const sitemapArticlesFirstYear = assertValue(
  process.env.SITEMAP_ARTICLES_FIRST_YEAR,
  "Missing environment variable: SITEMAP_ARTICLES_FIRST_YEAR",
);

function assertValue<T>(v: T | undefined, errorMessage: string): T {
  if (v === undefined) {
    throw new Error(errorMessage);
  }

  return v;
}
