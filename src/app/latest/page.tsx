import LatestNewsClient from "./LatestNewsClient";
import { sanityFetch as sanityLiveFetch } from "@/sanity/live";
import {
  latestNewsQuery,
  LatestNewsResponse,
} from "@/sanity/queries/latest-news";
import { Metadata } from "next";
import { fetchTrendingArticlesFromAnalytics } from "../api/trending";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata("/latest", "Latest News", "Latest News Page");
}

const LatestNewsPage = async () => {
  const { data: latestNewsData }: { data: LatestNewsResponse } =
    await sanityLiveFetch({
      query: latestNewsQuery,
    });
  const generalTrendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: null,
    subcategorySlug: null,
    periodStart: undefined,
    periodEnd: undefined,
  });
  const jsonLd = buildWebPageJsonLd("Latest News", "/latest");
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <LatestNewsClient
        latestNews={latestNewsData}
        generalTrendingArticles={generalTrendingArticles}
      />
    </>
  );
};

export default LatestNewsPage;
