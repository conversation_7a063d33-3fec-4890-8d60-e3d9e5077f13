import { TelegramShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const TelegramButton = ({ shareUrl }: { shareUrl: string }) => {
  return (
    <TelegramShareButton url={shareUrl} title="Check this out">
      <ShareIconButton
        icon={(hover) => (
          <Icon icon="telegram" size={24} color={hover ? "#0088cc" : "black"} />
        )}
      />
    </TelegramShareButton>
  );
};

export default TelegramButton;
