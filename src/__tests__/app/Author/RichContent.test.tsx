import { render, screen } from "@testing-library/react";
import RichContent, {
  BlockContent,
  Body,
  Content,
} from "@/components/Author/RichContent";
import type { PortableTextReactComponents } from "@portabletext/react";

interface MockPortableTextProps {
  value: Content[];
  components: Partial<PortableTextReactComponents>;
}

interface MockBlockChild {
  _key: string;
  _type: string;
  text: string;
  marks: string[];
}

interface MockBlock {
  _key: string;
  _type: string;
  style?: string;
  children?: MockBlockChild[];
  markDefs?: Array<{
    _key: string;
    _type: string;
    href?: string;
    target?: string;
  }>;
  listItem?: string;
}

jest.mock("@portabletext/react", () => ({
  PortableText: ({ value, components }: MockPortableTextProps) => {
    return (
      <div data-testid="portable-text-mock">
        {Array.isArray(value) &&
          value.map((block: MockBlock, index: number) => {
            if (block._type === "block") {
              const style = block.style || "normal";
              const Component =
                components.block?.[style] ||
                (() => <div>Default component</div>);

              let children = "Content";
              if (block.children && block.children.length > 0) {
                children = block.children.map(
                  (child: MockBlockChild, childIndex: number) => {
                    let text = child.text || "";

                    if (child.marks && child.marks.length > 0) {
                      child.marks.forEach((mark: string) => {
                        const markDefs = block.markDefs || [];
                        const markDef = markDefs.find(
                          (def) => def._key === mark,
                        );

                        if (
                          markDef &&
                          markDef._type === "link" &&
                          components.marks?.link
                        ) {
                          const LinkComponent = components.marks.link;
                          text = (
                            <LinkComponent key={childIndex} value={markDef}>
                              {text}
                            </LinkComponent>
                          );
                        } else if (
                          components.marks &&
                          mark in components.marks
                        ) {
                          const MarkComponent =
                            components.marks[
                              mark as keyof typeof components.marks
                            ];
                          if (MarkComponent) {
                            text = (
                              <MarkComponent key={childIndex}>
                                {text}
                              </MarkComponent>
                            );
                          }
                        }
                      });
                    }

                    return (
                      <span key={childIndex} data-testid="text-node">
                        {text}
                      </span>
                    );
                  },
                );
              }

              return <Component key={index}>{children}</Component>;
            }

            return null;
          })}
      </div>
    );
  },
}));

describe("RichContent Component", () => {
  it("renders the component with data-testid", () => {
    const content: BlockContent[] = [
      {
        _key: "1",
        _type: "block",
        style: "normal",
        children: [
          { _key: "a", _type: "span", text: "Hello world", marks: [] },
        ],
        markDefs: [],
      },
    ];

    render(<RichContent content={content} />);

    expect(screen.getByTestId("rich-content")).toBeInTheDocument();
    expect(screen.getByTestId("portable-text-mock")).toBeInTheDocument();
  });

  it("renders paragraphs correctly", () => {
    const content: BlockContent[] = [
      {
        _key: "1",
        _type: "block",
        style: "normal",
        children: [
          { _key: "a", _type: "span", text: "Normal paragraph", marks: [] },
        ],
        markDefs: [],
      },
    ];

    render(<RichContent content={content} />);

    const paragraphs = screen.getAllByTestId("portable-text-mock");
    expect(paragraphs.length).toBeGreaterThan(0);
  });

  it("renders headings correctly", () => {
    const content: BlockContent[] = [
      {
        _key: "1",
        _type: "block",
        style: "h1",
        children: [{ _key: "a", _type: "span", text: "Heading 1", marks: [] }],
        markDefs: [],
      },
      {
        _key: "2",
        _type: "block",
        style: "h2",
        children: [{ _key: "b", _type: "span", text: "Heading 2", marks: [] }],
        markDefs: [],
      },
      {
        _key: "3",
        _type: "block",
        style: "h3",
        children: [{ _key: "c", _type: "span", text: "Heading 3", marks: [] }],
        markDefs: [],
      },
    ];

    render(<RichContent content={content} />);

    expect(screen.getByTestId("portable-text-mock")).toBeInTheDocument();
  });

  it("renders blocks with marks correctly", () => {
    const content: BlockContent[] = [
      {
        _key: "1",
        _type: "block",
        style: "normal",
        children: [
          { _key: "a", _type: "span", text: "Bold text", marks: ["strong"] },
          { _key: "b", _type: "span", text: "Italic text", marks: ["em"] },
          {
            _key: "c",
            _type: "span",
            text: "Underlined text",
            marks: ["underline"],
          },
        ],
        markDefs: [],
      },
    ];

    render(<RichContent content={content} />);

    expect(screen.getByTestId("portable-text-mock")).toBeInTheDocument();
  });

  it("renders links correctly", () => {
    const content: Body[] = [
      {
        _key: "1",
        _type: "block",
        style: "normal",
        children: [
          { _key: "a", _type: "span", text: "Click here", marks: ["link-1"] },
        ],
        markDefs: [
          {
            _key: "link-1",
            _type: "link",
            href: "https://example.com",
            target: "_blank",
          },
        ],
      },
    ];

    render(<RichContent content={content} />);

    expect(screen.getByTestId("portable-text-mock")).toBeInTheDocument();
  });

  it("renders nested content structures correctly", () => {
    const complexContent: (BlockContent | Body)[] = [
      {
        _key: "1",
        _type: "block",
        style: "h1",
        children: [
          { _key: "title", _type: "span", text: "Article Title", marks: [] },
        ],
        markDefs: [],
      },
      {
        _key: "2",
        _type: "block",
        style: "normal",
        children: [
          {
            _key: "p1",
            _type: "span",
            text: "This is a paragraph with ",
            marks: [],
          },
          { _key: "p2", _type: "span", text: "bold", marks: ["strong"] },
          { _key: "p3", _type: "span", text: " and ", marks: [] },
          { _key: "p4", _type: "span", text: "italic", marks: ["em"] },
          { _key: "p5", _type: "span", text: " text.", marks: [] },
        ],
        markDefs: [],
      },
      {
        _key: "3",
        _type: "block",
        listItem: "bullet",
        style: "normal",
        children: [
          { _key: "l1", _type: "span", text: "List item 1", marks: [] },
        ],
        markDefs: [],
      },
    ];

    render(<RichContent content={complexContent} />);

    expect(screen.getByTestId("portable-text-mock")).toBeInTheDocument();
  });
});
