import { sanityFetch } from "@/sanity/live";
import { notFound } from "next/navigation";
import { authorQuery } from "@/sanity/queries/Author/author";
import AuthorClient from "@/app/author/[slug]/AuthorClient";
import { Metadata } from "next";
import { buildAuthorJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata({
  params,
}: ArticlePageProps): Promise<Metadata> {
  const { slug } = await params;
  const { data: author } = await fetchAuthor({ slug: slug });

  if (!author) {
    return notFound();
  }

  return generatePageMetadata(
    `/author/${slug}`,
    `Author page - ${author.name}`,
    author.shortBio,
    author.image?.secure_url,
  );
}

interface ArticlePageProps {
  params: Promise<{ category: string; slug: string }>;
}

async function fetchAuthor(options: { slug: string }) {
  return await sanityFetch({
    query: authorQuery,
    params: { slug: options.slug },
  });
}

export default async function AuthorPage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const { data: author } = await fetchAuthor({ slug: slug });

  if (!author) {
    return notFound();
  }

  const jsonLd = buildAuthorJsonLd(
    author.name,
    `/author/${slug}`,
    author.image?.secure_url,
    author.shortBio,
  );
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <AuthorClient author={author} />
    </>
  );
}
