"use client";
import { Tag } from "@/sanity/queries/Tag/tag";
import Link from "next/link";
import LatestArticles from "../../../components/Tag/LatestArticles";
import { Trending } from "../../../components/HomePage/Trending";
import { IMAGES_PATH } from "@/constants";
import Button from "../../../components/ui/Button";
import { useEffect, useRef, useState } from "react";
import RequireLoginTooltip from "../../../components/Auth/RequireLoginTooltip";
import { useUser } from "@auth0/nextjs-auth0";
import { Article as TrendingArticle } from "@/sanity/queries/Trending/trending";
import Image from "next/image";

type Props = {
  tag: Tag;
  generalTrendingArticles: TrendingArticle[];
};

export default function TagClient({ tag, generalTrendingArticles }: Props) {
  const [isFollowed, setIsFollowed] = useState<boolean | undefined>(undefined);
  const [showNeedLogin, setShowNeedLogin] = useState<boolean>(false);
  const [isFollowedCheckComplete, setIsFollowedCheckComplete] = useState(false);
  const [showReachFollowLimitTooltip, setReachFollowLimitTooltip] =
    useState<boolean>(false);
  const [showLimitTooltipClicked, setShowLimitTooltipClicked] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const { user } = useUser();
  useEffect(() => {
    const checkIfTagIsFollowed = async () => {
      if (!user) {
        setIsFollowedCheckComplete(true);
        return;
      }

      try {
        const res = await fetch(`/api/tags/${tag.id}/followers`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!res.ok) {
          console.error("Failed to fetch followed status");
          return;
        }
        const data = await res.json();
        if (data?.followerCount >= 20) {
          setReachFollowLimitTooltip(true);
        } else {
          setReachFollowLimitTooltip(false);
        }

        setIsFollowed(data?.userFollows ?? false);
      } catch (err) {
        console.error("Error checking bookmark:", err);
      } finally {
        setIsFollowedCheckComplete(true);
      }
    };

    checkIfTagIsFollowed();
  }, [user, tag.id]);

  const handleFollow = async () => {
    if (!user) {
      setShowNeedLogin(true);
      return;
    }

    try {
      const response = await fetch(`/api/tags/${tag.id}/followers`, {
        method: isFollowed ? "DELETE" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const data = await response.json();

        if (
          response.status === 400 ||
          data?.error?.toLowerCase().includes("limit")
        ) {
          setShowLimitTooltipClicked(true);
        } else {
          console.error("Failed to update bookmarks status", data?.error);
        }

        return;
      }

      setIsFollowed(!isFollowed);
    } catch (error) {
      console.error("Error updating bookmark:", error);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setShowLimitTooltipClicked(false);
      }
    };

    if (showLimitTooltipClicked) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showLimitTooltipClicked]);
  return (
    <div className="container mx-auto p-4">
      <header>
        <nav className="mt-2">
          <ol className="flex items-center gap-2 text-[13px] lg:text-[14px]">
            <li className="underline hover:text-red-700">
              <Link href="/">Home</Link>
            </li>
            <li>·</li>
            <li>{tag.title}</li>
          </ol>
        </nav>
        <div className="flex flex-col items-start lg:items-start lg:flex-row lg:gap-8 pt-6 lg:pt-8">
          <div className="flex flex-col items-start lg:items-start">
            <div className="flex flex-col items-start lg:flex-row lg:gap-4 lg:items-end">
              <h1 className="font-titles font-semibold text-[32px] lg:text-[40px] leading-none mt-4 lg:mt-0 mb-4 lg:mb-0">
                {tag.title}
              </h1>
              <div className="relative z-20" ref={wrapperRef}>
                {isFollowedCheckComplete && (
                  <>
                    <Button
                      variant="outlinedBlack"
                      size="xs"
                      className={`font-normal ${isFollowed ? "!border-black !bg-black !text-white" : ""}`}
                      onClick={() => {
                        if (showReachFollowLimitTooltip && !isFollowed) {
                          setShowLimitTooltipClicked(true);
                          return;
                        }
                        setShowLimitTooltipClicked(false);
                        if (user) {
                          handleFollow();
                        } else {
                          setShowNeedLogin(true);
                        }
                      }}
                    >
                      {isFollowed ? "Following" : "Follow +"}
                    </Button>

                    {showLimitTooltipClicked && !isFollowed && (
                      <div
                        ref={tooltipRef}
                        className="absolute top-full mt-2 left-1/2 -translate-x-1/2 w-[240px] bg-black text-white text-sm p-3 rounded-xl z-30 shadow-lg"
                      >
                        <div className="text-center font-semibold text-[15px]">
                          You’ve reached your limit of 20 tags.
                        </div>
                        <div className="mt-1 text-center">
                          To follow a new one, please remove a tag first.
                        </div>
                        <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-black" />
                      </div>
                    )}
                  </>
                )}
                <RequireLoginTooltip
                  show={showNeedLogin}
                  centerOnMobile={true}
                  onHideNeedLogin={(show: boolean) => setShowNeedLogin(show)}
                />
              </div>
            </div>
            <p className="mt-4 text-[16px] lg:text-[18px]">{tag.subtitle}</p>
          </div>
        </div>
      </header>
      <div className="mt-6 flex justify-center lg:justify-start">
        <div className="relative border border-grey-200 rounded-[20px] flex text-[14px]"></div>
      </div>
      <div className="lg:grid lg:grid-cols-12 lg:gap-4 mt-4 pt-4 border-t border-grey-200">
        <div className="pt-8 lg:col-span-9">
          {tag.articles.length ? (
            <LatestArticles tag={tag} />
          ) : (
            <div className="mt-8 max-w-96 mx-auto flex flex-col items-center">
              <Image
                src={`${IMAGES_PATH}/account/no-saved-articles.png`}
                alt="We suggest selecting at least one topic to personalize your experience"
                width={170}
                height={170}
                className="max-w-full"
              />

              <p className="text-sm text-center">
                Looks like there aren’t any articles here just yet. Check back
                soon for updates—we’ll have new content for you shortly!
              </p>
            </div>
          )}
        </div>
        <div className="col-span-3">
          <div className="sticky top-24">
            <Trending
              oneColumn={true}
              trendingArticles={generalTrendingArticles}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
