import { NextRequest, NextResponse } from "next/server";
import { sanityFetch } from "@/sanity/live";
import { moreArticlesQuery } from "@/sanity/queries/Author/author";
import { GET } from "./route";

jest.mock("next/server", () => ({
  NextResponse: {
    json: jest.fn((data, options) => ({ data, options })),
  },
}));

jest.mock("@/sanity/live", () => ({
  sanityFetch: jest.fn(),
}));

jest.mock("@/sanity/queries/Author/author", () => ({
  moreArticlesQuery: "mockedQuery",
}));

describe("Author Articles API Route", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("GET /api/authors/:authorId/articles", () => {
    it("should fetch articles with default pagination parameters", async () => {
      const url = new URL("https://example.com/api/authors/author123/articles");
      const mockRequest = {
        url,
      } as unknown as NextRequest;

      const mockResponse = {
        data: {
          articles: [{ id: "article1", title: "Test Article" }],
          totalCount: 5,
        },
      };

      (sanityFetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const response = await GET(mockRequest, {
        params: Promise.resolve({ authorId: "author123" }),
      });

      expect(sanityFetch).toHaveBeenCalledWith({
        query: moreArticlesQuery,
        params: {
          authorId: "author123",
          start: 0,
          end: 10,
        },
      });

      expect(NextResponse.json).toHaveBeenCalledWith(mockResponse);
      expect(response).toEqual({
        data: mockResponse,
        options: undefined,
      });
    });

    it("should fetch articles with custom pagination parameters", async () => {
      const url = new URL(
        "https://example.com/api/authors/author123/articles?page=2&pageSize=5",
      );
      const mockRequest = {
        url,
      } as unknown as NextRequest;

      const mockResponse = {
        data: {
          articles: [{ id: "article6", title: "Another Test Article" }],
          totalCount: 8,
        },
      };

      (sanityFetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const response = await GET(mockRequest, {
        params: Promise.resolve({ authorId: "author123" }),
      });

      expect(sanityFetch).toHaveBeenCalledWith({
        query: moreArticlesQuery,
        params: {
          authorId: "author123",
          start: 5,
          end: 10,
        },
      });

      expect(NextResponse.json).toHaveBeenCalledWith(mockResponse);
      expect(response).toEqual({
        data: mockResponse,
        options: undefined,
      });
    });

    it("should handle errors correctly", async () => {
      const url = new URL("https://example.com/api/authors/author123/articles");
      const mockRequest = {
        url,
      } as unknown as NextRequest;

      const mockError = new Error("Failed to fetch articles");
      (sanityFetch as jest.Mock).mockRejectedValueOnce(mockError);

      const response = await GET(mockRequest, {
        params: Promise.resolve({ authorId: "author123" }),
      });

      expect(NextResponse.json).toHaveBeenCalledWith(
        {
          error: "Error fetching articles",
          message: "Failed to fetch articles",
        },
        { status: 500 },
      );
      expect(response).toEqual({
        data: {
          error: "Error fetching articles",
          message: "Failed to fetch articles",
        },
        options: { status: 500 },
      });
    });

    it("should handle invalid page/pageSize parameters", async () => {
      const url = new URL(
        "https://example.com/api/authors/author123/articles?page=abc&pageSize=def",
      );
      const mockRequest = {
        url,
      } as unknown as NextRequest;

      const mockResponse = {
        data: {
          articles: [{ id: "article1", title: "Test Article" }],
          totalCount: 5,
        },
      };

      (sanityFetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const response = await GET(mockRequest, {
        params: Promise.resolve({ authorId: "author123" }),
      });

      expect(sanityFetch).toHaveBeenCalledWith({
        query: moreArticlesQuery,
        params: {
          authorId: "author123",
          start: 0,
          end: 10,
        },
      });

      expect(NextResponse.json).toHaveBeenCalledWith(mockResponse);
      expect(response).toEqual({
        data: mockResponse,
        options: undefined,
      });
    });
  });
});
