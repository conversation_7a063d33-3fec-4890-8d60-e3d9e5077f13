"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Icon } from "@/components/Icon";

interface MenuItemProps {
  href: string;
  label: string;
}

const MenuItem = ({ href, label }: MenuItemProps) => {
  const pathname = usePathname();
  const isActive = pathname === href;

  return (
    <Link
      href={href}
      className="flex items-center justify-between md:py-3 md:px-2 md:border-b md:border-grey-200"
    >
      <span
        className={`whitespace-nowrap text-sm md:text-[18px] ${isActive ? "underline underline-offset-4" : "hover-underline-animation"}`}
      >
        {label}
      </span>

      <span className="hidden md:block">
        <Icon icon="goto" />
      </span>
    </Link>
  );
};

export default MenuItem;
