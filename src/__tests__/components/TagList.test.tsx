import TagsList from "@/components/Tag/Taglist";
import { render, screen } from "@testing-library/react";

const mockTags = [
  { id: "1", title: "World News", slug: "world-news" },
  { id: "2", title: "Politics", slug: "politics" },
  { id: "3", title: "Asia-Pacific", slug: "asia-pacific" },
];

describe("TagsList Component", () => {
  it("renders without crashing", () => {
    render(<TagsList tags={mockTags} />);
    expect(screen.getByText("World News")).toBeInTheDocument();
  });

  it("does not render if no tags are provided", () => {
    const { container } = render(<TagsList tags={[]} />);
    expect(container.firstChild).toBeNull();
  });

  it("renders the correct number of tags", () => {
    render(<TagsList tags={mockTags} />);
    const tagElements = screen.getAllByRole("link");
    expect(tagElements).toHaveLength(mockTags.length);
  });

  it("each tag has the correct label and slug", () => {
    render(<TagsList tags={mockTags} />);

    mockTags.forEach((tag) => {
      const tagElement = screen.getByRole("link", { name: tag.title });
      expect(tagElement).toBeInTheDocument();
      expect(tagElement).toHaveAttribute("href", `/tags/${tag.slug}`);
    });
  });
});
