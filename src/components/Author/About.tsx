"use client";

import { Author } from "@/sanity/queries/Author/author";
import RichContent from "./RichContent";
import { Icon } from "@/components/Icon";
import Link from "next/link";
import { useCallback, useEffect, useState, useRef } from "react";

type AboutProps = {
  author: Author;
};

export default function About({ author }: AboutProps) {
  const [actualItem, setActualItem] = useState<string>(author.about[0].title);
  const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const scrollToElement = useCallback((id: string) => {
    const element = document.getElementById(id);

    if (element) {
      const headerHeight = window.scrollY > 0 ? 97 : 200;

      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - headerHeight;

      window.history.pushState(null, "", `#${id}`);

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 150;

      let currentSection = author.about[0].title;

      author.about.forEach((item) => {
        const section = sectionRefs.current[item.title];
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;

          if (
            scrollPosition >= sectionTop &&
            scrollPosition < sectionTop + sectionHeight
          ) {
            currentSection = item.title;
          }
        }
      });

      if (currentSection !== actualItem) {
        setActualItem(currentSection);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [author.about, actualItem]);

  const setRef = (el: HTMLDivElement | null, id: string) => {
    sectionRefs.current[id] = el;
  };

  return (
    <section className="lg:grid lg:grid-cols-3 gap-4">
      <div className="hidden lg:block">
        <div className="bg-grey-100 rounded-xl p-4 lg:sticky lg:top-24 mb-6">
          <div className="font-bold text-[17px] uppercase mb-2">
            Get to know me
          </div>

          {author.about.map((aboutItem) => (
            <button
              key={aboutItem.title}
              className={`w-full before:content-[' '] before:block before:w-2 before:h-2 before:rounded-full flex gap-4 items-center border-b border-grey-300 py-3 cursor-pointer transition-colors
                ${actualItem === aboutItem.title ? "before:bg-black" : "before:bg-grey-300"}
              `}
              onClick={(e) => {
                e.preventDefault();
                scrollToElement(aboutItem.title);
              }}
            >
              {aboutItem.title}
            </button>
          ))}
        </div>
      </div>

      <div className="lg:col-span-2">
        {author.about.map((aboutItem) => (
          <div
            key={aboutItem.title}
            id={aboutItem.title}
            ref={(el) => setRef(el, aboutItem.title)}
            className="border border-grey-200 py-3 rounded-xl mb-6 scroll-mt-24"
          >
            <h2 className="font-titles font-semibold text-[28px] px-4">
              {aboutItem.title}
            </h2>

            <div className="text-[18px] px-4">
              <RichContent content={aboutItem.description} />
            </div>

            {aboutItem.blockWithSocialLinks && (
              <div className="flex mt-1 pb-2 overflow-y-auto hide-scrollbar">
                <Link
                  key="email"
                  href={`mailto:${author.email}`}
                  className="flex gap-1 hover-underline-animation-child pl-4 pr-4"
                >
                  <Icon icon="email" />

                  <span className="inline-block hover-underline-animation-child-item">
                    {author.email}
                  </span>
                </Link>

                {author.socialMedia?.map((socialMediaItem) => (
                  <Link
                    key={socialMediaItem.icon}
                    href={socialMediaItem.link}
                    target="_blank"
                    className="flex items-center gap-1 hover-underline-animation-child pr-4"
                  >
                    <Icon icon={socialMediaItem.icon} className="w-6 h-6" />

                    <span className="inline-block hover-underline-animation-child-item">
                      {socialMediaItem.nickname}
                    </span>
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </section>
  );
}
