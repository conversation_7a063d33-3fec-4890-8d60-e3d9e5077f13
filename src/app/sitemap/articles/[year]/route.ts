import { extractParam } from "@/app/api/util";
import { sanityFetch } from "@/sanity/client";
import { sitemapArticlesFirstYear } from "@/sanity/env";
import { Article, articlesQuery } from "@/sanity/queries/sitemap";
import { getArticleUrl } from "@/utils/utils";
import { NextRequest, NextResponse } from "next/server";

function isWithinLast12Months(date: string | number | Date): boolean {
  const publishedDate: Date = new Date(date);
  const currentDate: Date = new Date();

  const twelveMonthsAgo: Date = new Date();
  twelveMonthsAgo.setMonth(currentDate.getMonth() - 12);

  return publishedDate >= twelveMonthsAgo;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ year: string }> },
) {
  const baseUrl = process.env.APP_BASE_URL || "";
  const year = await extractParam(params, "year");
  const firstYearWithArticles = sitemapArticlesFirstYear;
  const currentYear = new Date().getFullYear().toString();

  if (!year) {
    return new NextResponse("Year parameter is required.", { status: 404 });
  }

  if (!/^\d{4}$/.test(year)) {
    return new NextResponse("Invalid year format.", { status: 404 });
  }

  if (year < firstYearWithArticles || year > currentYear) {
    return new NextResponse("Not found.", { status: 404 });
  }

  const articles: Article[] = await sanityFetch({
    query: articlesQuery,
    params: { year },
  });

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  articles.map(
    (article) =>
      (sitemap += `<url>
      <loc>${baseUrl}${getArticleUrl(article.category.slug, article.slug, article.subcategory?.slug)}</loc>
      <lastmod>${new Date(article.updatedAt).toISOString()}</lastmod>
      <changefreq>${isWithinLast12Months(article.publishedDate) ? "montly" : "yearly"}</changefreq>
      <priority>${isWithinLast12Months(article.publishedDate) ? "1.0" : "0.8"}</priority>
    </url>`),
  );

  sitemap += `</urlset>`;

  return new NextResponse(sitemap, {
    headers: {
      "Content-Type": "application/xml",
    },
  });
}
