"use client";

import { useEffect, useState } from "react";
import AccountLayout from "@/components/Account/Layout";
import PatronSaint from "@/components/Account/PatronSaint";
import SavedArticles from "./SavedArticles";
import { Article } from "@/app/api/articles/bookmarks/route";
import { Icon } from "@/components/Icon";
import Skeleton from "@/components/ui/Skeleton";

export default function SavedArticlesClient() {
  const [initialItems, setInitialItems] = useState<Article[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [reloadKey, setReloadKey] = useState(0);

  const fetchSavedArticles = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/articles/bookmarks");
      const json = await res.json();
      const items = json?.data?.items ?? [];
      const count = json?.data?.totalCount ?? 0;
      setInitialItems(items);
      setTotalCount(count);
    } catch (err) {
      console.error("Error fetching saved articles:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSavedArticles();
  }, [reloadKey]);

  const handleReload = () => {
    setReloadKey((prev) => prev + 1);
  };

  return (
    <AccountLayout>
      <AccountLayout.Left>
        <div className="hidden lg:hidden mt-8">
          <PatronSaint />
        </div>
      </AccountLayout.Left>

      <AccountLayout.Main>
        <h2 className="font-titles text-[25px] lg:text-[34px] font-semibold">
          Saved Articles
        </h2>
        <h3 className="text-[16px] lg:text-[18px]">
          Access your articles from all your devices with your EWTN account
        </h3>

        {isLoading ? (
          <>
            <div className="mt-6 pt-2 border-t border-black flex items-center gap-1 mb-4">
              <Icon icon="savedArticle" className="-ml-1" />
              <span className="text-[16px] lg:text-[18px] font-bold uppercase">
                saved articles
              </span>
            </div>
            <div className="space-y-4">
              {Array.from({ length: 10 }, (_, i) => (
                <div
                  className="flex gap-4 items-center group"
                  data-testid="news-card-container"
                  key={`saved-article-skeleton-${i}`}
                >
                  <div className="flex gap-4 border-b pb-4 border-grey-200 group-last:border-b-0 w-full">
                    <div className="w-full">
                      <Skeleton
                        imagePosition="left"
                        imageHeight="6rem"
                        imageWidth="12rem"
                        titleWidth="100%"
                        titleHeight="1.5rem"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <SavedArticles
            key={reloadKey}
            initialItems={initialItems}
            totalCount={totalCount}
            onReload={handleReload}
          />
        )}
      </AccountLayout.Main>

      <AccountLayout.Right>
        <PatronSaint />
      </AccountLayout.Right>
    </AccountLayout>
  );
}
