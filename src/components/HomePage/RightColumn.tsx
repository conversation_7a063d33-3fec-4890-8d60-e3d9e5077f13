import { HomePageResult } from "@/sanity/queries/HomePage/homePage";
import { Article } from "@/sanity/queries/Trending/trending";
import { getArticleUrl, getMediaImage, truncateTitle } from "@/utils/utils";
import Link from "next/link";
import Image from "next/image";
import { Icon } from "../Icon";
import Button from "../ui/Button";

function MainArticle({ article }: { article: Article }) {
  if (!article) return null;

  return (
    <div className="w-full border-b border-grey-200 md:h-full lg:h-auto pb-4">
      <div className="relative mb-3.5 aspect-[16/9] w-full">
        {article.media && article.media[0] && (
          <Link
            href={getArticleUrl(
              article.category?.slug,
              article.slug,
              article.subcategory?.slug,
            )}
            className="transition-opacity hover:opacity-85"
          >
            <Image
              src={getMediaImage(article.media[0])}
              alt={article.title}
              fill
              className="object-cover rounded-2xl w-20 h-20"
            />
          </Link>
        )}

        <div className="absolute bottom-2 left-2 flex gap-2">
          {article.media?.length > 1 && (
            <div className="w-9 h-9 rounded-2xl bg-black/70 flex items-center justify-center z-10">
              <Icon icon="gallery" color="white" className="w-5" />
            </div>
          )}

          {article.media?.find((media) => media.type == "video") && (
            <div className="w-9 h-9 rounded-2xl bg-black/70 flex items-center justify-center z-10">
              <Icon icon="play" className="w-3  " />
            </div>
          )}
        </div>
      </div>

      <h3 className="text-2xl font-semibold font-titles">
        <Link
          href={getArticleUrl(
            article.category?.slug,
            article.slug,
            article.subcategory?.slug,
          )}
          className="hover-underline-animation"
        >
          {truncateTitle(article.title)}
        </Link>
      </h3>
    </div>
  );
}

function rightColumnArticles(
  title: string,
  viewAllLink: string,
  className: string | undefined,
  mainArticle: Article,
  restArticles: Article[],
) {
  return (
    <div className={`${className} bg-gray-100 border-t pt-6 pb-8`}>
      <h2 className="text-xl font-bold mb-4 uppercase">{title}</h2>
      <ul>
        <li key={mainArticle.id} className={`mb-4`}>
          <MainArticle article={mainArticle} />
        </li>
        {restArticles.map((article, index) => (
          <li
            key={article.id}
            className={`mb-4 pb-4 ${index !== restArticles.length - 1 ? "border-b border-grey-200" : ""}`}
          >
            <Link
              href={getArticleUrl(
                article.category?.slug,
                article.slug,
                article.subcategory?.slug,
              )}
              className="text-lg leading-6 font-semibold hover-underline-animation"
            >
              {truncateTitle(article.title)}
            </Link>
          </li>
        ))}
      </ul>
      <Button
        className="w-full"
        variant="secondary"
        state="enabled"
        testId="login-button"
        iconName="arrow_forward"
        iconPosition="right"
        href={viewAllLink}
      >
        View All
      </Button>
    </div>
  );
}

export function RightColumn({
  rightColumn,
  className,
}: {
  rightColumn: HomePageResult["rightColumn"];
  className?: string;
}) {
  if (!rightColumn) return null;
  if (rightColumn.isSubcategory) {
    const subcategory = rightColumn.subcategory;
    const mainArticle = subcategory.articles[0];
    const restArticles = subcategory.articles.slice(1);
    return rightColumnArticles(
      subcategory.title,
      `/${subcategory.category?.slug}/${subcategory.slug}`,
      className,
      mainArticle,
      restArticles,
    );
  } else {
    const category = rightColumn.category;
    const mainArticle = category.articles[0];
    const restArticles = category.articles.slice(1);
    return rightColumnArticles(
      category.title,
      `/${category.slug}`,
      className,
      mainArticle,
      restArticles,
    );
  }
}
