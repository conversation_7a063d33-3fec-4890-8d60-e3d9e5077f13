import { MultiCheckboxProps } from "@/components/ui/Form/MultiCheckbox/types";

const MultiCheckbox: React.FC<MultiCheckboxProps> = ({
  options,
  selectedValues,
  onChange,
}) => {
  return (
    <div className="flex flex-wrap gap-2">
      {options.map((option) => (
        <label
          key={option.id}
          className={`px-4 py-2 text-sm rounded-full border cursor-pointer transition-colors bg-white ${
            selectedValues.includes(option.id)
              ? "border-black"
              : "border-grey-200 hover:border-black"
          }`}
        >
          <input
            type="checkbox"
            className="hidden"
            checked={selectedValues.includes(option.id)}
            onChange={() => onChange(option.id)}
          />
          {option.name}
        </label>
      ))}
    </div>
  );
};

export default MultiCheckbox;
