import { NextRequest } from "next/server";
import { client } from "@/sanity/client";
import { groq } from "next-sanity";
import { workspace } from "@/sanity/env";

const liveUpdatesQuery = groq`
  *[_type == "liveBlogArticle" && agency == '${workspace}' && !(_id in path("drafts.**")) && slug.current == $slug][0] {
    _id,
    "liveUpdates": liveUpdates[] | order(timestamp desc) {
      _id,
      _key,
      title,
      timestamp,
      "slug": slug.current,
      "content": content,
      "featuredMedia": featuredMedia {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption,
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
      "relatedArticle": relatedArticle-> {
        _id,
        title,
        "slug": slug.current
      }
    }
  }
`;

async function fetchAuthorName(authorRef: string) {
  const authorData = await client.fetch(
    `*[_type == "author" && agency == '${workspace}' && _id == $id][0] { fullName }`,
    { id: authorRef },
  );
  return authorData?.fullName || null;
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> },
) {
  const slug = (await params).slug;

  if (!slug) {
    return new Response(JSON.stringify({ error: "Missing slug" }), {
      status: 400,
    });
  }

  return new Response(
    new ReadableStream({
      async start(controller) {
        const subscription = client
          .listen(liveUpdatesQuery, { slug })
          .subscribe(async (update) => {
            if (update.result) {
              // Process each live update
              for (const liveUpdate of update.result?.liveUpdates) {
                if (liveUpdate.authors) {
                  for (const author of liveUpdate.authors) {
                    if (author._id == null) {
                      // Author is only a reference, fetch full name
                      const authorName = await fetchAuthorName(author._ref);
                      if (authorName) {
                        author.name = authorName;
                      }
                    }
                  }
                }
              }

              // Send new data as a Server-Sent Event
              controller.enqueue(
                `data: ${JSON.stringify(update.result.liveUpdates)}\n\n`,
              );
            }
          });

        // Cleanup when client disconnects
        controller.enqueue(": keep-alive\n\n");
        req.signal.addEventListener("abort", () => {
          subscription.unsubscribe();
          controller.close();
        });
      },
    }),
    {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    },
  );
}
