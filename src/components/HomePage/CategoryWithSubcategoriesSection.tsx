import Link from "next/link";
import { Icon } from "../Icon";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import { getArticleUrl, getMediaImage, truncateTitle } from "@/utils/utils";
import AuthorsList from "../Author/AuthorsList";
import { Category } from "@/sanity/queries/HomePage/categoryArticles";
import { HomeSection } from "@/sanity/queries/HomePage/homePage";
import { Article } from "@/sanity/queries/Trending/trending";

function ViewAllLink({ href }: { href: string }) {
  return (
    <Link
      href={href}
      className="text-[14px] mr-8 inline-block hover-underline-animation relative"
      data-testid="view-all-link"
    >
      View All
      <Icon
        icon="arrow_forward"
        className="!text-sm absolute left-full ml-1 -top-px"
      />
    </Link>
  );
}

function MainArticle({ article }: { article: Article }) {
  if (!article) return null;

  return (
    <div className="w-full md:h-full lg:h-auto">
      <div className="relative mb-3.5 aspect-[16/9] w-full">
        {article.media && article.media[0] && (
          <Link
            href={getArticleUrl(
              article.category?.slug,
              article.slug,
              article.subcategory?.slug,
            )}
            className="transition-opacity hover:opacity-85"
          >
            <Image
              src={getMediaImage(article.media[0])}
              alt={article.title}
              fill
              className="object-cover rounded-2xl w-20 h-20"
            />
          </Link>
        )}

        <div className="absolute bottom-2 left-2 flex gap-2">
          {article.media?.length > 1 && (
            <div className="w-9 h-9 rounded-2xl bg-black/70 flex items-center justify-center z-10">
              <Icon icon="gallery" color="white" className="w-5" />
            </div>
          )}

          {article.media?.find((media) => media.type == "video") && (
            <div className="w-9 h-9 rounded-2xl bg-black/70 flex items-center justify-center z-10">
              <Icon icon="play" className="w-3  " />
            </div>
          )}
        </div>
      </div>

      <h3 className="text-2xl font-semibold font-titles">
        <Link
          href={getArticleUrl(
            article.category?.slug,
            article.slug,
            article.subcategory?.slug,
          )}
          className="hover-underline-animation"
        >
          {truncateTitle(article.title)}
        </Link>
      </h3>

      <AuthorsList
        authors={article.authors}
        publishedDate={article.publishedDate ?? article.created_at}
      ></AuthorsList>
    </div>
  );
}

function MainSubcategory({
  category,
  subcategory,
  className,
}: {
  category: Category;
  subcategory: Extract<
    HomeSection,
    { _type: "categoryWithSubcategoriesSection" }
  >["category"]["subcategories"][0];
  className?: string;
}) {
  if (!subcategory.articles.length) return null;

  const mainArticle = subcategory.articles[0];
  const restArticles = subcategory.articles.slice(1);

  return (
    <div
      className={`px-0 ${className} [&:nth-child(2n)]:mt-8 md:[&:nth-child(2n)]:mt-0 [&:nth-child(3n)]:hidden md:[&:nth-child(3n)]:block lg:[&:nth-child(3n)]:hidden`}
    >
      <h4 className="text-lg font-bold mb-4">{subcategory.title}</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 mb-4">
        <div className="sm:pr-2 md:pr-0 lg:pr-4 lg:border-r lg:border-grey-200 md:border-b lg:border-0 border-grey-200 md:pb-4 lg:pb-0">
          <MainArticle article={mainArticle} />
        </div>
        <div className="sm:pl-2 md:pl-0 lg:pl-4 pt-4 sm:pt-0">
          <ul className="border-b border-grey-200 pb-4">
            {restArticles.map((article) => (
              <li
                key={article.id}
                className="mt-4 pt-4 border-t first:mt-0 md:first:border-t-0 lg:first:border-t border-grey-200 font-titles"
                data-testid="secondary-article"
              >
                <Link
                  href={getArticleUrl(
                    article.category?.slug,
                    article.slug,
                    article.subcategory?.slug,
                  )}
                  className="text-lg leading-6 font-semibold hover-underline-animation"
                >
                  {truncateTitle(article.title)}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
      <div className="lg:left-1/2 lg:relative lg:pl-4">
        <ViewAllLink href={`/${category.slug}/${subcategory.slug}`} />
      </div>
    </div>
  );
}

function SecondarySubcategory({
  category,
  subcategory,
}: {
  category: Category;
  subcategory: Extract<
    HomeSection,
    { _type: "categoryWithSubcategoriesSection" }
  >["category"]["subcategories"][0];
}) {
  const mainArticle = subcategory.articles[0];
  const restArticles = subcategory.articles.slice(1);

  const groupedArticles = restArticles.reduce((acc, article, index) => {
    const groupIndex = Math.floor(index / 2);
    if (!acc[groupIndex]) {
      acc[groupIndex] = [];
    }
    acc[groupIndex].push(article);
    return acc;
  }, [] as Article[][]);

  return (
    <div className="px-0 pt-4 border-t border-grey-200 mt-8 lg:mt-10 md:first:hidden lg:first:block">
      <h4 className="text-lg font-bold mb-4">{subcategory.title}</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 gap-x-4 mb-4">
        <div className="border-b border-grey-200 pb-4 md:border-0">
          <MainArticle article={mainArticle} />
        </div>

        <ul
          className={`border-b border-grey-200 sm:border-0 md:border-b hidden sm:block pt-4 sm:pt-0 ${restArticles.length > 0 ? "pb-4" : ""}`}
        >
          {restArticles.map((article) => (
            <li
              key={article.id}
              className="mt-4 pt-4 border-t first:mt-0 border-grey-200 font-titles"
              data-testid="secondary-article"
            >
              <Link
                href={getArticleUrl(
                  article.category?.slug,
                  article.slug,
                  article.subcategory?.slug,
                )}
                className="text-lg leading-6 font-semibold hover-underline-animation"
              >
                {truncateTitle(article.title)}
              </Link>
            </li>
          ))}
        </ul>
      </div>
      <div className="sm:hidden my-4">
        <Swiper
          modules={[Navigation]}
          slidesPerView={"auto"}
          centeredSlides={false}
          className="relative news-swiper flex justify-between"
          navigation
        >
          {groupedArticles.map((articles, index) => (
            <SwiperSlide
              key={index}
              className={`!w-[90%] sm:!w-[40.6%] ${index === 0 ? "pr-4 border-r border-grey-200" : "pl-4"}`}
            >
              <ul>
                {articles.map((article) => (
                  <li
                    key={article.id}
                    className="first:pb-4 last:pt-4 last:pb-4 border-b border-grey-200"
                  >
                    <Link
                      href={getArticleUrl(
                        article.category?.slug,
                        article.slug,
                        article.subcategory?.slug,
                      )}
                      className="text-lg leading-6 font-semibold hover-underline-animation"
                    >
                      {truncateTitle(article.title)}
                    </Link>
                  </li>
                ))}
              </ul>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div className="-mt-11 absolute right-0 sm:static sm:mt-0 z-10">
        <ViewAllLink href={`/${category.slug}/${subcategory.slug}`} />
      </div>
    </div>
  );
}

interface CategoryWithSubcategoriesSectionProps {
  section: Extract<HomeSection, { _type: "categoryWithSubcategoriesSection" }>;
}

export default function CategoryWithSubcategoriesSection({
  section,
}: CategoryWithSubcategoriesSectionProps) {
  if (!section || !section.category || !section.category.subcategories) {
    return null;
  }
  const category = section.category;
  const subcategories = section.category.subcategories;
  const mainSubcategories = subcategories.slice(0, 3);
  const secondarySubcategories = subcategories.slice(2);

  if (!mainSubcategories) return null;

  return (
    <div className="container mx-auto pb-8 px-4">
      <div className="border-t pt-4 flex items-center justify-between lg:justify-start gap-x-5 gap-y-0.5">
        <h3 className="text-xl font-extrabold leading-9 uppercase">
          {category.title} News
        </h3>
        <ViewAllLink href={`/${category.slug}`} />
      </div>
      <p className="text-lg leading-6 mt-4">{category.description}</p>
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-2 gap-x-8 lg:gap-0 mt-8">
        {mainSubcategories.map((subcategory, index) => (
          <MainSubcategory
            key={subcategory.title}
            category={category}
            subcategory={subcategory}
            className={
              index === 0
                ? "lg:border-r lg:border-grey-200 lg:pr-4"
                : "border-t md:border-t-0 border-grey-200 pt-2 lg:pl-4 lg:border-t-0"
            }
          />
        ))}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-x-8">
        {secondarySubcategories.map((subcategory) => (
          <SecondarySubcategory
            key={subcategory.title}
            category={category}
            subcategory={subcategory}
          />
        ))}
      </div>
    </div>
  );
}
