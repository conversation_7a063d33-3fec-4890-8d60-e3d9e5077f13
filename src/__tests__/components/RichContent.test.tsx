import { render, screen, fireEvent } from "@testing-library/react";
import RichContent, { Content } from "@/components/ui/RichContent";
import { getPlainTextFromContent } from "@/components/ui/RichContent";
import { InlineRelatedArticlesContainer } from "@/components/ui/RichContent";
import { Author } from "@/sanity/queries/dailyStory";

const longText = "This is a long paragraph. ".repeat(100);

const mockContent = [
  {
    _key: "1",
    _type: "block",
    style: "normal",
    children: [
      { _key: "1-1", _type: "span", text: "This is a paragraph", marks: [] },
    ],
  },
  {
    _key: "2",
    _type: "block",
    style: "h1",
    children: [{ _key: "2-1", _type: "span", text: "Header 1", marks: [] }],
  },
  {
    _key: "3",
    _type: "block",
    style: "h2",
    children: [{ _key: "3-1", _type: "span", text: "Header 2", marks: [] }],
  },
  {
    _key: "4",
    _type: "block",
    style: "normal",
    children: [{ _key: "4-1", _type: "span", text: "Bold Text", marks: [] }],
  },
];

const longContent = [
  {
    _key: "long-1",
    _type: "block",
    style: "normal",
    children: [{ _key: "long-1-1", _type: "span", text: longText, marks: [] }],
  },
];

type ImageCarouselBlock = {
  _type: "imageCarousel";
  _key: string;
  images: {
    url: string;
    type: string;
    text: string;
  }[];
};

const mockInlineRelatedArticles: InlineRelatedArticlesContainer[] = [
  {
    _type: "inlineRelatedArticles",
    inlineRelatedArticles: {
      _key: "8cc3c777da9e",
      _type: "inlineRelatedArticles",
      articles: [
        {
          id: "article-id",
          title:
            "Vatican: Holy days of obligation not dispensed if transferred to a Monday",
          slug: "test-inline-article",
          category: { id: "1", title: "Test", slug: "test" },
          publishedDate: "2025-01-01T00:00:00.000Z",
          created_at: "2025-01-01T00:00:00.000Z",
          description: "desc",
          author: { name: "Author Name" } as Author,
          body: [
            {
              _key: "body-1",
              _type: "block",
              style: "normal",
              children: [
                {
                  _key: "body-1-1",
                  _type: "span",
                  text: "This is the body of the article",
                  marks: [],
                },
              ],
            },
          ],
          media: [
            {
              resource_type: "image",
              type: "image",
              url: "https://res.cloudinary.com/ewtn/video/upload/v1738948760/27391-363513438_small_dccech_oofpd9.mp4",
              text: "Pope Francis meets with the prime minister of the grand duchy, Luc Frieden, before addressing members of the government. Credit: Daniel Ibañez/EWTN News",
            },
          ],
        },
      ],
    },
    _key: "inline-1",
  },
];
jest.mock("react-tweet", () => ({
  Tweet: () => <div data-testid="mock-tweet">Mocked Tweet</div>,
}));

describe("RichContent", () => {
  it("renders RichContent without crashing", () => {
    render(<RichContent content={mockContent} />);
    expect(screen.getByText("This is a paragraph")).toBeInTheDocument();
  });

  it("renders block elements correctly (paragraph, header, strong)", () => {
    render(<RichContent content={mockContent} />);
    expect(screen.getByText("Header 1")).toBeInTheDocument();
    expect(screen.getByText("Header 2")).toBeInTheDocument();
    expect(screen.getByText("Bold Text")).toBeInTheDocument();
  });

  it("renders inline related articles", () => {
    render(<RichContent content={mockInlineRelatedArticles} />);
    expect(
      screen.getAllByText(
        "Vatican: Holy days of obligation not dispensed if transferred to a Monday",
      ),
    ).toHaveLength(2);
  });

  it("does not apply firstLetterBig style when prop is not passed", () => {
    render(<RichContent content={mockContent} />);
    const paragraph = screen.getByText("This is a paragraph");
    expect(paragraph).not.toHaveClass("first-letter:text-5xl");
    expect(paragraph).not.toHaveClass("first-letter:font-bold");
  });

  it("responds to screen resizing", () => {
    render(<RichContent content={mockContent} />);
    fireEvent.resize(window, { target: { innerWidth: 500 } });
    expect(screen.getByText("Header 1")).toBeInTheDocument();
  });

  it("renders inline related articles when present in content", () => {
    render(<RichContent content={mockInlineRelatedArticles} />);

    expect(
      screen.getAllByText(
        "Vatican: Holy days of obligation not dispensed if transferred to a Monday",
      ),
    ).toHaveLength(2);
  });

  it("displays 'Read More' button when content overflows", () => {
    const { container } = render(
      <RichContent content={longContent} shouldTruncate={true} />,
    );

    const contentDiv = container.querySelector(
      '[data-testid="rich-content"] div',
    );

    Object.defineProperty(contentDiv, "scrollHeight", {
      configurable: true,
      value: 1000,
    });
    Object.defineProperty(contentDiv, "clientHeight", {
      configurable: true,
      value: 220,
    });

    fireEvent(window, new Event("resize"));

    expect(
      screen.getByRole("button", { name: /read more/i }),
    ).toBeInTheDocument();
  });

  it("toggles to 'Read Less' after clicking 'Read More'", () => {
    const { container } = render(
      <RichContent content={longContent} shouldTruncate={true} />,
    );

    const contentDiv = container.querySelector(
      '[data-testid="rich-content"] div',
    );

    Object.defineProperty(contentDiv, "scrollHeight", { value: 1000 });
    Object.defineProperty(contentDiv, "clientHeight", { value: 220 });

    fireEvent(window, new Event("resize"));

    const button = screen.getByRole("button", { name: /read more/i });
    fireEvent.click(button);

    expect(
      screen.getByRole("button", { name: /read less/i }),
    ).toBeInTheDocument();
  });

  it("toggles content back to collapsed when clicking 'Read Less'", () => {
    const { container } = render(
      <RichContent content={longContent} shouldTruncate={true} />,
    );

    const contentDiv = container.querySelector(
      '[data-testid="rich-content"] div',
    );

    Object.defineProperty(contentDiv, "scrollHeight", { value: 1000 });
    Object.defineProperty(contentDiv, "clientHeight", { value: 220 });

    fireEvent(window, new Event("resize"));

    const readMoreBtn = screen.getByRole("button", { name: /read more/i });
    fireEvent.click(readMoreBtn);

    const readLessBtn = screen.getByRole("button", { name: /read less/i });
    fireEvent.click(readLessBtn);

    expect(
      screen.getByRole("button", { name: /read more/i }),
    ).toBeInTheDocument();
  });

  it("does not show 'Read More' or 'Read Less' buttons when shouldTruncate is not passed", () => {
    const { container } = render(<RichContent content={longContent} />);

    const contentDiv = container.querySelector(
      '[data-testid="rich-content"] div',
    );

    Object.defineProperty(contentDiv, "scrollHeight", {
      configurable: true,
      value: 1000,
    });
    Object.defineProperty(contentDiv, "clientHeight", {
      configurable: true,
      value: 220,
    });

    fireEvent(window, new Event("resize"));

    expect(
      screen.queryByRole("button", { name: /read more/i }),
    ).not.toBeInTheDocument();

    expect(
      screen.queryByRole("button", { name: /read less/i }),
    ).not.toBeInTheDocument();
  });

  it("renders image carousel with images and captions", () => {
    const mockImageCarousel: ImageCarouselBlock[] = [
      {
        _type: "imageCarousel",
        _key: "carousel-1",
        images: [
          {
            url: "https://res.cloudinary.com/demo/image/upload/sample.jpg",
            type: "image",
            text: "Sample caption 1",
          },
          {
            url: "https://res.cloudinary.com/demo/image/upload/sample2.jpg",
            type: "image",
            text: "Sample caption 2",
          },
        ],
      },
    ];
    render(<RichContent content={mockImageCarousel as Content[]} />);
    expect(screen.getByText("Sample caption 1")).toBeInTheDocument();
    expect(screen.getByText("Sample caption 2")).toBeInTheDocument();
  });

  it("returns empty string if content is not an array", () => {
    // @ts-expect-error: purposely passing non-array
    render(<RichContent content={null} />);
    // Should not throw and should render nothing
    expect(screen.queryByTestId("rich-content")).toBeInTheDocument();
  });

  it("does not truncate when shouldTruncate is false", () => {
    render(<RichContent content={longContent} shouldTruncate={false} />);
    expect(
      screen.queryByRole("button", { name: /read more/i }),
    ).not.toBeInTheDocument();
  });

  it("handles block with nested content array (recursion)", () => {
    const nestedContent = [
      {
        _key: "outer",
        _type: "block",
        children: [
          { _key: "outer-1", _type: "span", text: "Outer text", marks: [] },
        ],
        content: [
          {
            _key: "inner",
            _type: "block",
            children: [
              { _key: "inner-1", _type: "span", text: "Inner text", marks: [] },
            ],
          },
        ],
      },
    ];
    render(<RichContent content={nestedContent as Content[]} />);
    expect(screen.getByText("Outer text")).toBeInTheDocument();
    expect(screen.getByTestId("rich-content")).toBeInTheDocument();
  });
});

describe("getPlainTextFromContent", () => {
  it("returns empty string if content is not an array", () => {
    expect(getPlainTextFromContent(null)).toBe("");
    expect(getPlainTextFromContent(undefined)).toBe("");
    expect(getPlainTextFromContent("string")).toBe("");
    expect(getPlainTextFromContent(123)).toBe("");
    expect(getPlainTextFromContent({})).toBe("");
  });

  it("recursively extracts text from nested content arrays", () => {
    const nestedContent = [
      {
        _key: "outer",
        _type: "block",
        children: [
          { _key: "outer-1", _type: "span", text: "Outer text", marks: [] },
        ],
        content: [
          {
            _key: "inner",
            _type: "block",
            children: [
              { _key: "inner-1", _type: "span", text: "Inner text", marks: [] },
            ],
          },
        ],
      },
    ];
    expect(getPlainTextFromContent(nestedContent)).toBe("Outer textInner text");
  });
  it("ignores children without text property as string", () => {
    const content = [
      {
        _key: "1",
        _type: "block",
        children: [
          { _key: "1-1", _type: "span", text: 123, marks: [] },
          { _key: "1-2", _type: "span", marks: [] },
        ],
      },
    ];
    expect(getPlainTextFromContent(content)).toBe("");
  });

  it("returns empty string for block without children or content", () => {
    const content = [
      {
        _key: "1",
        _type: "block",
      },
    ];
    expect(getPlainTextFromContent(content)).toBe("");
  });
});
