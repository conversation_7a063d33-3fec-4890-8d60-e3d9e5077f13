import { defineQuery } from "next-sanity";

/**
 * Types for the Our Faith Page Schema
 */

export interface FaithCardDisplay {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  link: string;
  published?: boolean;
}

export interface OurFaithPageType {
  id: string;
  title: string;
  description: string;
  faithCards: FaithCardDisplay[];
}

/**
 * Query for fetching the Our Faith Page data
 */
export const ourFaithPageQuery = defineQuery(`
  *[_type == "ourFaithPage" && !(_id in path("drafts.**"))][0]{
    "id": _id,
    title,
    description,
    "faithCards": faithCards[published == true] {
      title,
      description,
      "imageUrl": image.secure_url,
      link,
      published
    }
  }
`);
