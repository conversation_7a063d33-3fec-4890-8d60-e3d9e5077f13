import { render, screen, fireEvent } from "@testing-library/react";
import { Author } from "@/sanity/queries/Author/author";
import About from "@/components/Author/About";
import { BlockContent } from "@/components/Author/RichContent";

jest.mock("@/components/Author/RichContent", () => ({
  __esModule: true,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  default: ({ _content }: { _content: BlockContent[] }) => (
    <div data-testid="rich-content">Mock <PERSON>ontent</div>
  ),
}));

jest.mock("@/components/Icon", () => ({
  Icon: ({ icon }: { icon: string }) => (
    <div data-testid={`icon-${icon}`}>Icon: {icon}</div>
  ),
}));

Object.defineProperty(window, "scrollTo", { value: jest.fn(), writable: true });
Object.defineProperty(window, "scrollY", { value: 0, writable: true });
Object.defineProperty(window.history, "pushState", {
  value: jest.fn(),
  writable: true,
});

Element.prototype.getBoundingClientRect = jest.fn(() => ({
  top: 100,
  left: 0,
  right: 0,
  bottom: 0,
  width: 100,
  height: 100,
  x: 0,
  y: 100,
  toJSON: () => ({}),
}));

const mockAuthor: Partial<Author> = {
  name: "John Doe",
  email: "<EMAIL>",
  about: [
    {
      title: "Biography",
      description: [
        {
          _key: "1",
          _type: "block",
          style: "normal",
          markDefs: [],
          children: [],
        },
      ],
      blockWithSocialLinks: false,
    },
    {
      title: "Experience",
      description: [
        {
          _key: "2",
          _type: "block",
          style: "normal",
          markDefs: [],
          children: [],
        },
      ],
      blockWithSocialLinks: true,
    },
  ],
  socialMedia: [
    {
      icon: "twitter",
      link: "https://twitter.com/johndoe",
      nickname: "@johndoe",
    },
  ],
};

describe("About Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    Object.defineProperty(window, "scrollY", { value: 0 });
  });

  it("renders the component with correct sections", () => {
    render(<About author={mockAuthor as Author} />);

    expect(screen.getAllByText("Get to know me").length).toBeGreaterThan(0);
    expect(screen.getAllByText("Biography").length).toBeGreaterThan(0);
    expect(screen.getAllByText("Experience").length).toBeGreaterThan(0);
  });

  it("displays the social media links", () => {
    render(<About author={mockAuthor as Author} />);

    expect(screen.getByText("@johndoe")).toBeInTheDocument();
    expect(screen.getByTestId("icon-twitter")).toBeInTheDocument();
    expect(screen.getByTestId("icon-email")).toBeInTheDocument();
  });

  it("scrolls to the correct section when menu item is clicked", () => {
    render(<About author={mockAuthor as Author} />);

    const experienceButton = screen.getAllByRole("button")[1];
    fireEvent.click(experienceButton);

    expect(window.scrollTo).toHaveBeenCalledWith({
      top: expect.any(Number),
      behavior: "smooth",
    });

    expect(window.history.pushState).toHaveBeenCalledWith(
      null,
      "",
      "#Experience",
    );
  });

  it("highlights the active section based on scroll position", () => {
    render(<About author={mockAuthor as Author} />);

    const biographyButton = screen.getAllByRole("button")[0];
    expect(biographyButton.className).toContain("before:bg-black");

    Object.defineProperty(window, "scrollY", { value: 300 });
    fireEvent.scroll(window);
  });
});
