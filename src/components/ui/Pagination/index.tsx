import { PaginationProps } from "./types";
import { Icon } from "@/components/Icon";

const Pagination: React.FC<PaginationProps> = ({
  totalItems,
  itemsPerPage,
  currentPage,
  onPageChange,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (totalPages <= 1) return null;

  return (
    <div className="flex justify-center gap-2 mt-8">
      {currentPage > 1 && (
        <span
          className="w-10 h-10 flex items-center justify-center bg-red-700 hover:bg-red-800 transition-all text-white rounded-xl cursor-pointer text-[13px]"
          onClick={() => onPageChange(Math.min(currentPage - 1, totalPages))}
        >
          <Icon icon="seeMore" className="rotate-180" />
        </span>
      )}

      {Array.from({ length: totalPages }).map((_, index) => (
        <span
          key={index}
          onClick={() => onPageChange(index + 1)}
          className={`w-10 h-10 flex items-center justify-center rounded-xl cursor-pointer text-[13px] transition-all ${
            currentPage === index + 1
              ? "bg-red-700 hover:bg-red-800 text-white"
              : "hover:border-black border border-grey-200"
          }`}
        >
          {index + 1}
        </span>
      ))}

      {currentPage < totalPages && (
        <span
          className="w-10 h-10 flex items-center justify-center bg-red-700 hover:bg-red-800 transition-all text-white rounded-xl cursor-pointer text-[13px]"
          onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
        >
          <Icon icon="seeMore" />
        </span>
      )}
    </div>
  );
};

export default Pagination;
