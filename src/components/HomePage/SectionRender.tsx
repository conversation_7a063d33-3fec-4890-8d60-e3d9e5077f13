import SectionMapper from "./SectionMapper";
import { HomeSection } from "@/sanity/queries/HomePage/homePage";
import { Article } from "@/sanity/queries/Trending/trending";

export default function SectionRender({
  sections = [],
  trendingArticles = [],
}: {
  sections: HomeSection[];
  trendingArticles: Article[];
}) {
  return (
    <>
      {sections.map((section, index) => (
        <SectionMapper
          key={index}
          section={section}
          trendingArticles={trendingArticles}
        />
      ))}
    </>
  );
}
