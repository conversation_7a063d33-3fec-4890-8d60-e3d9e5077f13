import { Article } from "@/app/api/articles/bookmarks/route";

export interface NewsItem {
  id: number;
  category: string;
  topicIds: number[];
  title: string;
  author: Option;
  time: string;
}

export interface NewsCardProps {
  item: NewsItem;
  onRemoveAuthor?: (id: number) => void;
}

export interface Option {
  id: number;
  name: string;
  role: string;
}

export interface ArticleProps {
  item: Article;
  onRemoveAuthor?: (id: string) => void;
}
