import { IconName } from "@/components/Icon";
import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const authorQuery = defineQuery(`
*[
  _type == "author" && 
  agency == '${workspace}' &&
  !(_id in path("drafts.**")) &&
  slug.current == $slug
][0] {
  "id": _id,
  "name": fullName,
  "jobTitle": jobTitle->name,  
  "location": location->name, 
  "slug": slug.current,
  shortBio,
  longBio,
  email,
  "created_at": _createdAt,
  image {
    "url": secure_url,
  },
  "socialMedia": socialMedia[] {
    icon,
    link,
    nickname
  },
  "about": about[] {
    title,
    "description": description[] {
      _key,
      _type,
      "children": children[] {
        _key,
        _type,
        "text": text,
        "marks": marks[]
      },
      "style": style,
      "markDefs": markDefs[]
    },
    blockWithSocialLinks
  },
  "articlesCount": count(*[
    _type == "dailyStoryArticle" && 
    agency == '${workspace}' &&
    ^._id in authors[]._ref
  ]),
  "articles": *[
      _type == "dailyStoryArticle" && 
      agency == '${workspace}' &&
      ^._id in authors[]._ref
  ] | order(publishedDate desc) [0...10] {
    "id": _id,
    title,
    "slug": slug.current,
    publishedDate,
    description,
    "authors": authors[]-> {
      "name": fullName,
      "slug": slug.current,
      image {
        "url": secure_url,
        resource_type,
        "type": resource_type,
      },
    },
    _updatedAt,
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "media": featuredMedia[0] {
      "url": secure_url,
      "type": resource_type
    },
    "created_at": _createdAt,
  }
}
`);

export const moreArticlesQuery = defineQuery(`
{
  "items": *[
    _type == "dailyStoryArticle" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    $authorId in authors[]._ref
  ] | order(publishedDate desc) [$start...$end] {
    "id": _id,
    title,
    "slug": slug.current,
    publishedDate,
    description,
    "authors": authors[]-> {
      "name": fullName,
      "slug": slug.current,
      image {
        "url": secure_url,
        resource_type,
        "type": resource_type,
      },
    },
    _updatedAt,
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "media": featuredMedia[0] {
      "url": secure_url,
      "type": resource_type
    },
    "created_at": _createdAt
  },
  "totalCount": count(*[
    _type == "dailyStoryArticle" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    $authorId in authors[]._ref
  ])
}
`);

export type AuthorSocialMedia = {
  icon: IconName;
  link: string;
  nickname: string;
};

export type MarkDef = {
  _key: string;
  _type: string;
  href?: string;
  target?: string;
};

export type BlockContent = {
  _key: string;
  _type: string;
  children: {
    _key: string;
    _type: string;
    text: string;
    marks: string[];
  }[];
  style: string;
  markDefs: MarkDef[];
};

export type AboutAuthor = {
  title: string;
  description: BlockContent[];
  blockWithSocialLinks: boolean;
};

export type Article = {
  id: string;
  title: string;
  slug: string;
  publishedDate: string;
  _updatedAt: string;
  description: string;
  authors: {
    slug: string;
    name: string;
    image?: {
      url: string;
      resource_type: string;
      text?: string;
      type: string;
    };
  }[];
  category: {
    id: string;
    title: string;
    slug: string;
  };
  subcategory?: {
    id: string;
    title: string;
    slug: string;
  };
  media: {
    url: string;
    type: string;
  };
  created_at: string;
};

export type Author = {
  id: string;
  name: string;
  jobTitle: string;
  location: string;
  slug: string;
  shortBio: string;
  longBio: string;
  email: string;
  created_at: string;
  image?: {
    url: string;
  };
  socialMedia: AuthorSocialMedia[];
  about: AboutAuthor[];
  articlesCount: number;
  articles: Article[];
};

export type MoreArticlesResponse = {
  articles: Article[];
  totalCount: number;
};
