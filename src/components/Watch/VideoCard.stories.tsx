import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@storybook/react";
import { VideoCard } from "./VideoCard";
import { VideoItem } from "@/app/watch/mocks";

const sampleVideo: VideoItem = {
  href: "/video/123",
  title: "Amazing Video Title That Could Be Long",
  caption: "Video Caption",
  description:
    "This is a detailed description of the video content that might be a little long and will get truncated accordingly.",
  timestamp: new Date().toISOString(),
  url: "https://res.cloudinary.com/ewtn-dev/video/upload/v1748440380/Will_the_Vatican_Host_Russia-Ukraine_Peace_Talks___EWTN_News_In_Depth_May_23_2025_ixdqt8.mp4",
  imageUrl:
    "https://res.cloudinary.com/ewtn-dev/video/upload/v1748440380/Will_the_Vatican_Host_Russia-Ukraine_Peace_Talks___EWTN_News_In_Depth_May_23_2025_ixdqt8.jpg",
};

const sampleThumbnail =
  "https://res.cloudinary.com/ewtn-dev/video/upload/v1748440380/Will_the_Vatican_Host_Russia-Ukraine_Peace_Talks___EWTN_News_In_Depth_May_23_2025_ixdqt8.jpg";

const meta: Meta<typeof VideoCard> = {
  title: "Components/VideoCard",
  component: VideoCard,
  tags: ["autodocs"],
  argTypes: {
    mode: {
      control: { type: "radio" },
      options: ["column", "row"],
    },
    showDescription: { control: "boolean" },
    showCaption: { control: "boolean" },
    showTimestamp: { control: "boolean" },
    truncateTitleAt: { control: { type: "number", min: 10, max: 200 } },
    truncateDescriptionAt: { control: { type: "number", min: 10, max: 200 } },
    captionClassName: { control: "text" },
    titleClassName: { control: "text" },
    descriptionClassName: { control: "text" },
  },
};

export default meta;

type Story = StoryObj<typeof VideoCard>;

export const ColumnMode: Story = {
  args: {
    video: sampleVideo,
    thumbnail: sampleThumbnail,
    mode: "column",
    showDescription: true,
    showCaption: true,
    showTimestamp: true,
  },
};

export const RowMode: Story = {
  args: {
    video: sampleVideo,
    thumbnail: sampleThumbnail,
    mode: "row",
    showDescription: true,
    showCaption: true,
    showTimestamp: true,
  },
};

export const NoDescription: Story = {
  args: {
    video: sampleVideo,
    thumbnail: sampleThumbnail,
    mode: "column",
    showDescription: false,
    showCaption: true,
    showTimestamp: true,
  },
};

export const CustomClasses: Story = {
  args: {
    video: sampleVideo,
    thumbnail: sampleThumbnail,
    mode: "column",
    showDescription: true,
    captionClassName: "text-red-500 italic",
    titleClassName: "text-xl font-bold text-blue-600",
    descriptionClassName: "text-gray-500 text-sm",
  },
};
