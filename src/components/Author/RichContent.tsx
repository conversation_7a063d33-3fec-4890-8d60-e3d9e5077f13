"use client";

import React from "react";
import { PortableText, PortableTextReactComponents } from "@portabletext/react";

interface Body {
  _key: string;
  _type: "block";
  children?: {
    _key: string;
    _type: "span";
    text: string;
    marks?: string[];
  }[];
  style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote" | "dropCap";
  markDefs?: {
    _key: string;
    _type: "link";
    href: string;
    target?: string;
  }[];
  listItem?: "bullet" | "number";
  textBody?: {
    style: string;
    _key: string;
    markDefs: Array<{ _type: string; href?: string; _key: string }>;
    children: Array<{
      _type: string;
      marks: string[];
      text: string;
      _key: string;
    }>;
  }[];
}
interface BlockContent {
  _key: string;
  _type: string;
  style?: string;
  children:
    | Array<{
        _key: string;
        _type: string;
        marks: string[];
        text: string;
      }>
    | undefined;
}

type Content = BlockContent | Body;
interface RichContentProps {
  content: Content[];
}

const RichContent: React.FC<RichContentProps> = ({ content }) => {
  const portableTextComponents: Partial<PortableTextReactComponents> = {
    block: {
      normal: ({ children }: { children?: React.ReactNode }) => (
        <p className={"text-[18px] mt-3"}>{children}</p>
      ),
      h1: ({ children }: { children?: React.ReactNode }) => (
        <h1 className="text-3xl xl:text-4xl">{children}</h1>
      ),
      h2: ({ children }: { children?: React.ReactNode }) => (
        <h2 className="text-2xl xl:text-3xl font-semibold mt-11">{children}</h2>
      ),
      h3: ({ children }: { children?: React.ReactNode }) => (
        <h3 className="text-xl xl:text-2xl font-medium mt-9">{children}</h3>
      ),
      h4: ({ children }: { children?: React.ReactNode }) => (
        <h4 className="text-l xl:text-xl font-medium mt-7">{children}</h4>
      ),
      blockquote: ({ children }: { children?: React.ReactNode }) => (
        <blockquote>{children}</blockquote>
      ),
    },

    marks: {
      strong: ({ children }: { children: React.ReactNode }) => (
        <strong>{children}</strong>
      ),

      em: ({ children }: { children: React.ReactNode }) => <em>{children}</em>,

      underline: ({ children }: { children: React.ReactNode }) => (
        <span style={{ textDecoration: "underline" }}>{children}</span>
      ),

      code: ({ children }: { children: React.ReactNode }) => (
        <code>{children}</code>
      ),

      link: ({
        children,
        value,
      }: {
        children: React.ReactNode;
        value?: { href: string; target?: string };
      }) => (
        <a
          href={value?.href || "#"}
          target={value?.target || "_blank"}
          rel="noopener noreferrer"
          className="text-red-700 hover-underline-animation-red"
        >
          {children}
        </a>
      ),
    },

    list: {
      bullet: ({ children }: { children?: React.ReactNode }) => (
        <ul className="text-[20px] list-disc pl-5">{children}</ul>
      ),
      number: ({ children }: { children?: React.ReactNode }) => (
        <ol className="list-decimal pl-5">{children}</ol>
      ),
    },

    listItem: {
      bullet: ({ children }: { children?: React.ReactNode }) => (
        <li className="ml-4">{children}</li>
      ),
      number: ({ children }: { children?: React.ReactNode }) => (
        <li className="ml-4">{children}</li>
      ),
    },
  };

  return (
    <div className="rich-content mb-1" data-testid="rich-content">
      <PortableText value={content} components={portableTextComponents} />
    </div>
  );
};

export type { Content, BlockContent, RichContentProps, Body };

export default RichContent;
