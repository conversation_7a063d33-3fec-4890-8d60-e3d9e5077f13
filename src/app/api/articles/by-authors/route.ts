import { NextRequest } from "next/server";
import { sanityFetch } from "@/sanity/client";
import { articlesByAuthorsQuery } from "@/sanity/queries/Author/authorFollowers";

export async function POST(req: NextRequest) {
  try {
    const { authorsIds } = await req.json();
    if (!Array.isArray(authorsIds) || authorsIds.length === 0) {
      return new Response(JSON.stringify({ items: [] }), { status: 200 });
    }
    const articles = await sanityFetch({
      query: articlesByAuthorsQuery,
      params: { authorsIds },
    });

    return new Response(JSON.stringify({ items: articles }), { status: 200 });
  } catch (err) {
    console.error("Error fetching articles by authors:", err);
    return new Response(JSON.stringify({ error: "Server error" }), {
      status: 500,
    });
  }
}
