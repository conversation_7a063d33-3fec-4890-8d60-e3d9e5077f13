import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const categoryQuery = `
  *[
    _type == "category" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    slug.current == $slug
  ][0] {
    "id": _id,
    title,
    "slug": slug.current,
    "image": image.secure_url,
    seoMetadata,
    subtitle,
    "subcategories": subcategories[]->{
      "id": _id,
      title,
      "slug": slug.current,
      "hasArticles": defined(*[
        _type == "dailyStoryArticle" && 
        agency == '${workspace}' &&
        subcategory._ref == ^._id
      ][0]._id),
    },
    "articlesCount": count(*[
      _type == "dailyStoryArticle" && 
      agency == '${workspace}' &&
      !(_id in $ignoreArticlesIds) &&
      category._ref == ^._id &&
      (
        !defined($subcategoryId) || 
        subcategory._ref == $subcategoryId
      )
    ]),
    "articles": *[
      _type == "dailyStoryArticle" &&
      agency == '${workspace}' && 
      !(_id in $ignoreArticlesIds) &&
      category._ref == ^._id &&
      (
        !defined($subcategoryId) || 
        subcategory._ref == $subcategoryId
      )
    ] | order(publishedDate desc) [0...10] {
      "id": _id,
      title,
      "slug": slug.current,
      publishedDate,
      description,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type
      },
      "authors": authors[]-> {
          "slug": slug.current,
          "name": fullName,
          image {
          "url": secure_url
          }
      },
      "created_at": _createdAt,
    },
  }
`;

export const moreArticlesQuery = defineQuery(`
  {
    "items": *[
      _type == "dailyStoryArticle" &&
      agency == '${workspace}' &&
      (
        !defined($ignoreArticlesIds) || count($ignoreArticlesIds) == 0 || !(_id in $ignoreArticlesIds)
      ) &&
      !(_id in path("drafts.**")) &&
      category._ref == $categoryId &&
      (
        !defined($subcategoryId) || 
        subcategory._ref == $subcategoryId
      )
    ] | order(publishedDate desc) [$start...$end] {
      "id": _id,
      title,
      "slug": slug.current,
      publishedDate,
      description,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
      "created_at": _createdAt
    },
    "totalCount": count(*[
      _type == "dailyStoryArticle" &&
      agency == '${workspace}' &&
      (
        !defined($ignoreArticlesIds) || count($ignoreArticlesIds) == 0 || !(_id in $ignoreArticlesIds)
      ) &&
      !(_id in path("drafts.**")) &&
      category._ref == $categoryId &&
      (
        !defined($subcategoryId) || 
        subcategory._ref == $subcategoryId
      )
    ])
  }
`);

export type Category = {
  id: string;
  title: string;
  slug: string;
  subtitle: string;
  articles: Article[];
  subcategories: Subcategory[];
  articlesCount: number;
};

export type Subcategory = {
  id: string;
  title: string;
  slug: string;
  hasArticles: boolean;
};

export type Article = {
  id: string;
  title: string;
  slug: string;
  publishedDate: string;
  description: string;
  category: {
    id: string;
    title: string;
    slug: string;
  };
  subcategory: {
    id: string;
    title: string;
    slug: string;
  };
  media: {
    url: string;
    type: string;
  };
  created_at: string;
  authors: Author[];
};

export type Author = {
  slug: string;
  name: string;
  image?: {
    url: string;
  };
};
