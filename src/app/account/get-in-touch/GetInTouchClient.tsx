"use client";

import { useState } from "react";
import AccountLayout from "@/components/Account/Layout";
import PatronSaint from "@/components/Account/PatronSaint";
import ContactUsForm from "@/components/ContactForm/page";
import { FormValues } from "@/types/contact-us";
import { IMAGES_PATH } from "@/constants";
import Image from "next/image";

export default function GetInTouchClient() {
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  const handleFormSubmit = async (data: FormValues) => {
    setIsFormSubmitting(true);
    try {
      const response = await fetch("/api/email", {
        method: "POST",
        body: JSON.stringify(data),
      });
      console.log("Response from API:", response);
      if (!response.ok || response.status !== 200) {
        console.error("Failed to submit form");
        setIsFormSubmitting(false);
        setShowError(true);
        return;
      } else {
        setIsFormSubmitting(false);
        setShowSuccess(true);
        setShowError(false);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setIsFormSubmitting(false);
      setShowError(true);
      return;
    }
  };

  return (
    <AccountLayout>
      <AccountLayout.Left>
        <div className="hidden lg:hidden mt-8">
          <PatronSaint />
        </div>
      </AccountLayout.Left>

      <AccountLayout.Main>
        {!showSuccess && !showError ? (
          <div>
            <h2 className="font-titles text-[25px] lg:text-[34px] font-semibold">
              Get in Touch
            </h2>
            <h3 className="text-[16px] lg:text-[18px]">
              Let us know how we can assist you
            </h3>

            <ContactUsForm
              isSubmitting={isFormSubmitting}
              onSubmit={handleFormSubmit}
            />
          </div>
        ) : null}

        {showSuccess ? (
          <div className="max-w-96 mx-auto flex flex-col items-center">
            <Image
              src={`${IMAGES_PATH}/account/get-in-touch-success.png`}
              alt="Success"
              width={212}
              height={212}
              className="max-w-full"
            />
            <p className="text-sm text-center">
              Your inquiry has been submitted successfully. Thank you for
              contacting us. We will get back to you as soon as possible.
            </p>
          </div>
        ) : null}

        {showError ? (
          <div className="max-w-96 mx-auto flex flex-col items-center">
            <Image
              src={`${IMAGES_PATH}/account/get-in-touch-error.png`}
              alt="Error"
              width={307}
              height={247}
              className="max-w-full"
            />
            <p className="text-sm text-center mt-4">
              Oops! It seems there was an issue with your inquiry submission.
              Please try resending it or reach out to <NAME_EMAIL>
              for further assistance.
            </p>
          </div>
        ) : null}
      </AccountLayout.Main>

      <AccountLayout.Right>
        <PatronSaint />
      </AccountLayout.Right>
    </AccountLayout>
  );
}
