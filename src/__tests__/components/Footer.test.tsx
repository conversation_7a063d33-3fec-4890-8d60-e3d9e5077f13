import { render, screen } from "@testing-library/react";
import Footer from "@/components/Footer/";
import FooterMobile from "@/components/Footer/footerMobile";
import FooterDesktop from "@/components/Footer/footerDesktop";
import {
  ComplianceLinkQueryResult,
  NavigationMenuQueryResult,
  SocialMediaQueryResult,
} from "@/sanity/queries/layout";

// Mock FooterMobile and FooterDesktop components
jest.mock("@/components/Footer/footerMobile", () =>
  jest.fn(() => <div data-testid="footer-mobile" />),
);
jest.mock("@/components/Footer/footerDesktop", () =>
  jest.fn(() => <div data-testid="footer-desktop" />),
);

describe("Footer Component", () => {
  const navigationMenu: NavigationMenuQueryResult = {
    title: "Main Menu",
    sections: [
      {
        title: "Section 1",
        slug: {
          _type: "slug",
          current: "section-1",
        },
        menuItems: [
          {
            title: "Item 1",
            linkType: "internal",
            internalLink: {
              _type: "slug",
              current: "item-1",
            },
            externalLink: "",
          },
        ],
      },
    ],
  };

  const socialMediaItems: SocialMediaQueryResult = [
    {
      _id: "1",
      _type: "socialMedia",
      _createdAt: "2022-01-01T00:00:00Z",
      _updatedAt: "2022-01-01T00:00:00Z",
      _rev: "1",
      link: "https://facebook.com/ewtn",
      icon: "facebook",
    },
    {
      _id: "2",
      _type: "socialMedia",
      _createdAt: "2022-01-01T00:00:00Z",
      _updatedAt: "2022-01-01T00:00:00Z",
      _rev: "1",
      link: "https://instagram.com/ewtn",
      icon: "instagram",
    },
  ];

  const complianceLinkItems: ComplianceLinkQueryResult = [
    {
      _id: "1",
      _type: "complianceLink",
      _createdAt: "2022-01-01T00:00:00Z",
      _updatedAt: "2022-01-01T00:00:00Z",
      _rev: "1",
      title: "Privacy Policy",
      slug: {
        _type: "slug",
        current: "privacy-policy",
      },
      isOnReducedFooter: false,
    },
    {
      _id: "2",
      _type: "complianceLink",
      _createdAt: "2022-01-01T00:00:00Z",
      _updatedAt: "2022-01-01T00:00:00Z",
      _rev: "1",
      title: "Terms And Conditions",
      slug: {
        _type: "slug",
        current: "terms-and-conditions",
      },
      isOnReducedFooter: true,
    },
  ];

  describe("Footer", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("renders FooterMobile on small screens", () => {
      // Mock window width for mobile
      global.innerWidth = 500;
      window.dispatchEvent(new Event("resize"));

      render(
        <Footer
          navigationMenu={navigationMenu}
          socialMediaItems={socialMediaItems}
          complianceLinkItems={complianceLinkItems}
          isFooterReduced={false}
        />,
      );

      expect(screen.getByTestId("footer-mobile")).toBeInTheDocument();
    });

    it("renders FooterDesktop on larger screens", () => {
      // Mock window width for desktop
      global.innerWidth = 1024;
      window.dispatchEvent(new Event("resize"));

      render(
        <Footer
          navigationMenu={navigationMenu}
          socialMediaItems={socialMediaItems}
          complianceLinkItems={complianceLinkItems}
          isFooterReduced={false}
        />,
      );

      expect(screen.getByTestId("footer-desktop")).toBeInTheDocument();
    });

    it("respects isFooterReduced", () => {
      render(
        <Footer
          navigationMenu={navigationMenu}
          socialMediaItems={socialMediaItems}
          complianceLinkItems={complianceLinkItems}
          isFooterReduced={false}
        />,
      );

      // Assert FooterDesktop uses isFooterReduced
      expect(FooterDesktop).toHaveBeenCalledWith(
        expect.objectContaining({ isFooterReduced: false }),
        {},
      );

      // Assert FooterMobile uses isFooterReduced
      expect(FooterMobile).toHaveBeenCalledWith(
        expect.objectContaining({ isFooterReduced: false }),
        {},
      );
    });
  });
});
