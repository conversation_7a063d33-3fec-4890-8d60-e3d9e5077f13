import { NextRequest } from "next/server";
import { auth0 } from "@/lib/auth0";
import {
  addBookmark,
  getBookmarkCount,
  getUserBookmark,
  removeBookmark,
} from "@/app/api/services/articles/bookmarks";
import {
  unauthorizedResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { POST, GET, DELETE } from "./route";

// Mock dependencies
jest.mock("@/lib/auth0", () => ({
  auth0: {
    getSession: jest.fn(),
  },
}));

jest.mock("@/app/api/services/articles/bookmarks", () => ({
  addBookmark: jest.fn(),
  getBookmarkCount: jest.fn(),
  getUserBookmark: jest.fn(),
  removeBookmark: jest.fn(),
}));

jest.mock("@/app/api/util/errors", () => ({
  unauthorizedResponse: jest.fn(() => ({
    status: 401,
    message: "Unauthorized",
  })),
  badRequestResponse: jest.fn((message: string) => ({ status: 400, message })),
  successResponse: jest.fn((message: string, status: number) => ({
    status,
    message,
  })),
  errorResponse: jest.fn((error) => ({
    status: 500,
    message: error.message,
  })),
  successResponseWithData: jest.fn((data, status: number) => ({
    status,
    data,
  })),
}));

describe("Bookmark API Routes", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /api/bookmark", () => {
    it("should return unauthorized if no user session", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce(null);

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(unauthorizedResponse).toHaveBeenCalled();
      expect(response).toEqual({ status: 401, message: "Unauthorized" });
    });

    it("should add a bookmark and return success response", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(addBookmark).toHaveBeenCalledWith("user123", "123");
      expect(successResponse).toHaveBeenCalledWith("Article bookmarked", 201);
      expect(response).toEqual({ status: 201, message: "Article bookmarked" });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (addBookmark as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to add bookmark"),
      );

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to add bookmark"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to add bookmark",
      });
    });
  });

  describe("GET /api/bookmark", () => {
    it("should return bookmark count and user bookmark status", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (getBookmarkCount as jest.Mock).mockResolvedValueOnce({
        bookmark_count: 10,
      });
      (getUserBookmark as jest.Mock).mockResolvedValueOnce(true);

      const response = await GET({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(getBookmarkCount).toHaveBeenCalledWith("123");
      expect(getUserBookmark).toHaveBeenCalledWith("user123", "123");
      expect(successResponseWithData).toHaveBeenCalledWith(
        { bookmarkCount: 10, userBookmarked: true },
        200,
      );
      expect(response).toEqual({
        status: 200,
        data: { bookmarkCount: 10, userBookmarked: true },
      });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (getBookmarkCount as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to get bookmark count"),
      );

      const response = await GET({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to get bookmark count"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to get bookmark count",
      });
    });
  });

  describe("DELETE /api/bookmark", () => {
    it("should return unauthorized if no user session", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce(null);

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(unauthorizedResponse).toHaveBeenCalled();
      expect(response).toEqual({ status: 401, message: "Unauthorized" });
    });

    it("should remove a bookmark and return success response", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(removeBookmark).toHaveBeenCalledWith("user123", "123");
      expect(successResponse).toHaveBeenCalledWith(
        "Article bookmark removed",
        200,
      );
      expect(response).toEqual({
        status: 200,
        message: "Article bookmark removed",
      });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (removeBookmark as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to remove bookmark"),
      );

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ articleId: "123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to remove bookmark"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to remove bookmark",
      });
    });
  });
});
