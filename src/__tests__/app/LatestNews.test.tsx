import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import LatestNewsClient from "@/app/latest/LatestNewsClient";
import { LatestNewsResponse } from "@/sanity/queries/latest-news";
import { usePagination } from "@/utils/usePagination";

jest.mock("@/utils/usePagination", () => ({
  usePagination: jest.fn(),
}));

const mockLatestNews: LatestNewsResponse = {
  articlesCount: 12,
  articles: Array.from({ length: 10 }).map((_, index) => ({
    updated_at: "2025-03-25T12:00:00Z",
    title: `Article ${index + 1}`,
    slug: `article-${index + 1}`,
    description: "This is a test description",
    category: { id: "1", title: "Tech", slug: "tech" },
    media: {
      url: "https://via.placeholder.com/150",
      type: "image",
      created_at: "2025-03-25",
    },
    authors: [{ _id: "1", name: "<PERSON>", slug: "john-doe" }],
  })),
};

describe("LatestNewsClient Component", () => {
  it("renders the Latest News title", () => {
    (usePagination as jest.Mock).mockReturnValue({
      items: mockLatestNews.articles,
      isLoading: false,
      hasMore: true,
      loadMore: jest.fn(),
    });

    render(<LatestNewsClient latestNews={mockLatestNews} />);

    expect(screen.getByText("Latest News")).toBeInTheDocument();
  });

  it("renders all articles correctly", () => {
    (usePagination as jest.Mock).mockReturnValue({
      items: mockLatestNews.articles,
      isLoading: false,
      hasMore: true,
      loadMore: jest.fn(),
    });

    render(<LatestNewsClient latestNews={mockLatestNews} />);
    expect(screen.getAllByText(/Article \d+/).length).toBe(10);
  });

  it("does not show Load More button when all articles are loaded", () => {
    (usePagination as jest.Mock).mockReturnValue({
      items: mockLatestNews.articles,
      isLoading: false,
      hasMore: false,
      loadMore: jest.fn(),
    });

    render(<LatestNewsClient latestNews={mockLatestNews} />);
    expect(screen.queryByTestId("load-more-button")).not.toBeInTheDocument();
  });
});
