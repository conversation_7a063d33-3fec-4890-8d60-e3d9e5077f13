import "@testing-library/jest-dom";
import React from "react";

process.env.SANITY_STUDIO_SANITY_PROJECT_ID = "project-id";
process.env.SANITY_STUDIO_SANITY_DATASET = "development";
process.env.SANITY_STUDIO_SANITY_WORKSPACE = "cna";
process.env.SITEMAP_ARTICLES_FIRST_YEAR = "2024";

interface MockSwiperProps {
  children?: React.ReactNode;
  className?: string;
  [key: string]: any;
}

jest.mock("swiper/react", () => ({
  __esModule: true,
  Swiper: function MockSwiper({ children, ...props }: MockSwiperProps) {
    return React.createElement(
      "div",
      {
        className: "swiper-container",
        ...props,
      },
      children,
    );
  },
  SwiperSlide: function MockSwiperSlide({
    children,
    ...props
  }: MockSwiperProps) {
    return React.createElement(
      "div",
      {
        className: "swiper-slide",
        ...props,
      },
      children,
    );
  },
}));

jest.mock("swiper/modules", () => ({
  __esModule: true,
  Navigation: jest.fn(),
}));

jest.mock("swiper/css", () => ({}));
jest.mock("swiper/css/navigation", () => ({}));
jest.mock("swiper/css/pagination", () => ({}));
