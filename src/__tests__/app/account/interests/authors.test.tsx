import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import Authors from "@/app/account/interests/authors";
import { newsData, authorsData } from "@/types/mocks/account";

jest.mock("@/components/ui/Button", () => {
  return function MockButton({
    children,
    onClick,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
  }) {
    return (
      <button onClick={onClick} data-testid="mock-button">
        {children}
      </button>
    );
  };
});

jest.mock("@/components/ui/Pagination", () => {
  return function MockPagination({
    onPageChange,
  }: {
    onPageChange: (page: number) => void;
  }) {
    return (
      <div data-testid="mock-pagination">
        <button onClick={() => onPageChange(2)}>Next Page</button>
      </div>
    );
  };
});

jest.mock("@/components/Account/Interests/newsCard", () => {
  return function MockNewsCard({ item }: { item: { title: string } }) {
    return <div data-testid="mock-news-card">{item.title}</div>;
  };
});

beforeEach(() => {
  global.fetch = jest.fn((url) => {
    if (typeof url === "string") {
      if (url.includes("/api/authors/followers")) {
        return Promise.resolve({
          json: () => Promise.resolve({ userFollows: authorsData }),
        });
      }

      if (url.includes("/api/articles/by-authors")) {
        return Promise.resolve({
          json: () => Promise.resolve({ items: newsData }),
        });
      }
    }

    return Promise.reject("not found");
  }) as jest.Mock;
});

afterEach(() => {
  jest.resetAllMocks();
});

describe("Authors Component", () => {
  it("renders the component with initial state", async () => {
    render(<Authors />);

    expect(await screen.findByText("Authors you follow")).toBeInTheDocument();
    expect(
      await screen.findByText("Latest updates from your favorite authors"),
    ).toBeInTheDocument();
  });

  it("loads initial authors on mount", async () => {
    render(<Authors />);

    for (const author of authorsData) {
      expect(await screen.findByText(author.name)).toBeInTheDocument();
      expect(screen.getByText(author.jobTitle)).toBeInTheDocument();
    }
  });

  it("handles author removal", async () => {
    render(<Authors />);
    const authorToRemove = authorsData[0];

    await screen.findByText(authorToRemove.name);

    const followingButtons = screen.getAllByText("Following");
    fireEvent.click(followingButtons[0]);

    await waitFor(() =>
      expect(screen.queryByText(authorToRemove.name)).not.toBeInTheDocument(),
    );
  });

  it("filters news based on selected authors", async () => {
    render(<Authors />);

    for (const news of newsData.slice(0, 5)) {
      expect(await screen.findByText(news.title)).toBeInTheDocument();
    }

    const removedAuthorId = authorsData[0].id;
    const newsFromRemovedAuthor = newsData.find(
      (n) => n.author.id === removedAuthorId,
    );

    fireEvent.click(screen.getAllByText("Following")[0]);

    if (newsFromRemovedAuthor) {
      await waitFor(() =>
        expect(
          screen.queryByText(newsFromRemovedAuthor.title),
        ).not.toBeInTheDocument(),
      );
    }
  });

  it("shows empty state when no authors are selected", async () => {
    (fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes("/api/authors/followers")) {
        return Promise.resolve({
          json: () => Promise.resolve({ userFollows: [] }),
        });
      }
      if (url.includes("/api/articles/by-authors")) {
        return Promise.resolve({
          json: () => Promise.resolve({ items: [] }),
        });
      }
      return Promise.reject("not found");
    });

    render(<Authors />);

    expect(
      await screen.findByText(
        "We suggest selecting at least one author to personalize your experience",
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByAltText(
        "We suggest selecting at least one topic to personalize your experience",
      ),
    ).toBeInTheDocument();
  });

  it("handles pagination correctly", async () => {
    render(<Authors />);
    await screen.findByText(newsData[0].title);

    const nextPageButton = screen.getByText("Next Page");
    fireEvent.click(nextPageButton);

    const secondPageNews = newsData.slice(5, 10);
    for (const news of secondPageNews) {
      expect(await screen.findByText(news.title)).toBeInTheDocument();
    }
  });

  it("displays correct number of news per page", async () => {
    render(<Authors />);
    const newsCards = await screen.findAllByTestId("mock-news-card");
    expect(newsCards.length).toBeLessThanOrEqual(5);
  });

  it("preserves author list when changing pages", async () => {
    render(<Authors />);
    const initialAuthors = authorsData.map((author) => author.name);
    await screen.findByText(initialAuthors[0]);

    fireEvent.click(screen.getByText("Next Page"));

    for (const authorName of initialAuthors) {
      expect(await screen.findByText(authorName)).toBeInTheDocument();
    }
  });
});
