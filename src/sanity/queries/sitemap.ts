import { workspace } from "@/sanity/env";

export const articlesQuery = `
  *[
    _type in ["dailyStoryArticle", "liveBlogArticle"] && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    publishedDate match ($year + "-*")
  ] {
    publishedDate,
    "updatedAt": _updatedAt,
    "slug": slug.current,
    "category": category-> {
      "slug": slug.current,
    },
    "subcategory": subcategory-> {
      "slug": slug.current,
    },
  }
    
`;
export const categoriesQuery = `
  *[
    _type == "category" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**"))
  ] {
    "slug": slug.current,
    "subcategories": subcategories[]->{
      "id": _id,
      "slug": slug.current,
    },
  }
`;

export const authorsQuery = `
  *[
    _type == "author" && 
    agency == '${workspace}' && 
    !(_id in path("drafts.**"))
  ] {
    "slug": slug.current,
  }
`;

export const tagsQuery = `
  *[
    _type == "tag" && 
    agency == '${workspace}' && 
    !(_id in path("drafts.**"))
  ] {
    "slug": slug.current,
  }
`;

export type Category = {
  slug: string;
  subcategories: Subcategory[];
};

export type Subcategory = {
  slug: string;
};

export type Author = {
  slug: string;
};

export type Tag = {
  slug: string;
};

export type Article = {
  publishedDate: string;
  updatedAt: string;
  slug: string;
  category: { slug: string };
  subcategory: { slug: string };
};
