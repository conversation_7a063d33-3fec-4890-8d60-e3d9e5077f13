"use client";

import Button from "@/components/ui/Button";
import { Icon } from "@/components/Icon";
import Image from "next/image";
import Link from "next/link";
import { SectionArticle } from "@/sanity/queries/HomePage/homePage";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";

export default function HighlightedNews({
  backgroundColor = "#000000",
  textColor = "#ffffff",
  section,
}: {
  backgroundColor?: string;
  textColor?: string;
  section: {
    _type: "highlightedArticleSection";
    status: boolean;
    title: string;
    description: string;
    image: {
      url: string;
      type: string;
      text: string;
    };
    article: SectionArticle;
  };
}) {
  return (
    section.status && (
      <div className="container mx-auto px-4 py-8">
        <div
          className="rounded-2xl overflow-hidden md:grid md:grid-cols-2"
          data-testid="highlighted-news"
        >
          {(section.image || section.article?.media?.length) && (
            <div className="relative aspect-square md:aspect-auto md:h-full">
              <Image
                src={
                  section.image
                    ? section.image.url
                    : getMediaImage(section.article.media[0])
                }
                alt={
                  section.image?.text
                    ? section.image.text
                    : section.article?.media?.[0]?.text ||
                      truncateDescription(
                        section.description ?? section.article?.description,
                      )
                }
                fill
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div
            style={{
              backgroundColor: backgroundColor,
              color: textColor,
            }}
            className="text-center md:text-left py-8 px-4 md:py-8 xl:py-12 xl:px-12"
          >
            <p className="text-[13px] leading-[15px] uppercase mb-6 md:mb-2 lg:mb-6">
              <Link
                className="hover-underline-animation-white"
                href={`/${section.article?.category?.slug}`}
              >
                {section.article?.category?.title}
              </Link>
            </p>

            <h2 className="text-[36px] leading-[40px] font-semibold font-titles mb-8 md:mb-8">
              <Link
                className="hover-underline-animation-white"
                href={getArticleUrl(
                  section.article?.category?.slug,
                  section.article?.slug,
                  section.article?.subcategory?.slug,
                )}
              >
                {truncateTitle(section.title ?? section.article?.title)}
              </Link>
            </h2>

            <p className="text-[14px] leading-[21px]">
              {truncateDescription(
                section.description ?? section.article?.description,
              )}
            </p>

            <div className="my-8 md:mt-4 lg:my-8">
              <Button
                variant="outlined"
                href={getArticleUrl(
                  section.article?.category?.slug,
                  section.article?.slug,
                  section.article?.subcategory?.slug,
                )}
                className="w-48 mx-auto md:mx-0 border-grey-300 hover:!border-white"
              >
                Go Deeper
              </Button>
            </div>

            {section.article?.tags?.length && (
              <Link
                href={`/tags/${section.article.tags[0].slug}`}
                className="text-[14px] leading-[20px] hover-underline-animation-white relative"
              >
                You can also explore Other Topics
                <Icon
                  icon="arrow_forward"
                  className="!text-[14px] absolute top-px ml-px"
                />
              </Link>
            )}
          </div>
        </div>
      </div>
    )
  );
}
