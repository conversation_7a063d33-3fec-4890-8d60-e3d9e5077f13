import { LinkedinShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const LinkedinButton = ({ shareUrl }: { shareUrl: string }) => {
  return (
    <LinkedinShareButton url={shareUrl} title="Check this out">
      <ShareIconButton
        icon={(hover) => (
          <Icon icon="linkedin" size={24} color={hover ? "#254da0" : "black"} />
        )}
      />
    </LinkedinShareButton>
  );
};

export default LinkedinButton;
