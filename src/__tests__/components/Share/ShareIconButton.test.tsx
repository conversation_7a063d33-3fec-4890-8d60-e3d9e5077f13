import { render, fireEvent, screen } from "@testing-library/react";
import ShareIconButton from "@/components/Share/ShareIconButton";

describe("ShareIconButton Component", () => {
  it("should render the ShareIconButton", () => {
    const mockIcon = jest.fn().mockReturnValue(<div data-testid="mock-icon" />);
    render(<ShareIconButton icon={mockIcon} />);

    const button = screen.getByTestId("mock-icon");
    expect(button).toBeInTheDocument();
  });

  it("should call icon function with correct hover state", () => {
    const mockIcon = jest.fn().mockReturnValue(<div data-testid="mock-icon" />);
    render(<ShareIconButton icon={mockIcon} />);

    const button = screen.getByTestId("mock-icon");

    // Initially, hover state should be false
    expect(mockIcon).toHaveBeenCalledWith(false);

    // Simulate hover
    fireEvent.mouseEnter(button);
    expect(mockIcon).toHaveBeenCalledWith(true);

    // Simulate mouse leave
    fireEvent.mouseLeave(button);
    expect(mockIcon).toHaveBeenCalledWith(false);
  });
});
