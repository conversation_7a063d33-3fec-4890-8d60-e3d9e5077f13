import cx from "clsx";
import React, { HTMLAttributes } from "react";
import { FaWhatsapp, FaTelegram } from "react-icons/fa";
import {
  RiFacebookCircleLine,
  RiPinterestLine,
  RiMessengerLine,
} from "react-icons/ri";
import { FaXT<PERSON><PERSON>, FaLinkedinIn, FaTiktok, FaThreads } from "react-icons/fa6";
import { FiPlusCircle } from "react-icons/fi";
import { MdOutlineMailOutline } from "react-icons/md";

export const ICONS = {
  home: "home",
  menu: "list",
  search: null,
  search2: null,
  searchWhite: null,
  user: "account_circle",
  seeMore: "arrow_right_alt",
  remember: "notifications",
  alert: "notifications_active",
  donate: "volunteer_activism",
  information: "info",
  gallery: null,
  video: "play_circle",
  downloadFile: null,
  edition: null,
  newsletter: "mail",
  open: "keyboard_arrow_down",
  openNew: "open_in_new",
  collapse: "keyboard_arrow_up",
  goto: "keyboard_arrow_right",
  fullScreen: "fullscreen",
  exitFullScreen: "fullscreen_exit",
  play: null,
  error: "close",
  ok: "check",
  listen: "headphones",
  summarize: null,
  saveArticle: "bookmark_add",
  savedArticle: "bookmark",
  savedArticleFilled: null,
  glossary: "dictionary",
  comments: "chat_bubble",
  pray: null,
  updated: "acute",
  whatsapp: null,
  facebook: null,
  x: null,
  twitter: null,
  telegram: null,
  plus: null,
  pinterest: null,
  shareEmail: null,
  facebookMessenger: null,
  clipboard: null,
  email: "mail",
  copyLink: "link",
  more: "add_circle",
  like: "thumb_up",
  instagram: null,
  youtube: null,
  linkedin: null,
  ios: null,
  android: null,
  googleNews: null,
  xls: null,
  doc: null,
  pdf: null,
  share: "share",
  cart: "shopping_cart",
  edit: "edit_square",
  arrow_up: "keyboard_arrow_up",
  arrow_upward_alt: "arrow_upward_alt",
  arrow_down: "keyboard_arrow_down",
  arrow_forward: "arrow_forward",
  arrow_back: "arrow_back",
  close: "close",
  add: "add",
  language: "language",
  replay: null,
  pause: null,
  mute: null,
  unmute: null,
  videoPlay: null,
  videoFullscreen: null,
  fullscreenExit: null,
  visibility: "visibility",
  visibility_off: "visibility_off",
  google_colored: null,
  apple: null,
  facebook_colored: null,
  newWindow: null,
  expandMediaSquare: null,
  collapseMediaSquare: null,
  arrow_right: null,
  loading: null,
  latest: null,
  oldest: null,
  tiktok: null,
  threads: null,
  refresh: "refresh",
  chevron_right: "chevron_right",
  share2: null,
  clip: null,
};

export type IconName = keyof typeof ICONS;

export interface IconProps extends HTMLAttributes<HTMLButtonElement> {
  icon: IconName;
  size?: number;
  color?: string;
}

const getSvgIcon = (iconName: IconName, size?: number, color?: string) => {
  switch (iconName) {
    case "search":
      return (
        <svg
          width="22"
          height="22"
          viewBox="0 0 22 22"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 0L14.0692 1.93078L16 2.5L14.0692 3.06922L13.5 5L12.9308 3.06922L11 2.5L12.9308 1.93078L13.5 0Z"
            fill={color}
          />
          <path
            d="M18.5 2L19.2969 4.70309L22 5.5L19.2969 6.29691L18.5 9L17.7031 6.29691L15 5.5L17.7031 4.70309L18.5 2Z"
            fill={color}
          />
          <path
            d="M16.9667 20.25L11.1917 14.475C10.7333 14.8417 10.2063 15.1319 9.61042 15.3458C9.01458 15.5597 8.38056 15.6667 7.70833 15.6667C6.04306 15.6667 4.63368 15.0899 3.48021 13.9365C2.32674 12.783 1.75 11.3736 1.75 9.70833C1.75 8.04306 2.32674 6.63368 3.48021 5.48021C4.63368 4.32674 6.04306 3.75 7.70833 3.75C9.37361 3.75 10.783 4.32674 11.9365 5.48021C13.0899 6.63368 13.6667 8.04306 13.6667 9.70833C13.6667 10.3806 13.5597 11.0146 13.3458 11.6104C13.1319 12.2063 12.8417 12.7333 12.475 13.1917L18.25 18.9667L16.9667 20.25ZM7.70833 13.8333C8.85417 13.8333 9.82812 13.4323 10.6302 12.6302C11.4323 11.8281 11.8333 10.8542 11.8333 9.70833C11.8333 8.5625 11.4323 7.58854 10.6302 6.78646C9.82812 5.98438 8.85417 5.58333 7.70833 5.58333C6.5625 5.58333 5.58854 5.98438 4.78646 6.78646C3.98438 7.58854 3.58333 8.5625 3.58333 9.70833C3.58333 10.8542 3.98438 11.8281 4.78646 12.6302C5.58854 13.4323 6.5625 13.8333 7.70833 13.8333Z"
            fill={color}
          />
        </svg>
      );
    case "searchWhite":
      return (
        <svg
          width="22"
          height="22"
          viewBox="0 0 22 22"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 0L14.0692 1.93078L16 2.5L14.0692 3.06922L13.5 5L12.9308 3.06922L11 2.5L12.9308 1.93078L13.5 0Z"
            fill="white"
          />
          <path
            d="M18.5 2L19.2969 4.70309L22 5.5L19.2969 6.29691L18.5 9L17.7031 6.29691L15 5.5L17.7031 4.70309L18.5 2Z"
            fill="white"
          />
          <path
            d="M16.9667 20.25L11.1917 14.475C10.7333 14.8417 10.2063 15.1319 9.61042 15.3458C9.01458 15.5597 8.38056 15.6667 7.70833 15.6667C6.04306 15.6667 4.63368 15.0899 3.48021 13.9365C2.32674 12.783 1.75 11.3736 1.75 9.70833C1.75 8.04306 2.32674 6.63368 3.48021 5.48021C4.63368 4.32674 6.04306 3.75 7.70833 3.75C9.37361 3.75 10.783 4.32674 11.9365 5.48021C13.0899 6.63368 13.6667 8.04306 13.6667 9.70833C13.6667 10.3806 13.5597 11.0146 13.3458 11.6104C13.1319 12.2063 12.8417 12.7333 12.475 13.1917L18.25 18.9667L16.9667 20.25ZM7.70833 13.8333C8.85417 13.8333 9.82812 13.4323 10.6302 12.6302C11.4323 11.8281 11.8333 10.8542 11.8333 9.70833C11.8333 8.5625 11.4323 7.58854 10.6302 6.78646C9.82812 5.98438 8.85417 5.58333 7.70833 5.58333C6.5625 5.58333 5.58854 5.98438 4.78646 6.78646C3.98438 7.58854 3.58333 8.5625 3.58333 9.70833C3.58333 10.8542 3.98438 11.8281 4.78646 12.6302C5.58854 13.4323 6.5625 13.8333 7.70833 13.8333Z"
            fill="white"
          />
        </svg>
      );
    case "summarize":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.44076 4.36328L8.38961 7.63983L11.6081 8.6058L8.38961 9.57177L7.44076 12.8483L6.49191 9.57177L3.27344 8.6058L6.49191 7.63983L7.44076 4.36328Z"
            fill={color}
          />
          <path
            d="M14.8937 7.75695L16.2221 12.3441L20.728 13.6965L16.2221 15.0488L14.8937 19.636L13.5653 15.0488L9.05948 13.6965L13.5653 12.3441L14.8937 7.75695Z"
            fill={color}
          />
        </svg>
      );
    case "pray":
      return (
        <svg
          width="50"
          height="56"
          viewBox="0 0 50 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.9592 10.6148C24.4943 10.2951 24.2644 10.1053 23.9353 10.0394C23.6063 9.9734 23.2662 9.99055 22.9452 10.0893C22.6243 10.188 22.3324 10.3653 22.0953 10.6055C21.8583 10.8456 21.6835 11.1411 21.5863 11.4659L18.1683 22.8733L14.771 26.3098C14.4899 26.5943 14.332 26.98 14.332 27.3823C14.332 27.7845 14.4899 28.1702 14.771 28.4547L17.8356 31.5546C17.9748 31.6955 18.1402 31.8073 18.3222 31.8835C18.5042 31.9598 18.6993 31.9991 18.8963 31.9991C19.0933 31.9991 19.2884 31.9598 19.4704 31.8835C19.6524 31.8073 19.8177 31.6955 19.957 31.5546L24.4831 26.9736C24.6085 26.8463 25.1515 26.2379 25.2583 26.0943C25.5755 25.6774 25.4519 10.9654 24.9592 10.6148ZM23.9135 25.1673C24.0261 24.8906 24.0833 24.594 24.0818 24.2948V12.0652C24.0851 11.9315 24.0399 11.8011 23.9547 11.6988C23.8696 11.5964 23.7503 11.5291 23.6195 11.5095C23.4886 11.4899 23.3552 11.5194 23.2444 11.5924C23.1336 11.6655 23.053 11.7769 23.0178 11.9059L19.5492 23.4954C19.5135 23.615 19.4491 23.7238 19.3617 23.8121L18.0821 25.1074L21.1457 28.2063L23.4256 25.9068C23.635 25.6954 23.8008 25.444 23.9135 25.1673Z"
            fill={color}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M25.0408 10.6148C25.5057 10.2951 25.7356 10.1053 26.0647 10.0394C26.3937 9.9734 26.7338 9.99055 27.0548 10.0893C27.3757 10.188 27.6676 10.3653 27.9047 10.6055C28.1417 10.8456 28.3165 11.1411 28.4137 11.4659L31.8317 22.8733L35.229 26.3098C35.5101 26.5943 35.668 26.98 35.668 27.3823C35.668 27.7845 35.5101 28.1702 35.229 28.4547L32.1644 31.5546C32.0252 31.6955 31.8598 31.8073 31.6778 31.8835C31.4958 31.9598 31.3007 31.9991 31.1037 31.9991C30.9067 31.9991 30.7116 31.9598 30.5296 31.8835C30.3476 31.8073 30.1823 31.6955 30.043 31.5546L25.5169 26.9736C25.3915 26.8463 24.8485 26.2379 24.7417 26.0943C24.4245 25.6774 24.5481 10.9654 25.0408 10.6148ZM26.0865 25.1673C25.9739 24.8906 25.9167 24.594 25.9182 24.2948V12.0652C25.9149 11.9315 25.9601 11.8011 26.0453 11.6988C26.1304 11.5964 26.2497 11.5291 26.3805 11.5095C26.5114 11.4899 26.6448 11.5194 26.7556 11.5924C26.8664 11.6655 26.947 11.7769 26.9822 11.9059L30.4508 23.4954C30.4865 23.615 30.5509 23.7238 30.6383 23.8121L31.9179 25.1074L28.8543 28.2063L26.5744 25.9068C26.365 25.6954 26.1992 25.444 26.0865 25.1673Z"
            fill={color}
          />
        </svg>
      );
    case "downloadFile":
      return (
        <svg
          width="15"
          height="18"
          viewBox="0 0 15 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.33333 9.09896H10.8333L7.5 12.4323L4.16667 9.09896H6.66667V5.76562H8.33333V9.09896ZM10 2.43229H1.66667V15.7656H13.3333V5.76562H10V2.43229ZM0 1.59229C0 1.13563 0.3725 0.765625 0.8325 0.765625H10.8333L15 4.93229V16.5931C15.0008 16.7026 14.98 16.8111 14.9388 16.9125C14.8976 17.0139 14.8369 17.1062 14.7601 17.1841C14.6832 17.262 14.5918 17.324 14.491 17.3666C14.3901 17.4092 14.2819 17.4315 14.1725 17.4323H0.8275C0.608648 17.4308 0.399181 17.3432 0.244348 17.1885C0.0895149 17.0339 0.00174554 16.8245 0 16.6056V1.59229Z"
            fill={color}
          />
        </svg>
      );
    case "whatsapp":
      return <FaWhatsapp size={size || 24} color={color || "black"} />;
    case "facebook":
      return (
        <RiFacebookCircleLine size={size || 24} color={color || "black"} />
      );
    case "x":
      return <FaXTwitter size={size || 24} color={color || "black"} />;
    case "telegram":
      return <FaTelegram size={size || 24} color={color || "black"} />;
    case "tiktok":
      return <FaTiktok size={size || 24} color={color || "black"} />;
    case "plus":
      return <FiPlusCircle size={size || 24} color={color || "black"} />;
    case "threads":
      return <FaThreads size={size || 24} color={color || "black"} />;
    case "pinterest":
      return <RiPinterestLine size={size || 24} color={color || "black"} />;
    case "shareEmail":
      return (
        <MdOutlineMailOutline size={size || 24} color={color || "black"} />
      );
    case "facebookMessenger":
      return <RiMessengerLine size={size || 24} color={color || "black"} />;
    case "clipboard":
      return (
        <svg
          width={size || 24}
          height={size || 24}
          viewBox="0 0 21 10"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.5 10H5.5C4.11667 10 2.93767 9.51233 1.963 8.537C0.987667 7.56233 0.5 6.38333 0.5 5C0.5 3.61667 0.987667 2.43733 1.963 1.462C2.93767 0.487333 4.11667 0 5.5 0H9.5V2H5.5C4.66667 2 3.95833 2.29167 3.375 2.875C2.79167 3.45833 2.5 4.16667 2.5 5C2.5 5.83333 2.79167 6.54167 3.375 7.125C3.95833 7.70833 4.66667 8 5.5 8H9.5V10ZM6.5 6V4H14.5V6H6.5ZM11.5 10V8H15.5C16.3333 8 17.0417 7.70833 17.625 7.125C18.2083 6.54167 18.5 5.83333 18.5 5C18.5 4.16667 18.2083 3.45833 17.625 2.875C17.0417 2.29167 16.3333 2 15.5 2H11.5V0H15.5C16.8833 0 18.0627 0.487333 19.038 1.462C20.0127 2.43733 20.5 3.61667 20.5 5C20.5 6.38333 20.0127 7.56233 19.038 8.537C18.0627 9.51233 16.8833 10 15.5 10H11.5Z"
            fill={color}
          />
        </svg>
      );
    case "twitter":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M17.1883 3.0625L12.1923 8.7735L7.87228 3.0625H1.61328L9.09028 12.8385L2.00428 20.9375H5.03828L10.5073 14.6875L15.2873 20.9375H21.3893L13.5953 10.6335L20.2203 3.0625H17.1883ZM16.1243 19.1225L5.15528 4.7815H6.95828L17.8043 19.1215L16.1243 19.1225Z"
            fill={color}
          />
        </svg>
      );
    case "instagram":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 9C11.2044 9 10.4413 9.31607 9.87868 9.87868C9.31607 10.4413 9 11.2044 9 12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12C15 11.2044 14.6839 10.4413 14.1213 9.87868C13.5587 9.31607 12.7956 9 12 9ZM12 7C13.3261 7 14.5979 7.52678 15.5355 8.46447C16.4732 9.40215 17 10.6739 17 12C17 13.3261 16.4732 14.5979 15.5355 15.5355C14.5979 16.4732 13.3261 17 12 17C10.6739 17 9.40215 16.4732 8.46447 15.5355C7.52678 14.5979 7 13.3261 7 12C7 10.6739 7.52678 9.40215 8.46447 8.46447C9.40215 7.52678 10.6739 7 12 7ZM18.5 6.75C18.5 7.08152 18.3683 7.39946 18.1339 7.63388C17.8995 7.8683 17.5815 8 17.25 8C16.9185 8 16.6005 7.8683 16.3661 7.63388C16.1317 7.39946 16 7.08152 16 6.75C16 6.41848 16.1317 6.10054 16.3661 5.86612C16.6005 5.6317 16.9185 5.5 17.25 5.5C17.5815 5.5 17.8995 5.6317 18.1339 5.86612C18.3683 6.10054 18.5 6.41848 18.5 6.75ZM12 4C9.526 4 9.122 4.007 7.971 4.058C7.187 4.095 6.661 4.2 6.173 4.39C5.76462 4.53994 5.39541 4.78026 5.093 5.093C4.78001 5.3954 4.53935 5.76458 4.389 6.173C4.199 6.663 4.094 7.188 4.058 7.971C4.006 9.075 4 9.461 4 12C4 14.474 4.007 14.878 4.058 16.029C4.095 16.812 4.2 17.339 4.389 17.826C4.559 18.261 4.759 18.574 5.091 18.906C5.428 19.242 5.741 19.443 6.171 19.609C6.665 19.8 7.191 19.906 7.971 19.942C9.075 19.994 9.461 20 12 20C14.474 20 14.878 19.993 16.029 19.942C16.811 19.905 17.338 19.8 17.826 19.611C18.2338 19.4603 18.6027 19.2205 18.906 18.909C19.243 18.572 19.444 18.259 19.61 17.829C19.8 17.336 19.906 16.809 19.942 16.029C19.994 14.925 20 14.539 20 12C20 9.526 19.993 9.122 19.942 7.971C19.905 7.189 19.8 6.661 19.61 6.173C19.4593 5.765 19.2191 5.39596 18.907 5.093C18.6047 4.77985 18.2355 4.53917 17.827 4.389C17.337 4.199 16.811 4.094 16.029 4.058C14.925 4.006 14.539 4 12 4ZM12 2C14.717 2 15.056 2.01 16.122 2.06C17.187 2.11 17.912 2.277 18.55 2.525C19.21 2.779 19.766 3.123 20.322 3.678C20.8305 4.1779 21.224 4.78259 21.475 5.45C21.722 6.087 21.89 6.813 21.94 7.878C21.987 8.944 22 9.283 22 12C22 14.717 21.99 15.056 21.94 16.122C21.89 17.187 21.722 17.912 21.475 18.55C21.2247 19.2178 20.8311 19.8226 20.322 20.322C19.822 20.8303 19.2173 21.2238 18.55 21.475C17.913 21.722 17.187 21.89 16.122 21.94C15.056 21.987 14.717 22 12 22C9.283 22 8.944 21.99 7.878 21.94C6.813 21.89 6.088 21.722 5.45 21.475C4.78233 21.2245 4.17753 20.8309 3.678 20.322C3.16941 19.8222 2.77593 19.2175 2.525 18.55C2.277 17.913 2.11 17.187 2.06 16.122C2.013 15.056 2 14.717 2 12C2 9.283 2.01 8.944 2.06 7.878C2.11 6.812 2.277 6.088 2.525 5.45C2.77524 4.78218 3.1688 4.17732 3.678 3.678C4.17767 3.16923 4.78243 2.77573 5.45 2.525C6.088 2.277 6.812 2.11 7.878 2.06C8.944 2.013 9.283 2 12 2Z"
            fill={color}
          />
        </svg>
      );
    case "youtube":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.606 6.995C19.53 6.697 19.314 6.472 19.067 6.403C18.63 6.28 16.5 6 12 6C7.5 6 5.372 6.28 4.931 6.403C4.687 6.471 4.471 6.696 4.394 6.995C4.285 7.419 4 9.196 4 12C4 14.804 4.285 16.58 4.394 17.006C4.47 17.303 4.686 17.528 4.932 17.596C5.372 17.72 7.5 18 12 18C16.5 18 18.629 17.72 19.069 17.597C19.313 17.529 19.529 17.304 19.606 17.005C19.715 16.581 20 14.8 20 12C20 9.2 19.715 7.42 19.606 6.995ZM21.543 6.498C22 8.28 22 12 22 12C22 12 22 15.72 21.543 17.502C21.289 18.487 20.546 19.262 19.605 19.524C17.896 20 12 20 12 20C12 20 6.107 20 4.395 19.524C3.45 19.258 2.708 18.484 2.457 17.502C2 15.72 2 12 2 12C2 12 2 8.28 2.457 6.498C2.711 5.513 3.454 4.738 4.395 4.476C6.107 4 12 4 12 4C12 4 17.896 4 19.605 4.476C20.55 4.742 21.292 5.516 21.543 6.498ZM10 15.5V8.5L16 12L10 15.5Z"
            fill={color}
          />
        </svg>
      );
    case "ios":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M17.0511 18.6433C16.0711 19.5933 15.0011 19.4433 13.9711 18.9933C12.8811 18.5333 11.8811 18.5133 10.7311 18.9933C9.29114 19.6133 8.53114 19.4333 7.67114 18.6433C2.79114 13.6133 3.51114 5.95328 9.05114 5.67328C10.4011 5.74328 11.3411 6.41328 12.1311 6.47328C13.3111 6.23328 14.4411 5.54328 15.7011 5.63328C17.2111 5.75328 18.3511 6.35328 19.1011 7.43328C15.9811 9.30328 16.7211 13.4133 19.5811 14.5633C19.0111 16.0633 18.2711 17.5533 17.0411 18.6533L17.0511 18.6433ZM12.0311 5.61328C11.8811 3.38328 13.6911 1.54328 15.7711 1.36328C16.0611 3.94328 13.4311 5.86328 12.0311 5.61328Z"
            fill={color}
          />
        </svg>
      );
    case "android":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_1533_19625)">
            <path
              d="M14.9751 1.3828L15.9351 -0.349202C15.9474 -0.371396 15.9552 -0.39579 15.958 -0.420993C15.9609 -0.446196 15.9588 -0.471713 15.9517 -0.496089C15.9447 -0.520464 15.933 -0.54322 15.9172 -0.563057C15.9014 -0.582894 15.8818 -0.599424 15.8596 -0.611702C15.8374 -0.623981 15.813 -0.631768 15.7878 -0.634619C15.7626 -0.63747 15.7371 -0.635329 15.7127 -0.628318C15.6884 -0.621307 15.6656 -0.609564 15.6458 -0.593758C15.6259 -0.577953 15.6094 -0.558396 15.5971 -0.536202L14.6271 1.2138C13.7991 0.850747 12.9048 0.663305 12.0006 0.663305C11.0965 0.663305 10.2022 0.850747 9.37412 1.2138L8.40413 -0.536202C8.37933 -0.581289 8.33763 -0.614678 8.28822 -0.629025C8.2388 -0.643371 8.18571 -0.6375 8.14062 -0.612702C8.09554 -0.587905 8.06215 -0.546212 8.0478 -0.496796C8.03346 -0.44738 8.03933 -0.394289 8.06413 -0.349202L9.02413 1.3828C8.11071 1.83329 7.33899 2.5266 6.79357 3.3867C6.24816 4.24681 5.95011 5.2405 5.93213 6.2588H18.0691C18.051 5.24029 17.7526 4.24646 17.2068 3.38633C16.661 2.5262 15.8889 1.83301 14.9751 1.3828ZM9.20012 4.0378C9.09981 4.0378 9.00174 4.00804 8.91835 3.95228C8.83495 3.89653 8.76997 3.81729 8.73162 3.72459C8.69328 3.63189 8.6833 3.52989 8.70294 3.43152C8.72258 3.33314 8.77097 3.24281 8.84198 3.17194C8.91298 3.10108 9.00341 3.05287 9.10183 3.03342C9.20024 3.01397 9.30221 3.02415 9.39484 3.06268C9.48746 3.10121 9.56658 3.16634 9.62217 3.24985C9.67776 3.33336 9.70732 3.43148 9.70712 3.5318C9.70686 3.66609 9.65333 3.79479 9.55828 3.88965C9.46322 3.98452 9.33442 4.0378 9.20012 4.0378ZM14.8021 4.0378C14.7018 4.0378 14.6037 4.00804 14.5203 3.95228C14.437 3.89653 14.372 3.81729 14.3336 3.72459C14.2953 3.63189 14.2853 3.52989 14.3049 3.43152C14.3246 3.33314 14.373 3.24281 14.444 3.17194C14.515 3.10108 14.6054 3.05287 14.7038 3.03342C14.8022 3.01397 14.9042 3.02415 14.9968 3.06268C15.0895 3.10121 15.1686 3.16634 15.2242 3.24985C15.2798 3.33336 15.3093 3.43148 15.3091 3.5318C15.3089 3.66609 15.2553 3.79479 15.1603 3.88965C15.0652 3.98452 14.9364 4.0378 14.8021 4.0378ZM5.93012 15.5348C5.92986 15.7279 5.96771 15.9191 6.0415 16.0975C6.1153 16.2759 6.22358 16.438 6.36015 16.5745C6.49672 16.711 6.65888 16.8191 6.83734 16.8928C7.01581 16.9665 7.20706 17.0042 7.40013 17.0038H8.37313V20.0038C8.37313 20.3646 8.51646 20.7107 8.77161 20.9658C9.02675 21.221 9.3728 21.3643 9.73362 21.3643C10.0945 21.3643 10.4405 21.221 10.6956 20.9658C10.9508 20.7107 11.0941 20.3646 11.0941 20.0038V17.0038H12.9081V20.0038C12.9081 20.3645 13.0514 20.7104 13.3065 20.9655C13.5615 21.2205 13.9074 21.3638 14.2681 21.3638C14.6288 21.3638 14.9747 21.2205 15.2298 20.9655C15.4848 20.7104 15.6281 20.3645 15.6281 20.0038V17.0038H16.6021C16.7949 17.0039 16.9859 16.966 17.1641 16.8923C17.3422 16.8186 17.5041 16.7105 17.6405 16.5741C17.7768 16.4378 17.8849 16.2759 17.9586 16.0977C18.0324 15.9196 18.0703 15.7286 18.0701 15.5358V6.7388H5.93012V15.5348ZM4.06313 6.5048C3.70242 6.50533 3.35665 6.64892 3.10169 6.90407C2.84673 7.15922 2.70339 7.50509 2.70312 7.8658V13.5348C2.70312 13.7134 2.7383 13.8902 2.80665 14.0552C2.875 14.2203 2.97517 14.3702 3.10146 14.4965C3.22775 14.6228 3.37767 14.7229 3.54268 14.7913C3.70768 14.8596 3.88453 14.8948 4.06313 14.8948C4.24172 14.8948 4.41857 14.8596 4.58357 14.7913C4.74858 14.7229 4.8985 14.6228 5.02479 14.4965C5.15108 14.3702 5.25125 14.2203 5.3196 14.0552C5.38795 13.8902 5.42312 13.7134 5.42312 13.5348V7.8658C5.4226 7.50527 5.27914 7.15965 5.02421 6.90472C4.76927 6.64978 4.42366 6.50633 4.06313 6.5058M19.9351 6.5058C19.5744 6.50633 19.2287 6.64992 18.9737 6.90507C18.7187 7.16022 18.5754 7.50609 18.5751 7.8668V13.5358C18.5751 13.7144 18.6103 13.8912 18.6786 14.0562C18.747 14.2213 18.8472 14.3712 18.9735 14.4975C19.0997 14.6238 19.2497 14.7239 19.4147 14.7923C19.5797 14.8606 19.7565 14.8958 19.9351 14.8958C20.1137 14.8958 20.2906 14.8606 20.4556 14.7923C20.6206 14.7239 20.7705 14.6238 20.8968 14.4975C21.0231 14.3712 21.1233 14.2213 21.1916 14.0562C21.2599 13.8912 21.2951 13.7144 21.2951 13.5358V7.8658C21.2946 7.50527 21.1511 7.15965 20.8962 6.90472C20.6413 6.64978 20.2957 6.50633 19.9351 6.5058Z"
              fill={color}
            />
          </g>
          <defs>
            <clipPath id="clip0_1533_19625">
              <rect width="24" height="24" fill="white" />
            </clipPath>
          </defs>
        </svg>
      );
    case "googleNews":
      return (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.0895 3.42222C12.9784 3.42222 12.8895 3.42222 12.7784 3.46667L5.51172 6.11111V3.55556C5.51172 3.24444 5.75616 3 6.06727 3H17.9562C18.2673 3 18.5117 3.24444 18.5117 3.55556V6.17778L14.4006 5.06667L14.0451 4.08889C13.8895 3.68889 13.5117 3.42222 13.0895 3.42222Z"
            fill={color}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M20.4888 8.44423V20.4442H20.511V20.5553C20.4665 20.822 20.2443 20.9998 19.9776 20.9998H4.08876C3.77765 20.9998 3.5332 20.7553 3.5332 20.4442V8.33312C3.57765 8.06645 3.79987 7.88867 4.06654 7.88867H19.9332C20.2443 7.88867 20.4888 8.13312 20.4888 8.44423ZM17.4665 11.3776H12.711V12.622H17.4665C17.5999 12.622 17.711 12.5109 17.711 12.3776V11.622C17.711 11.4887 17.5999 11.3776 17.4665 11.3776ZM8.48876 17.3553C9.55543 17.3776 10.3999 16.9109 10.8888 16.1331C11.1776 15.6664 11.3332 15.0887 11.3332 14.4664C11.3332 14.2848 11.3149 14.1215 11.2949 13.9432L11.2888 13.8887H8.46654V15.022H10.0888C10.0443 15.3331 9.86654 15.5998 9.64431 15.7998C9.35542 16.0442 8.95543 16.1998 8.46654 16.1998C7.48876 16.1998 6.68876 15.3776 6.68876 14.3776C6.68876 13.3998 7.48876 12.5553 8.46654 12.5553C8.64431 12.5553 8.82209 12.5776 8.97765 12.622C9.22209 12.7109 9.44431 12.822 9.62209 12.9998L10.4888 12.1331C10.1554 11.822 9.75542 11.5998 9.28876 11.4664C9.04431 11.3998 8.77765 11.3553 8.48876 11.3553C6.82209 11.3553 5.48876 12.6887 5.48876 14.3553C5.48876 16.022 6.82209 17.3553 8.48876 17.3553ZM17.4665 17.3776C17.5999 17.3776 17.711 17.2664 17.711 17.1331V16.3776C17.711 16.2442 17.5999 16.1331 17.4665 16.1331H12.711V17.3776H17.4665ZM18.2221 14.9998C18.3554 14.9998 18.4665 14.8887 18.4665 14.7553V13.9998C18.4665 13.8664 18.3554 13.7553 18.2221 13.7553H12.711V14.9998H18.2221Z"
            fill={color}
          />
          <path
            d="M14.778 7.44379L13.6224 4.24379C13.5113 3.9549 13.2002 3.79935 12.9335 3.88824L1.33352 8.11046C1.17796 8.17713 1.04463 8.33268 1.02241 8.48824C1.00019 8.59935 1.00019 8.71046 1.04463 8.79935L3.06685 14.3771V8.44379C3.06685 7.88824 3.5113 7.44379 4.06685 7.44379H14.778Z"
            fill={color}
          />
          <path
            d="M14.5781 5.57812L22.6226 7.73368C22.8892 7.82257 23.067 8.13368 22.9781 8.42257L20.9337 16.0226V8.44479C20.9337 7.88924 20.4892 7.44479 19.9337 7.44479H15.267L14.5781 5.57812Z"
            fill={color}
          />
        </svg>
      );
    case "xls":
      return (
        <svg
          width="25"
          height="25"
          viewBox="0 0 25 25"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.39993 25C7.8943 25 7.46426 24.8129 7.10979 24.4388C6.75535 24.0646 6.57812 23.6107 6.57812 23.0769V1.92305C6.57812 1.38933 6.75535 0.935383 7.10979 0.561216C7.46426 0.187073 7.8943 0 8.39993 0H19.3891L24.9988 5.92149V23.0769C24.9988 23.6107 24.8216 24.0646 24.4671 24.4388C24.1127 24.8129 23.6826 25 23.177 25H8.39993ZM18.883 6.40493V1.06837H8.39993C8.19752 1.06837 8.01197 1.1574 7.84326 1.33545C7.67458 1.51353 7.59024 1.7094 7.59024 1.92305V23.0769C7.59024 23.2906 7.67458 23.4865 7.84326 23.6645C8.01197 23.8426 8.19752 23.9316 8.39993 23.9316H23.177C23.3794 23.9316 23.5649 23.8426 23.7337 23.6645C23.9023 23.4865 23.9867 23.2906 23.9867 23.0769V6.40493H18.883Z"
            fill={color}
          />
          <rect
            y="6.25"
            width="19.7364"
            height="13.8889"
            rx="6.94444"
            fill={color}
          />
          <path
            d="M6.04297 11.9971L6.65479 13.1216L7.28369 11.9971H8.33301L7.28711 13.8052L8.38086 15.6953H7.32812L6.66162 14.5059L5.99512 15.6953H4.94238L6.03613 13.8052L4.99023 11.9971H6.04297ZM9.85059 10.4453V15.6953H8.86279V10.4453H9.85059ZM12.6636 14.6733C12.6636 14.6027 12.6431 14.5389 12.6021 14.4819C12.561 14.425 12.4847 14.3726 12.373 14.3247C12.2637 14.2746 12.1053 14.229 11.8979 14.188C11.7111 14.147 11.5368 14.0957 11.375 14.0342C11.2155 13.9704 11.0765 13.894 10.958 13.8052C10.8418 13.7163 10.7507 13.6115 10.6846 13.4907C10.6185 13.3677 10.5854 13.2275 10.5854 13.0703C10.5854 12.9154 10.6185 12.7695 10.6846 12.6328C10.7529 12.4961 10.8498 12.3753 10.9751 12.2705C11.1027 12.1634 11.2576 12.0802 11.4399 12.021C11.6245 11.9595 11.8319 11.9287 12.062 11.9287C12.3833 11.9287 12.659 11.98 12.8892 12.0825C13.1216 12.1851 13.2993 12.3263 13.4224 12.5063C13.5477 12.6841 13.6104 12.8869 13.6104 13.1147H12.626C12.626 13.019 12.6055 12.9336 12.5645 12.8584C12.5257 12.7809 12.4642 12.7205 12.3799 12.6772C12.2979 12.6317 12.1908 12.6089 12.0586 12.6089C11.9492 12.6089 11.8547 12.6283 11.7749 12.667C11.6951 12.7035 11.6336 12.7536 11.5903 12.8174C11.5493 12.8789 11.5288 12.9473 11.5288 13.0225C11.5288 13.0794 11.5402 13.1307 11.563 13.1763C11.5881 13.2196 11.6279 13.2594 11.6826 13.2959C11.7373 13.3324 11.8079 13.3665 11.8945 13.3984C11.9834 13.4281 12.0928 13.4554 12.2227 13.4805C12.4893 13.5352 12.7274 13.6069 12.937 13.6958C13.1466 13.7824 13.313 13.9009 13.436 14.0513C13.5591 14.1994 13.6206 14.3942 13.6206 14.6357C13.6206 14.7998 13.5841 14.9502 13.5112 15.0869C13.4383 15.2236 13.3335 15.3433 13.1968 15.4458C13.0601 15.5461 12.896 15.6247 12.7046 15.6816C12.5155 15.7363 12.3024 15.7637 12.0654 15.7637C11.7214 15.7637 11.4297 15.7021 11.1904 15.5791C10.9535 15.4561 10.7734 15.3 10.6504 15.1108C10.5296 14.9194 10.4692 14.7235 10.4692 14.5229H11.4023C11.4069 14.6574 11.4411 14.7656 11.5049 14.8477C11.571 14.9297 11.6541 14.9889 11.7544 15.0254C11.8569 15.0618 11.9674 15.0801 12.0859 15.0801C12.2135 15.0801 12.3195 15.063 12.4038 15.0288C12.4881 14.9924 12.5519 14.9445 12.5952 14.8853C12.6408 14.8237 12.6636 14.7531 12.6636 14.6733Z"
            fill="white"
          />
        </svg>
      );
    case "doc":
      return (
        <svg
          width="25"
          height="25"
          viewBox="0 0 25 25"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.39996 25C7.89433 25 7.46427 24.8129 7.1098 24.4388C6.75535 24.0646 6.57812 23.6107 6.57812 23.0769V1.92305C6.57812 1.38933 6.75535 0.935383 7.1098 0.561216C7.46427 0.187073 7.89433 0 8.39996 0H19.3893L24.9991 5.92149V23.0769C24.9991 23.6107 24.8219 24.0646 24.4674 24.4388C24.113 24.8129 23.6829 25 23.1773 25H8.39996ZM18.8832 6.40493V1.06837H8.39996C8.19755 1.06837 8.01199 1.1574 7.84328 1.33545C7.6746 1.51353 7.59026 1.7094 7.59026 1.92305V23.0769C7.59026 23.2906 7.6746 23.4865 7.84328 23.6645C8.01199 23.8426 8.19755 23.9316 8.39996 23.9316H23.1773C23.3797 23.9316 23.5652 23.8426 23.7339 23.6645C23.9026 23.4865 23.987 23.2906 23.987 23.0769V6.40493H18.8832Z"
            fill={color}
          />
          <rect
            y="6.25"
            width="19.7368"
            height="13.8889"
            rx="6.94444"
            fill={color}
          />
          <path
            d="M6.43408 14.8887V10.4453H7.42529V15.6953H6.5332L6.43408 14.8887ZM4.09619 13.8906V13.8188C4.09619 13.5363 4.12809 13.2799 4.19189 13.0498C4.2557 12.8174 4.34912 12.618 4.47217 12.4517C4.59521 12.2853 4.74674 12.1566 4.92676 12.0654C5.10677 11.9743 5.31185 11.9287 5.54199 11.9287C5.75846 11.9287 5.94759 11.9743 6.10938 12.0654C6.27344 12.1566 6.41243 12.2865 6.52637 12.4551C6.64258 12.6214 6.736 12.8185 6.80664 13.0464C6.87728 13.272 6.92855 13.5192 6.96045 13.7881V13.9453C6.92855 14.2028 6.87728 14.4421 6.80664 14.6631C6.736 14.8841 6.64258 15.0778 6.52637 15.2441C6.41243 15.4082 6.27344 15.5358 6.10938 15.627C5.94531 15.7181 5.75391 15.7637 5.53516 15.7637C5.30501 15.7637 5.09993 15.717 4.91992 15.6235C4.74219 15.5301 4.5918 15.3991 4.46875 15.2305C4.34798 15.0618 4.2557 14.8636 4.19189 14.6357C4.12809 14.4079 4.09619 14.1595 4.09619 13.8906ZM5.08057 13.8188V13.8906C5.08057 14.0433 5.09196 14.1857 5.11475 14.3179C5.13981 14.45 5.17969 14.5674 5.23438 14.6699C5.29134 14.7702 5.36426 14.8488 5.45312 14.9058C5.54427 14.9604 5.65479 14.9878 5.78467 14.9878C5.95329 14.9878 6.09229 14.9502 6.20166 14.875C6.31104 14.7975 6.39421 14.6916 6.45117 14.5571C6.51042 14.4227 6.5446 14.2677 6.55371 14.0923V13.6445C6.54688 13.501 6.52637 13.3722 6.49219 13.2583C6.46029 13.1421 6.41243 13.043 6.34863 12.9609C6.28711 12.8789 6.20964 12.8151 6.11621 12.7695C6.02507 12.724 5.91683 12.7012 5.7915 12.7012C5.6639 12.7012 5.55452 12.7308 5.46338 12.79C5.37223 12.847 5.29818 12.9256 5.24121 13.0259C5.18652 13.1261 5.14551 13.2446 5.11816 13.3813C5.0931 13.5158 5.08057 13.6616 5.08057 13.8188ZM8.03711 13.8838V13.812C8.03711 13.5409 8.07585 13.2913 8.15332 13.0635C8.23079 12.8333 8.34359 12.634 8.4917 12.4653C8.63981 12.2967 8.8221 12.1657 9.03857 12.0723C9.25505 11.9766 9.50342 11.9287 9.78369 11.9287C10.064 11.9287 10.3135 11.9766 10.5322 12.0723C10.751 12.1657 10.9344 12.2967 11.0825 12.4653C11.2329 12.634 11.3468 12.8333 11.4243 13.0635C11.5018 13.2913 11.5405 13.5409 11.5405 13.812V13.8838C11.5405 14.1527 11.5018 14.4022 11.4243 14.6323C11.3468 14.8602 11.2329 15.0596 11.0825 15.2305C10.9344 15.3991 10.7521 15.5301 10.5356 15.6235C10.3192 15.717 10.0708 15.7637 9.79053 15.7637C9.51025 15.7637 9.26074 15.717 9.04199 15.6235C8.82552 15.5301 8.64209 15.3991 8.4917 15.2305C8.34359 15.0596 8.23079 14.8602 8.15332 14.6323C8.07585 14.4022 8.03711 14.1527 8.03711 13.8838ZM9.02148 13.812V13.8838C9.02148 14.0387 9.03516 14.1834 9.0625 14.3179C9.08984 14.4523 9.13314 14.5708 9.19238 14.6733C9.25391 14.7736 9.33366 14.8522 9.43164 14.9092C9.52962 14.9661 9.64925 14.9946 9.79053 14.9946C9.92725 14.9946 10.0446 14.9661 10.1426 14.9092C10.2406 14.8522 10.3192 14.7736 10.3784 14.6733C10.4377 14.5708 10.481 14.4523 10.5083 14.3179C10.5379 14.1834 10.5527 14.0387 10.5527 13.8838V13.812C10.5527 13.6616 10.5379 13.5203 10.5083 13.3882C10.481 13.2537 10.4365 13.1353 10.375 13.0327C10.3158 12.9279 10.2371 12.8459 10.1392 12.7866C10.0412 12.7274 9.92269 12.6978 9.78369 12.6978C9.64469 12.6978 9.5262 12.7274 9.42822 12.7866C9.33252 12.8459 9.25391 12.9279 9.19238 13.0327C9.13314 13.1353 9.08984 13.2537 9.0625 13.3882C9.03516 13.5203 9.02148 13.6616 9.02148 13.812ZM13.6733 14.9946C13.7941 14.9946 13.9012 14.9718 13.9946 14.9263C14.0881 14.8784 14.161 14.8123 14.2134 14.728C14.2681 14.6414 14.2965 14.54 14.2988 14.4238H15.2251C15.2228 14.6836 15.1533 14.9149 15.0166 15.1177C14.8799 15.3182 14.6965 15.4766 14.4663 15.5928C14.2362 15.7067 13.9787 15.7637 13.6938 15.7637C13.4067 15.7637 13.1561 15.7158 12.9419 15.6201C12.73 15.5244 12.5534 15.3923 12.4121 15.2236C12.2708 15.0527 12.1649 14.8545 12.0942 14.6289C12.0236 14.401 11.9883 14.1572 11.9883 13.8975V13.7983C11.9883 13.5363 12.0236 13.2925 12.0942 13.0669C12.1649 12.839 12.2708 12.6408 12.4121 12.4722C12.5534 12.3013 12.73 12.168 12.9419 12.0723C13.1538 11.9766 13.4022 11.9287 13.687 11.9287C13.9901 11.9287 14.2555 11.9868 14.4834 12.103C14.7135 12.2192 14.8936 12.3856 15.0234 12.6021C15.1556 12.8162 15.2228 13.0703 15.2251 13.3643H14.2988C14.2965 13.2412 14.2703 13.1296 14.2202 13.0293C14.1724 12.929 14.1017 12.8493 14.0083 12.79C13.9172 12.7285 13.8044 12.6978 13.6699 12.6978C13.5264 12.6978 13.409 12.7285 13.3179 12.79C13.2267 12.8493 13.1561 12.9313 13.106 13.0361C13.0558 13.1387 13.0205 13.256 13 13.3882C12.9818 13.5181 12.9727 13.6548 12.9727 13.7983V13.8975C12.9727 14.041 12.9818 14.1789 13 14.311C13.0182 14.4432 13.0524 14.5605 13.1025 14.6631C13.1549 14.7656 13.2267 14.8465 13.3179 14.9058C13.409 14.965 13.5275 14.9946 13.6733 14.9946Z"
            fill="white"
          />
        </svg>
      );
    case "pdf":
      return (
        <svg
          width="25"
          height="25"
          viewBox="0 0 25 25"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.39996 25C7.89433 25 7.46427 24.8129 7.1098 24.4388C6.75535 24.0646 6.57812 23.6107 6.57812 23.0769V1.92305C6.57812 1.38933 6.75535 0.935383 7.1098 0.561216C7.46427 0.187073 7.89433 0 8.39996 0H19.3893L24.9991 5.92149V23.0769C24.9991 23.6107 24.8219 24.0646 24.4674 24.4388C24.113 24.8129 23.6829 25 23.1773 25H8.39996ZM18.8832 6.40493V1.06837H8.39996C8.19755 1.06837 8.01199 1.1574 7.84328 1.33545C7.6746 1.51353 7.59026 1.7094 7.59026 1.92305V23.0769C7.59026 23.2906 7.6746 23.4865 7.84328 23.6645C8.01199 23.8426 8.19755 23.9316 8.39996 23.9316H23.1773C23.3797 23.9316 23.5652 23.8426 23.7339 23.6645C23.9026 23.4865 23.987 23.2906 23.987 23.0769V6.40493H18.8832Z"
            fill={color}
          />
          <rect
            y="6.25"
            width="19.7368"
            height="13.8889"
            rx="6.94444"
            fill={color}
          />
          <path
            d="M5.73438 12.708V17.1172H4.75V11.9971H5.6626´L5.73438 12.708ZM8.07568 13.8052V13.877C8.07568 14.1458 8.04378 14.3953 7.97998 14.6255C7.91846 14.8556 7.82731 15.0562 7.70654 15.2271C7.58577 15.3957 7.43538 15.5278 7.25537 15.6235C7.07764 15.717 6.87256 15.7637 6.64014 15.7637C6.41455 15.7637 6.21859 15.7181 6.05225 15.627C5.8859 15.5358 5.74577 15.4082 5.63184 15.2441C5.52018 15.0778 5.43018 14.8853 5.36182 14.6665C5.29346 14.4478 5.24105 14.2131 5.20459 13.9624V13.7744C5.24105 13.5055 5.29346 13.2594 5.36182 13.0361C5.43018 12.8105 5.52018 12.6157 5.63184 12.4517C5.74577 12.2853 5.88477 12.1566 6.04883 12.0654C6.21517 11.9743 6.40999 11.9287 6.6333 11.9287C6.868 11.9287 7.07422 11.9731 7.25195 12.062C7.43197 12.1509 7.58236 12.2785 7.70312 12.4448C7.82617 12.6112 7.91846 12.8094 7.97998 13.0396C8.04378 13.2697 8.07568 13.5249 8.07568 13.8052ZM7.08789 13.877V13.8052C7.08789 13.6479 7.07422 13.5033 7.04688 13.3711C7.02181 13.2367 6.98079 13.1193 6.92383 13.019C6.86914 12.9188 6.79622 12.8413 6.70508 12.7866C6.61621 12.7297 6.50798 12.7012 6.38037 12.7012C6.24593 12.7012 6.13086 12.7228 6.03516 12.7661C5.94173 12.8094 5.8654 12.8721 5.80615 12.9541C5.74691 13.0361 5.70247 13.1341 5.67285 13.248C5.64323 13.362 5.625 13.4907 5.61816 13.6343V14.1094C5.62956 14.278 5.66146 14.4295 5.71387 14.564C5.76628 14.6961 5.84717 14.8009 5.95654 14.8784C6.06592 14.9559 6.20947 14.9946 6.38721 14.9946C6.51709 14.9946 6.62646 14.9661 6.71533 14.9092C6.8042 14.8499 6.87598 14.769 6.93066 14.6665C6.98763 14.564 7.02751 14.4455 7.05029 14.311C7.07536 14.1766 7.08789 14.0319 7.08789 13.877ZM10.8716 14.8887V10.4453H11.8628V15.6953H10.9707L10.8716 14.8887ZM8.53369 13.8906V13.8188C8.53369 13.5363 8.56559 13.2799 8.62939 13.0498C8.6932 12.8174 8.78662 12.618 8.90967 12.4517C9.03271 12.2853 9.18424 12.1566 9.36426 12.0654C9.54427 11.9743 9.74935 11.9287 9.97949 11.9287C10.196 11.9287 10.3851 11.9743 10.5469 12.0654C10.7109 12.1566 10.8499 12.2865 10.9639 12.4551C11.0801 12.6214 11.1735 12.8185 11.2441 13.0464C11.3148 13.272 11.366 13.5192 11.3979 13.7881V13.9453C11.366 14.2028 11.3148 14.4421 11.2441 14.6631C11.1735 14.8841 11.0801 15.0778 10.9639 15.2441C10.8499 15.4082 10.7109 15.5358 10.5469 15.627C10.3828 15.7181 10.1914 15.7637 9.97266 15.7637C9.74251 15.7637 9.53743 15.717 9.35742 15.6235C9.17969 15.5301 9.0293 15.3991 8.90625 15.2305C8.78548 15.0618 8.6932 14.8636 8.62939 14.6357C8.56559 14.4079 8.53369 14.1595 8.53369 13.8906ZM9.51807 13.8188V13.8906C9.51807 14.0433 9.52946 14.1857 9.55225 14.3179C9.57731 14.45 9.61719 14.5674 9.67188 14.6699C9.72884 14.7702 9.80176 14.8488 9.89062 14.9058C9.98177 14.9604 10.0923 14.9878 10.2222 14.9878C10.3908 14.9878 10.5298 14.9502 10.6392 14.875C10.7485 14.7975 10.8317 14.6916 10.8887 14.5571C10.9479 14.4227 10.9821 14.2677 10.9912 14.0923V13.6445C10.9844 13.501 10.9639 13.3722 10.9297 13.2583C10.8978 13.1421 10.8499 13.043 10.7861 12.9609C10.7246 12.8789 10.6471 12.8151 10.5537 12.7695C10.4626 12.724 10.3543 12.7012 10.229 12.7012C10.1014 12.7012 9.99202 12.7308 9.90088 12.79C9.80973 12.847 9.73568 12.9256 9.67871 13.0259C9.62402 13.1261 9.58301 13.2446 9.55566 13.3813C9.5306 13.5158 9.51807 13.6616 9.51807 13.8188ZM13.8862 15.6953H12.895V11.6689C12.895 11.3887 12.9497 11.1528 13.0591 10.9614C13.1707 10.7677 13.3268 10.6219 13.5273 10.5239C13.7301 10.4237 13.9705 10.3735 14.2485 10.3735C14.3397 10.3735 14.4274 10.3804 14.5117 10.394C14.596 10.4054 14.6781 10.4202 14.7578 10.4385L14.7476 11.1802C14.7043 11.1688 14.6587 11.1608 14.6108 11.1562C14.563 11.1517 14.5072 11.1494 14.4434 11.1494C14.3249 11.1494 14.2235 11.1699 14.1392 11.2109C14.0571 11.2497 13.9945 11.3078 13.9512 11.3853C13.9079 11.4627 13.8862 11.5573 13.8862 11.6689V15.6953ZM14.6245 11.9971V12.6943H12.3447V11.9971H14.6245Z"
            fill="white"
          />
        </svg>
      );
    case "linkedin":
      return <FaLinkedinIn size={size || 24} color={color || "black"} />;
    case "edition":
      return (
        <svg
          width="13"
          height="13"
          viewBox="0 0 13 13"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0 10.292V13H2.70796L10.6946 5.01333L7.98667 2.30537L0 10.292ZM12.7888 2.91918C13.0704 2.63755 13.0704 2.18261 12.7888 1.90099L11.099 0.211221C10.8174 -0.0704069 10.3624 -0.0704069 10.0808 0.211221L8.75934 1.5327L11.4673 4.24066L12.7888 2.91918Z"
            fill={color}
          />
        </svg>
      );
    case "savedArticleFilled":
      return (
        <svg
          width="14"
          height="20"
          viewBox="0 0 14 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.5 1.5C0.5 1.22386 0.723858 1 1 1H13C13.2761 1 13.5 1.22386 13.5 1.5V18.0685C13.5 18.5152 12.9589 18.7377 12.6447 18.4203L8.06598 13.7954C7.479 13.2024 6.52101 13.2024 5.93402 13.7954L1.35533 18.4203C1.04108 18.7377 0.5 18.5152 0.5 18.0685V1.5Z"
            fill="white"
            stroke="white"
          />
        </svg>
      );
    case "replay":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#e8eaed"
        >
          <path d="M480-80q-75 0-140.5-28.5t-114-77q-48.5-48.5-77-114T120-440h80q0 117 81.5 198.5T480-160q117 0 198.5-81.5T760-440q0-117-81.5-198.5T480-720h-6l62 62-56 58-160-160 160-160 56 58-62 62h6q75 0 140.5 28.5t114 77q48.5 48.5 77 114T840-440q0 75-28.5 140.5t-77 114q-48.5 48.5-114 77T480-80Z" />
        </svg>
      );
    case "pause":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#e8eaed"
        >
          <path d="M360-320h80v-320h-80v320Zm160 0h80v-320h-80v320ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Z" />
        </svg>
      );
    case "mute":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#e8eaed"
        >
          <path d="M560-131v-82q90-26 145-100t55-168q0-94-55-168T560-749v-82q124 28 202 125.5T840-481q0 127-78 224.5T560-131ZM120-360v-240h160l200-200v640L280-360H120Zm440 40v-322q47 22 73.5 66t26.5 96q0 51-26.5 94.5T560-320Z" />
        </svg>
      );
    case "unmute":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#e8eaed"
        >
          <path d="M792-56 671-177q-25 16-53 27.5T560-131v-82q14-5 27.5-10t25.5-12L480-368v208L280-360H120v-240h128L56-792l56-56 736 736-56 56Zm-8-232-58-58q17-31 25.5-65t8.5-70q0-94-55-168T560-749v-82q124 28 202 125.5T840-481q0 53-14.5 102T784-288ZM650-422l-90-90v-130q47 22 73.5 66t26.5 96q0 15-2.5 29.5T650-422ZM480-592 376-696l104-104v208Z" />
        </svg>
      );
    case "videoPlay":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#e8eaed"
        >
          <path d="M320-200v-560l440 280-440 280Z" />
        </svg>
      );
    case "videoFullscreen":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="15"
          height="15"
          viewBox="0 0 15 15"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.15586 9.80938V13.8463H5.19332C5.34633 13.8463 5.49307 13.9071 5.60126 14.0153C5.70946 14.1234 5.77024 14.2702 5.77024 14.4232C5.77024 14.5761 5.70946 14.7229 5.60126 14.831C5.49307 14.9392 5.34633 15 5.19332 15H0.576936C0.501174 15 0.426153 14.9851 0.356158 14.9561C0.286162 14.9271 0.222564 14.8846 0.168992 14.831C0.11542 14.7775 0.0729235 14.7139 0.0439304 14.6439C0.0149373 14.5739 1.3864e-05 14.4989 1.3864e-05 14.4232V9.80938C1.3864e-05 9.65639 0.0607978 9.50967 0.168992 9.40149C0.277186 9.29331 0.423927 9.23254 0.576936 9.23254C0.652867 9.23227 0.728104 9.247 0.798331 9.27587C0.868557 9.30474 0.932391 9.34719 0.986175 9.40079C1.03996 9.45438 1.08264 9.51805 1.11175 9.58817C1.14087 9.65829 1.15586 9.73346 1.15586 9.80938ZM9.8087 14.996C9.65569 14.996 9.50895 14.9352 9.40075 14.827C9.29256 14.7189 9.23178 14.5721 9.23178 14.4192C9.23178 14.2662 9.29256 14.1194 9.40075 14.0113C9.50895 13.9031 9.65569 13.8423 9.8087 13.8423H13.8462V9.80539C13.8462 9.6524 13.9069 9.50567 14.0151 9.39749C14.1233 9.28931 14.2701 9.22854 14.4231 9.22854C14.5761 9.22854 14.7228 9.28931 14.831 9.39749C14.9392 9.50567 15 9.6524 15 9.80539V14.4192C15 14.5721 14.9392 14.7189 14.831 14.827C14.7228 14.9352 14.5761 14.996 14.4231 14.996H9.8087ZM9.8087 2.6651e-09H14.4231C14.5761 2.6651e-09 14.7228 0.0607778 14.831 0.168957C14.9392 0.277137 15 0.423857 15 0.576846V5.19861C15 5.3516 14.9392 5.49833 14.831 5.60651C14.7228 5.71469 14.5761 5.77546 14.4231 5.77546C14.2701 5.77546 14.1233 5.71469 14.0151 5.60651C13.9069 5.49833 13.8462 5.3516 13.8462 5.19861V1.16169H9.8087C9.65569 1.16169 9.50895 1.10092 9.40075 0.992739C9.29256 0.884559 9.23178 0.737836 9.23178 0.584847C9.23072 0.508424 9.24486 0.432552 9.27337 0.36164C9.30189 0.290728 9.34422 0.226187 9.3979 0.171772C9.45158 0.117357 9.51553 0.0741501 9.58606 0.044662C9.65658 0.0151739 9.73226 -7.3439e-06 9.8087 2.6651e-09ZM0.576936 5.77146C0.501174 5.77146 0.426153 5.75654 0.356158 5.72755C0.286162 5.69856 0.222564 5.65607 0.168992 5.60251C0.11542 5.54894 0.0729235 5.48535 0.0439304 5.41536C0.0149373 5.34538 1.3864e-05 5.27037 1.3864e-05 5.19462V0.580846C-0.000513694 0.504758 0.0140198 0.429316 0.0427766 0.358867C0.0715334 0.288419 0.113946 0.224358 0.167571 0.170369C0.221196 0.116379 0.284973 0.0735311 0.355231 0.0442921C0.425488 0.0150531 0.500836 -1.82574e-06 0.576936 2.6651e-09H5.19132C5.34433 2.6651e-09 5.49107 0.0607778 5.59926 0.168957C5.70746 0.277137 5.76824 0.423857 5.76824 0.576846C5.76824 0.729835 5.70746 0.876558 5.59926 0.984738C5.49107 1.09292 5.34433 1.1537 5.19132 1.1537H1.15586V5.19861C1.1548 5.35126 1.09328 5.49726 0.984766 5.60463C0.876254 5.712 0.729601 5.772 0.576936 5.77146Z"
            fill="white"
          />
        </svg>
      );
    case "fullscreenExit":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24px"
          viewBox="0 -960 960 960"
          width="24px"
          fill="#e8eaed"
        >
          <path d="M240-120v-120H120v-80h200v200h-80Zm400 0v-200h200v80H720v120h-80ZM120-640v-80h120v-120h80v200H120Zm520 0v-200h80v120h120v80H640Z" />
        </svg>
      );
    case "newWindow":
      return (
        <svg
          width="11"
          height="11"
          viewBox="0 0 11 11"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.72222 0V1.22222H8.91611L2.90889 7.22944L3.77056 8.09111L9.77778 2.08389V4.27778H11V0M9.77778 9.77778H1.22222V1.22222H5.5V0H1.22222C0.898069 0 0.587192 0.128769 0.357981 0.357981C0.128769 0.587192 0 0.898069 0 1.22222V9.77778C0 10.1019 0.128769 10.4128 0.357981 10.642C0.587192 10.8712 0.898069 11 1.22222 11H9.77778C10.1019 11 10.4128 10.8712 10.642 10.642C10.8712 10.4128 11 10.1019 11 9.77778V5.5H9.77778V9.77778Z"
            fill={color}
          />
        </svg>
      );
    case "play":
      return (
        <svg
          width="13"
          height="15"
          viewBox="0 0 13 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M11.7053 6.4517L1.60212 0.438395C1.47873 0.365814 1.35171 0.322266 1.20655 0.322266C0.810988 0.322266 0.488004 0.648879 0.488004 1.04807H0.484375V13.5319H0.488004C0.488004 13.9311 0.810988 14.2577 1.20655 14.2577C1.35534 14.2577 1.47873 14.2069 1.613 14.1344L11.7053 8.12831C11.9449 7.92872 12.0973 7.62751 12.0973 7.29001C12.0973 6.95251 11.9449 6.65493 11.7053 6.4517Z"
            fill="white"
          />
        </svg>
      );
    case "expandMediaSquare":
      return (
        <svg
          width="17"
          height="18"
          viewBox="0 0 17 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.25 17.2479V12.6651H2.08333V15.4148H4.83333V17.2479H0.25ZM12.1667 17.2479V15.4148H14.9167V12.6651H16.75V17.2479H12.1667ZM0.25 5.33274V0.75H4.83333V2.5831H2.08333V5.33274H0.25ZM14.9167 5.33274V2.5831H12.1667V0.75H16.75V5.33274H14.9167Z"
            fill="white"
          />
        </svg>
      );
    case "collapseMediaSquare":
      return (
        <svg
          width="19"
          height="18"
          viewBox="0 0 19 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3.5 18V15H0.5V13H5.5V18H3.5ZM13.5 18V13H18.5V15H15.5V18H13.5ZM0.5 5V3H3.5V0H5.5V5H0.5ZM13.5 5V0H15.5V3H18.5V5H13.5Z"
            fill="white"
          />
        </svg>
      );
    case "google_colored":
      return (
        <svg
          width="30"
          height="30"
          viewBox="0 0 30 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
        >
          <rect
            x="3"
            y="4"
            width="24"
            height="22.3636"
            fill="url(#pattern0_5370_33581)"
          />
          <defs>
            <pattern
              id="pattern0_5370_33581"
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref="#image0_5370_33581"
                transform="scale(0.0227273 0.0243902)"
              />
            </pattern>
            <image
              id="image0_5370_33581"
              width="44"
              height="41"
              xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAApCAIAAADBK1zlAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAALKADAAQAAAABAAAAKQAAAAA0jmjTAAAEaklEQVRYCWP8//8/w0ADpoF2AMh+FjIc8e/l898njvy5e+vfq+e/bt389+kT0BA2NQ0mPl4GLl52azs2C1sGbh7iTWYkKTp+7dn2c8+2H2dOE7QA6Cbu2GQWSzuCKoEKiHXEn8vnP/c0/3n6lBhD4Wo4TEw5o5NZdA3hIlgZRDni26yJX1ctw6qfGEHepHSOqCQ8Kgk54uuXj6XZv27dwGMEMVIcdo68dR24VOLNHVRyARMvD3dMMi4XAMXxhcTHrDhg4sejmRgpoAsEe2cwKaniUYwzi37pa8HvAjY1dTZre1Y9I7jpvy+d+3X0ILIuYlwA1I49JIB54X1hBtx0NAaHkQlnbAquNA/U+33xnB/nzhDpApyOwBMRfDnF7AFhaM7C5AIzFIeLF/5YgOvCEhL/n2/+sar46y6B/z8Z4eogDP6KejYXLzRByrlYcsf/Z2tZdR7yxrxgFviLbAF3WBQtXAC0Aj0k/v949veoIsTu/z+5vm+T/3mDHchlkZISXLQe2U1UZKOHxP/XB+CmM7J/4wq8zmn/BSjCHZcKF6c6AyOLfrmGZgeH1R0WRU38EXH54T/05INmCgZXRx7hfwxHfLyCoZ6BTd8eUxBZpGDJd2QuMey91dxwZQjnQIT+fd0Ol4MzGHk04WxqMYCBBzcK3RFwCWTGfxY+ZC7V2UQ5guq2ohlIlCMY/4AacLQD6I5g4vbEtOz/l+uYghSK6OLLHfw6DBhp88nHh3J47TSQZ8Yl//UHw+2XKCUvpkqMLMqjhaZo2XfTSW+Y2+4fdFPEmVF7YzjQdMG5M3b/wnSEpABKDKBwgDoZRR3g+r/942n6Yj/pqzhQZPbVVV9/fYNLEc84cgtLMNiooYQchiM4pJgEIoF2PPgjl/LRZtsPXoh997++WHh1FfF2Q1SuO/3n+QdEeQDXrocafeiOAKpjlAre91M3+5P2vb8okTXv1uZd9w/CDSLIuPfy3+JDPzGV8XIyWOEPCZAjJH1XMDu+/YcSYhCzqk5NWndrG6a5mCJAFxQv+f7pB6YMQ4gpqFpGBuhVOUTu4uuryfvqkNUhs23FdBN0QvVFtZEF4Wxg0ll3e9usIx/Z3kTABeEMYDAsyeLmQU3H2B0B1NN5atrq+3vhmjEZBoLK9tJm+mKI3HTn/YMLr69ue3ICopj1pxLnszLGv1zIehPt2GNsUWIZKIvTEUC5pJ2llz7cQzaCVDbzPx6OF/ks39QhGoHFCdbMjM8RwIDN3V9PoTuA1nO9iWP94KIqztwTw4EWERDH4XMEUAW13CH/z3uaZ6K4APamDwFHQFw67fwCYP6EsMkgPaUtKsyyudlQEgeyOUQ5AqgBmF8mnltAatQIsvJk6oQHqXkhW4nJJtYREJ3Awmrrg31HX2FpAqIZrcAlHqXu467ggCcA4FpIcwRE28tvry++vHb+9dVnX19ee//g/W9QcxxoqzSPKB8rj6GYtoGYlpKAAtwOggxyHEHQUFIVYKk7SDWCcvWDwhEAdf6b57WUEQQAAAAASUVORK5CYII="
            />
          </defs>
        </svg>
      );
    case "apple":
      return (
        <svg
          width="16"
          height="20"
          viewBox="0 0 16 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.1675 10.5911C13.1939 13.4559 15.6807 14.4093 15.708 14.4216C15.6868 14.4886 15.3106 15.7804 14.3976 17.1146C13.609 18.2681 12.7895 19.4171 11.4994 19.4409C10.2313 19.4647 9.82423 18.6893 8.37467 18.6893C6.92598 18.6893 6.47305 19.4171 5.27286 19.4647C4.02773 19.5123 3.07956 18.2178 2.28384 17.0688C0.65892 14.7168 -0.583565 10.4263 1.08454 7.52893C1.91286 6.09082 3.39415 5.17967 5.00145 5.15587C6.22455 5.13296 7.37803 5.97891 8.12617 5.97891C8.87342 5.97891 10.2754 4.96113 11.7505 5.11093C12.3674 5.13649 14.1007 5.36031 15.2127 6.98876C15.1237 7.04428 13.1455 8.196 13.1675 10.5911ZM10.7847 3.5565C11.4456 2.75638 11.8906 1.64255 11.769 0.534004C10.8165 0.571896 9.66385 1.16935 8.98005 1.96859C8.36762 2.67707 7.83097 3.81029 7.97548 4.8968C9.03821 4.97875 10.1238 4.35663 10.7847 3.5565Z"
            fill={color}
          />
        </svg>
      );
    case "facebook_colored":
      return (
        <svg
          width="31"
          height="30"
          viewBox="0 0 31 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
        >
          <rect
            x="5.5"
            y="5"
            width="19"
            height="21"
            fill="url(#pattern0_5370_33718)"
          />
          <defs>
            <pattern
              id="pattern0_5370_33718"
              patternContentUnits="objectBoundingBox"
              width="1"
              height="1"
            >
              <use
                xlinkHref="#image0_5370_33718"
                transform="matrix(0.0287081 0 0 0.025974 -0.157895 -0.0476191)"
              />
            </pattern>
            <image
              id="image0_5370_33718"
              width="44"
              height="41"
              xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAApCAIAAADBK1zlAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAALKADAAQAAAABAAAAKQAAAAA0jmjTAAADnUlEQVRYCc1YTUhUURSeeTPNjBJSkKNTMKmVA6HTjz8LBWsxNhH9kNFGaJEYBMFUazFICGqX7YrSnS2kaCG1yE0uophJ0aAwLFPLP7Ifghxt5k3f9OLdN2fuu+89dbBZ3ffued/33XPPPfecsadSKdt6/6T1FpDm/y9EOFfgiYkvqedj8vBkYmxenvyWmlmUAeLLk/yb7bu8UtDvrNspbd9iN49stxQTD2OJ3lji2ceEIcGBEufpamdTtalFmhXx5HWy8+nSyHx60eZ/e4qkSyF3uNIh/sRYBA7PlQfL96LLYiDBbGuNq+OUS2BgIGL+RyrSszQwYex/AQemsDudzW5vAT9QRCKgoKV7cXDW2hboqdlf7Ohu8RTydOgeUexCpCe+VgqgbHA2CadyJep6ot1KHJRvki42unb7pMDWf6uKfkj778SdX4SVGx/8I4SzYD4SbxzznKmnODVlfB/fjS7XlzvIeeGb4jSSFeg9toXc2Qr0jJX3N/spOEcEMpLJfIBduBDaIKbMnh2ek0GhfU/diDnkRK2FYHy4gq9gdEb+GRd8l6bQJlMqAveCmaysMNSW0VT46FWivW9pYdGgPAAFiNT7hW4HbibREjLnCvIyn202MwqUb16MJdWPqQjcjeqc1UFsXDb0gYo5NKUvArezapfTgZaIegL1QU65VfBPXxkRFaFUKKpp7gZzf0shBZ+eDjyzvcqSgNQkSAzVpdL09Y3qRwiR47dp2lZntQPqicI8+kZrbWn8dloUXsX5jIiNFALUiZaYBMafheG1TUNERaBSFeBamnozI9hYW7mXJTpKiVrZEpPAeHJBtB1BP6OmlKjWBbi9sd8vx9n62o66Aj5mjyvjWh8rRd99F4mo28E8QUUgn6Me1Ls+gKuFjsQzyldcWv3vTSXcg6VOv6YxYetQHYB+QR3naEAoOCJwyaJfyBE9YPcVSSerMtbJJ0PHkjsRkUYKzheBGrC1NmO/10rTuVpXuIKFpALLF4G5jiZXQ0mG01avAyF/tYmzNl0RoLzV7EbHsnpuBaHK5wAgF00kAl1bV4sH8rlfWnoJkK6z/PYLOCIRmIaO++c96FgsURJjhBdAuA2gYmlqleip0bGgX0C1TgjEjzjql0PuQ0Z/DZgSASacl3Blvvk/SZATkZFIPtBTrNuL6n2A96jWUSsPTckNAceRIIvcxyPJgdHkXr+Ee0GblQVQytRKRBiCWjUwCEyrcCuz/wPrwDQLvj8ioQAAAABJRU5ErkJggg=="
            />
          </defs>
        </svg>
      );
    case "arrow_right":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="10"
          height="9"
          viewBox="0 0 10 9"
          fill="none"
        >
          <g clipPath="url(#clip0_7450_1924)">
            <path
              d="M9.36486 4.0946L5.39189 0.121623C5.22973 -0.0405396 4.97838 -0.0405397 4.81622 0.121622C4.65405 0.283785 4.65405 0.535136 4.81621 0.697298L8.22162 4.1027L0.405405 4.1027C0.178378 4.1027 -7.48528e-07 4.28108 -7.88223e-07 4.50811C-8.27917e-07 4.73514 0.178377 4.91351 0.405404 4.91351L7.97838 4.91352L4.57297 8.31892C4.41081 8.48108 4.41081 8.73243 4.57297 8.8946C4.65405 8.97568 4.75946 9.01622 4.85675 9.01622C4.95405 9.01622 5.06756 8.97568 5.14054 8.8946L9.35675 4.67838C9.51892 4.51622 9.51892 4.26487 9.35675 4.1027L9.36486 4.0946Z"
              fill={color}
            />
          </g>
          <defs>
            <clipPath id="clip0_7450_1924">
              <rect width="9.48648" height="9" fill="white" />
            </clipPath>
          </defs>
        </svg>
      );
    case "gallery":
      return (
        <svg
          width="20"
          height="19"
          viewBox="0 0 20 19"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.24479 11.2129H16.4115L13.249 7.08789L11.1406 9.83789L9.71979 8.00456L7.24479 11.2129ZM6.32812 14.8796C5.82396 14.8796 5.39236 14.7 5.03333 14.341C4.67431 13.982 4.49479 13.5504 4.49479 13.0462V2.04622C4.49479 1.54206 4.67431 1.11046 5.03333 0.751432C5.39236 0.392405 5.82396 0.212891 6.32812 0.212891H17.3281C17.8323 0.212891 18.2639 0.392405 18.6229 0.751432C18.9819 1.11046 19.1615 1.54206 19.1615 2.04622V13.0462C19.1615 13.5504 18.9819 13.982 18.6229 14.341C18.2639 14.7 17.8323 14.8796 17.3281 14.8796H6.32812ZM6.32812 13.0462H17.3281V2.04622H6.32812V13.0462ZM2.66146 18.5462C2.15729 18.5462 1.72569 18.3667 1.36667 18.0077C1.00764 17.6487 0.828125 17.2171 0.828125 16.7129V3.87956H2.66146V16.7129H15.4948V18.5462H2.66146Z"
            fill={color}
          />
        </svg>
      );
    case "loading":
      return (
        <svg
          width="21"
          height="21"
          viewBox="0 0 21 21"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.5 20.5C9.13333 20.5 7.84167 20.2375 6.625 19.7125C5.40833 19.1875 4.34583 18.4708 3.4375 17.5625C2.52917 16.6542 1.8125 15.5917 1.2875 14.375C0.7625 13.1583 0.5 11.8667 0.5 10.5C0.5 9.11667 0.7625 7.82083 1.2875 6.6125C1.8125 5.40417 2.52917 4.34583 3.4375 3.4375C4.34583 2.52917 5.40833 1.8125 6.625 1.2875C7.84167 0.7625 9.13333 0.5 10.5 0.5C10.7833 0.5 11.0208 0.595833 11.2125 0.7875C11.4042 0.979167 11.5 1.21667 11.5 1.5C11.5 1.78333 11.4042 2.02083 11.2125 2.2125C11.0208 2.40417 10.7833 2.5 10.5 2.5C8.28333 2.5 6.39583 3.27917 4.8375 4.8375C3.27917 6.39583 2.5 8.28333 2.5 10.5C2.5 12.7167 3.27917 14.6042 4.8375 16.1625C6.39583 17.7208 8.28333 18.5 10.5 18.5C12.7167 18.5 14.6042 17.7208 16.1625 16.1625C17.7208 14.6042 18.5 12.7167 18.5 10.5C18.5 10.2167 18.5958 9.97917 18.7875 9.7875C18.9792 9.59583 19.2167 9.5 19.5 9.5C19.7833 9.5 20.0208 9.59583 20.2125 9.7875C20.4042 9.97917 20.5 10.2167 20.5 10.5C20.5 11.8667 20.2375 13.1583 19.7125 14.375C19.1875 15.5917 18.4708 16.6542 17.5625 17.5625C16.6542 18.4708 15.5958 19.1875 14.3875 19.7125C13.1792 20.2375 11.8833 20.5 10.5 20.5Z"
            fill={color}
          />
        </svg>
      );
    case "latest":
      return (
        <svg
          width="22"
          height="16"
          viewBox="0 0 22 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.5 15.5C5.41667 15.5 3.64583 14.7708 2.1875 13.3125C0.729167 11.8542 0 10.0833 0 8C0 5.91667 0.729167 4.14583 2.1875 2.6875C3.64583 1.22917 5.41667 0.5 7.5 0.5C9.58333 0.5 11.3542 1.22917 12.8125 2.6875C14.2708 4.14583 15 5.91667 15 8C15 10.0833 14.2708 11.8542 12.8125 13.3125C11.3542 14.7708 9.58333 15.5 7.5 15.5ZM18.5 16L15 12.5L16.425 11.1L17.5 12.175V0H19.5V12.2L20.6 11.1L22 12.5L18.5 16ZM7.5 13.5C9.03333 13.5 10.3333 12.9667 11.4 11.9C12.4667 10.8333 13 9.53333 13 8C13 6.46667 12.4667 5.16667 11.4 4.1C10.3333 3.03333 9.03333 2.5 7.5 2.5C5.96667 2.5 4.66667 3.03333 3.6 4.1C2.53333 5.16667 2 6.46667 2 8C2 9.53333 2.53333 10.8333 3.6 11.9C4.66667 12.9667 5.96667 13.5 7.5 13.5ZM9.5 11.5L10.9 10.1L8.5 7.675V4H6.5V8.5L9.5 11.5Z"
            fill={color}
          />
        </svg>
      );
    case "oldest":
      return (
        <svg
          width="23"
          height="16"
          viewBox="0 0 23 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.88477 15.5C5.80143 15.5 4.0306 14.7708 2.57227 13.3125C1.11393 11.8542 0.384766 10.0833 0.384766 8C0.384766 5.91667 1.11393 4.14583 2.57227 2.6875C4.0306 1.22917 5.80143 0.5 7.88477 0.5C9.9681 0.5 11.7389 1.22917 13.1973 2.6875C14.6556 4.14583 15.3848 5.91667 15.3848 8C15.3848 10.0833 14.6556 11.8542 13.1973 13.3125C11.7389 14.7708 9.9681 15.5 7.88477 15.5ZM17.8848 16V3.8L16.7848 4.9L15.3848 3.5L18.8848 0L22.3848 3.5L20.9598 4.9L19.8848 3.825V16H17.8848ZM7.88477 13.5C9.4181 13.5 10.7181 12.9667 11.7848 11.9C12.8514 10.8333 13.3848 9.53333 13.3848 8C13.3848 6.46667 12.8514 5.16667 11.7848 4.1C10.7181 3.03333 9.4181 2.5 7.88477 2.5C6.35143 2.5 5.05143 3.03333 3.98477 4.1C2.9181 5.16667 2.38477 6.46667 2.38477 8C2.38477 9.53333 2.9181 10.8333 3.98477 11.9C5.05143 12.9667 6.35143 13.5 7.88477 13.5ZM9.88477 11.5L11.2848 10.1L8.88477 7.675V4H6.88477V8.5L9.88477 11.5Z"
            fill={color}
          />
        </svg>
      );
    case "search2":
      return (
        <svg
          width="21"
          height="13"
          viewBox="0 0 21 13"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1.76953 13C1.4862 13 1.2487 12.9042 1.05703 12.7125C0.865365 12.5208 0.769531 12.2833 0.769531 12C0.769531 11.7167 0.865365 11.4792 1.05703 11.2875C1.2487 11.0958 1.4862 11 1.76953 11H9.76953C10.0529 11 10.2904 11.0958 10.482 11.2875C10.6737 11.4792 10.7695 11.7167 10.7695 12C10.7695 12.2833 10.6737 12.5208 10.482 12.7125C10.2904 12.9042 10.0529 13 9.76953 13H1.76953ZM1.76953 8C1.4862 8 1.2487 7.90417 1.05703 7.7125C0.865365 7.52083 0.769531 7.28333 0.769531 7C0.769531 6.71667 0.865365 6.47917 1.05703 6.2875C1.2487 6.09583 1.4862 6 1.76953 6H4.76953C5.05286 6 5.29036 6.09583 5.48203 6.2875C5.6737 6.47917 5.76953 6.71667 5.76953 7C5.76953 7.28333 5.6737 7.52083 5.48203 7.7125C5.29036 7.90417 5.05286 8 4.76953 8H1.76953ZM1.76953 3C1.4862 3 1.2487 2.90417 1.05703 2.7125C0.865365 2.52083 0.769531 2.28333 0.769531 2C0.769531 1.71667 0.865365 1.47917 1.05703 1.2875C1.2487 1.09583 1.4862 1 1.76953 1H4.76953C5.05286 1 5.29036 1.09583 5.48203 1.2875C5.6737 1.47917 5.76953 1.71667 5.76953 2C5.76953 2.28333 5.6737 2.52083 5.48203 2.7125C5.29036 2.90417 5.05286 3 4.76953 3H1.76953ZM12.7695 10C11.3862 10 10.207 9.5125 9.23203 8.5375C8.25703 7.5625 7.76953 6.38333 7.76953 5C7.76953 3.61667 8.25703 2.4375 9.23203 1.4625C10.207 0.4875 11.3862 0 12.7695 0C14.1529 0 15.332 0.4875 16.307 1.4625C17.282 2.4375 17.7695 3.61667 17.7695 5C17.7695 5.48333 17.6987 5.9625 17.557 6.4375C17.4154 6.9125 17.2029 7.35 16.9195 7.75L20.0695 10.9C20.2529 11.0833 20.3445 11.3167 20.3445 11.6C20.3445 11.8833 20.2529 12.1167 20.0695 12.3C19.8862 12.4833 19.6529 12.575 19.3695 12.575C19.0862 12.575 18.8529 12.4833 18.6695 12.3L15.5195 9.15C15.1195 9.43333 14.682 9.64583 14.207 9.7875C13.732 9.92917 13.2529 10 12.7695 10ZM12.7695 8C13.6029 8 14.3112 7.70833 14.8945 7.125C15.4779 6.54167 15.7695 5.83333 15.7695 5C15.7695 4.16667 15.4779 3.45833 14.8945 2.875C14.3112 2.29167 13.6029 2 12.7695 2C11.9362 2 11.2279 2.29167 10.6445 2.875C10.0612 3.45833 9.76953 4.16667 9.76953 5C9.76953 5.83333 10.0612 6.54167 10.6445 7.125C11.2279 7.70833 11.9362 8 12.7695 8Z"
            fill={color}
          />
        </svg>
      );
    case "share2":
      return (
        <svg
          width="24"
          height="25"
          viewBox="0 0 24 25"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask
            id="mask0_14608_134543"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="24"
            height="25"
          >
            <rect y="0.5" width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_14608_134543)">
            <path
              d="M6 23.5C5.45 23.5 4.97917 23.3042 4.5875 22.9125C4.19583 22.5208 4 22.05 4 21.5V10.5C4 9.95 4.19583 9.47917 4.5875 9.0875C4.97917 8.69583 5.45 8.5 6 8.5H9V10.5H6V21.5H18V10.5H15V8.5H18C18.55 8.5 19.0208 8.69583 19.4125 9.0875C19.8042 9.47917 20 9.95 20 10.5V21.5C20 22.05 19.8042 22.5208 19.4125 22.9125C19.0208 23.3042 18.55 23.5 18 23.5H6ZM11 16.5V5.325L9.4 6.925L8 5.5L12 1.5L16 5.5L14.6 6.925L13 5.325V16.5H11Z"
              fill={color}
            />
          </g>
        </svg>
      );
    case "clip":
      return (
        <svg
          width="40"
          height="20"
          viewBox="0 0 40 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.6289 11.5132H14.6602C14.6064 12.0073 14.465 12.4495 14.2358 12.8398C14.0067 13.2301 13.6826 13.5399 13.2637 13.769C12.8447 13.9946 12.3219 14.1074 11.6953 14.1074C11.237 14.1074 10.8198 14.0215 10.4438 13.8496C10.0715 13.6777 9.75098 13.4342 9.48242 13.1191C9.21387 12.8005 9.00618 12.4191 8.85938 11.9751C8.71615 11.5275 8.64453 11.0298 8.64453 10.4819V9.70312C8.64453 9.15527 8.71615 8.65934 8.85938 8.21533C9.00618 7.76774 9.21566 7.3846 9.48779 7.06592C9.76351 6.74723 10.0947 6.50195 10.4814 6.33008C10.8682 6.1582 11.3032 6.07227 11.7866 6.07227C12.3774 6.07227 12.877 6.18327 13.2852 6.40527C13.6934 6.62728 14.0103 6.93522 14.2358 7.3291C14.465 7.7194 14.6064 8.17236 14.6602 8.68799H13.6289C13.5788 8.32275 13.4857 8.00944 13.3496 7.74805C13.2135 7.48307 13.0202 7.27897 12.7695 7.13574C12.5189 6.99251 12.1912 6.9209 11.7866 6.9209C11.4393 6.9209 11.1331 6.98714 10.8682 7.11963C10.6068 7.25212 10.3866 7.4401 10.2075 7.68359C10.0321 7.92708 9.89958 8.21891 9.81006 8.55908C9.72054 8.89925 9.67578 9.27702 9.67578 9.69238V10.4819C9.67578 10.8651 9.71517 11.2249 9.79395 11.5615C9.8763 11.8981 9.99984 12.1935 10.1646 12.4478C10.3293 12.702 10.5387 12.9025 10.793 13.0493C11.0472 13.1925 11.348 13.2642 11.6953 13.2642C12.1357 13.2642 12.4867 13.1943 12.748 13.0547C13.0094 12.915 13.2064 12.7145 13.3389 12.4531C13.4749 12.1917 13.5716 11.8784 13.6289 11.5132ZM21.2554 13.1567V14H17.3453V13.1567H21.2554ZM17.5494 6.17969V14H16.5128V6.17969H17.5494ZM23.9943 6.17969V14H22.9576V6.17969H23.9943ZM29.2038 10.9331H27.1144V10.0898H29.2038C29.6084 10.0898 29.936 10.0254 30.1867 9.89648C30.4373 9.76758 30.62 9.58854 30.7346 9.35938C30.8527 9.13021 30.9118 8.86882 30.9118 8.5752C30.9118 8.30664 30.8527 8.0542 30.7346 7.81787C30.62 7.58154 30.4373 7.39176 30.1867 7.24854C29.936 7.10173 29.6084 7.02832 29.2038 7.02832H27.3561V14H26.3195V6.17969H29.2038C29.7946 6.17969 30.2941 6.28174 30.7023 6.48584C31.1105 6.68994 31.4203 6.97282 31.6315 7.33447C31.8428 7.69255 31.9484 8.10254 31.9484 8.56445C31.9484 9.06576 31.8428 9.49365 31.6315 9.84814C31.4203 10.2026 31.1105 10.473 30.7023 10.6592C30.2941 10.8418 29.7946 10.9331 29.2038 10.9331Z"
            fill="#A8A8A8"
          />
          <rect
            x="0.5"
            y="0.5"
            width="39"
            height="19"
            rx="6.5"
            stroke="#989898"
          />
        </svg>
      );
  }
};

export const Icon = ({ icon, size, color = "black", ...props }: IconProps) => {
  const svgIcon = getSvgIcon(icon, size, color);

  return ICONS[icon] === null ? (
    <span className={props.className} data-testid={icon}>
      {svgIcon &&
        React.cloneElement(svgIcon, {
          className: "w-full h-full",
        })}
    </span>
  ) : (
    <span
      className={cx("material-symbols-rounded", props.className)}
      data-testid={icon}
    >
      {ICONS[icon]}
    </span>
  );
};
