"use client";
import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";
import { Article } from "@/sanity/queries/Trending/trending";
import { format, subDays, subMonths } from "date-fns";
import Skeleton from "@/components/ui/Skeleton";
import Select from "react-select";
import { TrendingVideos } from "@/components/TrendingVideos/page";
import AuthorsList from "@/components/Author/AuthorsList";
import { mockVideoItems } from "../watch/mocks";

type DateOption = "yesterday" | "past_week" | "past_month";

interface TrendingClientProps {
  trendingArticles: Article[];
}

const TrendingNewsClient: React.FC<TrendingClientProps> = ({
  trendingArticles: initialArticles,
}) => {
  const [articles, setArticles] = useState<Article[]>(initialArticles);
  const [dateOption, setDateOption] = useState<DateOption>("yesterday");
  const [loading, setLoading] = useState(false);

  const getDateRange = (option: DateOption) => {
    const today = new Date();
    const periodEnd = subDays(today, 1);
    let periodStart: Date;

    switch (option) {
      case "yesterday":
        periodStart = periodEnd;
        break;
      case "past_week":
        periodStart = subDays(today, 7);
        break;
      case "past_month":
        periodStart = subMonths(today, 1);
        break;
      default:
        periodStart = periodEnd;
    }

    const formatDate = (date: Date) => format(date, "yyyy-MM-dd");
    return {
      periodStart: formatDate(periodStart),
      periodEnd: formatDate(periodEnd),
    };
  };

  const handleDateChange = async (selectedOption: {
    value: DateOption;
    label: string;
  }) => {
    const value = selectedOption.value;
    setDateOption(value);
    setLoading(true);

    const { periodStart, periodEnd } = getDateRange(value);
    const params = new URLSearchParams({
      listSize: "10",
      periodStart,
      periodEnd,
    });

    try {
      const res = await fetch(`/api/trending?${params.toString()}`);
      if (!res.ok) throw new Error("Failed to fetch articles");
      const data = await res.json();
      setArticles(data);
    } catch (err) {
      console.error("Error fetching trending articles:", err);
    } finally {
      setLoading(false);
    }
  };

  const options = [
    { value: "yesterday", label: "Yesterday" },
    { value: "past_week", label: "Past Week" },
    { value: "past_month", label: "Past Month" },
  ];

  return (
    <div className="container mx-auto p-4 pt-12">
      {/* Breadcrumbs */}
      <header>
        <nav className="mt-2">
          <ol className="flex items-center gap-2 text-[13px] xl:text-[14px]">
            <li className="underline hover:text-red-700">
              <Link href="/">Home</Link>
            </li>
            <li>·</li>
            <li>Trending Stories</li>
          </ol>
        </nav>
      </header>

      {/* Header */}
      <div className="header pt-7 pb-7 flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <h1 className="font-titles font-semibold text-[32px] xl:text-[52px] leading-10 xl:leading-[56px]">
          Trending Stories
        </h1>

        {/* Date Filter Dropdown */}
        <div className="w-full md:w-auto flex justify-end">
          <div className="flex items-center gap-2 max-w-full">
            <label htmlFor="date-filter" className="text-sm whitespace-nowrap">
              Date range
            </label>
            <div className="min-w-[120px] max-w-full">
              <Select
                inputId="date-filter"
                value={options.find((o) => o.value === dateOption)}
                onChange={(value) =>
                  handleDateChange(
                    value as { value: DateOption; label: string },
                  )
                }
                options={options}
                isSearchable={false}
                classNamePrefix="react-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    minHeight: "32px",
                    fontSize: "0.875rem",
                    borderRadius: "0.75rem",
                    padding: "0.25rem 0.75rem",
                    color: "black",
                    whiteSpace: "nowrap",
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: "black",
                  }),
                  dropdownIndicator: (base) => ({ ...base, padding: "4px" }),
                  indicatorsContainer: (base) => ({ ...base, height: "32px" }),
                  menu: (base) => ({
                    ...base,
                    borderRadius: "0.75rem",
                    overflow: "hidden",
                  }),
                  option: (base, state) => ({
                    ...base,
                    color: state.isSelected ? "black" : "inherit",
                    borderBottom: "1px solid #e5e7eb",
                    backgroundColor: state.isSelected
                      ? "#f3f4f6"
                      : state.isFocused
                        ? "#f9fafb"
                        : "white",
                    ":last-of-type": {
                      borderBottom: "none",
                    },
                  }),
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Articles */}
      <div className="xl:grid xl:grid-cols-12 xl:gap-4 mt-4 pt-2 border-t border-grey-200">
        {/* Left Column */}
        <div className="xl:col-span-9 pt-3">
          <section className="overflow-hidden xl:pb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-1 gap-6">
              {loading
                ? Array.from({ length: 10 }).map((_, i) => (
                    <Skeleton
                      key={`skeleton-${i}`}
                      imagePosition="left"
                      imageHeight="12rem"
                      imageWidth="22rem"
                      showTitle={true}
                    />
                  ))
                : articles?.slice(0, 10).map((article, index) => (
                    <div
                      key={article.slug || `article-${index}`}
                      className="flex gap-4 pb-6 mb-4 border-b border-b-grey-200 xl:block xl:gap-0 xl:flex-none sm:items-start"
                    >
                      {/* Index */}
                      <div className="text-[52px] font-semibold text-gray-500 pt-2 pr-2 w-6 flex-shrink-0 font-titles -mt-1 xl:float-left mr-4 xl:mr-8">
                        {index + 1}
                      </div>

                      {/* Image + Title */}
                      <div className="flex flex-col flex-1 gap-4 md:flex-row xl:items-start xl:gap-8 items-start">
                        {/* Image */}
                        {article.media && (
                          <div className="relative aspect-[16/9] w-full md:w-[240px] xl:w-1/3 flex-shrink-0">
                            <Link
                              href={getArticleUrl(
                                article.category?.slug,
                                article.slug,
                                article.subcategory?.slug,
                              )}
                              className="transition-opacity hover:opacity-85"
                            >
                              {article.media[0] && (
                                <Image
                                  src={getMediaImage(article.media[0])}
                                  alt={article.title}
                                  fill
                                  className="object-cover rounded-2xl w-full h-full"
                                />
                              )}
                            </Link>
                          </div>
                        )}

                        {/* Title */}
                        <div className="xl:w-2/3 mt-1 xl:mt-6">
                          <h2 className="font-semibold font-titles text-[24px] leading-[28px] xl:text-[28px] xl:leading-[36px]">
                            <Link
                              href={getArticleUrl(
                                article.category?.slug,
                                article.slug,
                                article.subcategory?.slug,
                              )}
                              className="hover-underline-animation"
                            >
                              <>
                                <span className="block xl:hidden">
                                  {truncateTitle(article.title, 100)}
                                </span>
                                <span className="hidden xl:block">
                                  {article.title}
                                </span>
                              </>
                            </Link>
                          </h2>

                          <p className="hidden lg:block mt-5 text-16px leading-[24px]">
                            {truncateDescription(article.description)}
                          </p>

                          {article.authors && (
                            <AuthorsList
                              authors={article.authors}
                              publishedDate={article.updated_at}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
            </div>
          </section>
        </div>

        {/* Right Column */}
        <div className="col-span-3">
          <div className="sticky top-24">
            <TrendingVideos trendingVideos={mockVideoItems} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendingNewsClient;
