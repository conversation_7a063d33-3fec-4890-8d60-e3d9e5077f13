import { sanityFetch } from "@/sanity/client";
import MyInterestsClient from "./MyInterestsClient";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import { auth0 } from "@/lib/auth0";
import { allTagsQuery, SearchTag } from "@/sanity/queries/Tag/tag";

export const metadata: Metadata = {
  title: "Account - My Interests",
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_URL}/account/interests`,
  },
  other: {
    "parsely-title": "Account - My Interests",
    "parsely-type": "index",
  },
};

async function fetchTags(): Promise<SearchTag[]> {
  return await sanityFetch<SearchTag[]>({ query: allTagsQuery });
}

export default async function MyInterestsPage() {
  const session = await auth0.getSession();

  if (!session?.user) {
    redirect("/auth/login?returnTo=/account");
  }

  const tags = await fetchTags();
  return <MyInterestsClient tags={tags} />;
}
