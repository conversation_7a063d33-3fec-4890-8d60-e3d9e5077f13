"use client";
import React, { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Article, SearchResponse } from "@/sanity/queries/search";
import Link from "next/link";
import Image from "next/image";
import Button from "@/components/ui/Button";
import { usePagination } from "@/utils/usePagination";
import { Trending } from "@/components/HomePage/Trending";
import { getArticleUrl, getMediaImage } from "@/utils/utils";
import "react-datepicker/dist/react-datepicker.css";
import { truncateTitle, truncateText } from "@/utils/utils";
import { Article as TrendingArticle } from "@/sanity/queries/Trending/trending";
import DueDateDropdown, {
  DateRangeOption,
} from "@/components/ui/DateRangePicker";
import AuthorsList from "@/components/Author/AuthorsList";

interface SearchClientProps {
  latestNews: SearchResponse;
  generalTrendingArticles: TrendingArticle[];
}

const SearchClient: React.FC<SearchClientProps> = ({
  latestNews,
  generalTrendingArticles,
}) => {
  const itemsPerPage = 9;
  const router = useRouter();
  const queryParams = useSearchParams();
  const initialSearchQuery = queryParams?.get("q") || "";

  const initialStart = queryParams?.get("startDate");
  const initialEnd = queryParams?.get("endDate");

  const initialDateRange =
    initialStart && initialEnd
      ? {
          startDate: new Date(initialStart).toISOString(),
          endDate: new Date(initialEnd).toISOString(),
          key: "selection",
        }
      : undefined;

  const [dateRange, setDateRange] = useState<DateRangeOption | undefined>(
    initialDateRange,
  );
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const [isSearching, setIsSearching] = useState(false);
  const [totalCount, setTotalCount] = useState(latestNews.articlesCount);
  const [searchTitle, setSearchTitle] = useState("");
  const initialSort = queryParams?.get("sort") || "newest";
  const [sortOrder, setSortOrder] = useState(initialSort);

  const {
    items: articles,
    isLoading,
    hasMore,
    loadMore,
    resetPagination,
    setItems,
  } = usePagination<Article>({
    initialItems: latestNews.articles,
    itemsPerPage,
    totalCount: latestNews.articlesCount,
    apiUrl: `/api/search`,
    extraParams: {
      searchQuery,
      sort: sortOrder,
      ...(dateRange?.startDate
        ? { startDate: `${dateRange.startDate.split("T")[0]}` }
        : {}),
      ...(dateRange?.endDate
        ? { endDate: `${dateRange.endDate.split("T")[0]}` }
        : {}),
    },
  });

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    const sanitizedQuery = searchQuery.replace(/[^a-zA-Z0-9 ]/g, "");
    if (!sanitizedQuery.trim()) return;

    setSearchQuery(sanitizedQuery); // ← updates input to reflect cleaned query
    setSearchTitle(truncateText(sanitizedQuery, 30, "..."));
    const formattedStart = dateRange?.startDate
      ? dateRange.startDate.split("T")[0]
      : "";
    const formattedEnd = dateRange?.endDate
      ? dateRange.endDate.split("T")[0]
      : "";
    const queryParams = new URLSearchParams();
    queryParams.set("q", sanitizedQuery);
    queryParams.set("sort", sortOrder);
    if (formattedStart) queryParams.set("startDate", formattedStart);
    if (formattedEnd) queryParams.set("endDate", formattedEnd);
    router.push(`?${queryParams.toString()}`);

    resetPagination();
    setItems([]);
    setIsSearching(true);

    try {
      const response = await fetch(
        `/api/search?searchQuery=${encodeURIComponent(sanitizedQuery)}&page=1&pageSize=${itemsPerPage}&sort=${sortOrder}` +
          (formattedStart
            ? `&startDate=${encodeURIComponent(formattedStart)}`
            : "") +
          (formattedEnd ? `&endDate=${encodeURIComponent(formattedEnd)}` : ""),
      );

      const data = await response.json();
      setItems(data.data.items);
      setTotalCount(data.data.totalCount);
    } catch (error) {
      console.error("Error fetching search results:", error);
    } finally {
      setIsSearching(false);
    }
  };

  useEffect(() => {
    if (initialSearchQuery) {
      setSearchTitle(truncateText(initialSearchQuery, 30, "..."));
    }
  }, [initialSearchQuery]);

  const parseDate = (dateStr: string): Date => {
    const [year, month, day] = dateStr.split("-").map(Number);
    return new Date(year, month - 1, day);
  };

  const previousParamsRef = useRef({
    q: initialSearchQuery,
    sort: initialSort,
    startDate: initialStart,
    endDate: initialEnd,
  });

  useEffect(() => {
    if (!hasLoadedOnce) {
      setHasLoadedOnce(true);
      return;
    }
    const newQuery = queryParams?.get("q") || "";
    const newSort = queryParams?.get("sort") || "newest";
    const newStart = queryParams?.get("startDate");
    const newEnd = queryParams?.get("endDate");
    const newDateRange =
      newStart && newEnd
        ? {
            startDate: parseDate(newStart).toISOString(),
            endDate: parseDate(newEnd).toISOString(),
            key: "selection",
          }
        : undefined;
    const prev = previousParamsRef.current;

    const shouldRefetch =
      newQuery !== prev.q ||
      newSort !== prev.sort ||
      newStart !== prev.startDate ||
      newEnd !== prev.endDate;

    if (!shouldRefetch) return;

    previousParamsRef.current = {
      q: newQuery,
      sort: newSort,
      startDate: newStart,
      endDate: newEnd,
    };

    setSearchQuery(newQuery);
    setSortOrder(newSort);
    setDateRange(newDateRange);
    resetPagination();
    setItems([]);
    setIsSearching(true);

    const formattedStart = newStart || "";
    const formattedEnd = newEnd || "";
    const fetchNewResults = async () => {
      try {
        const response = await fetch(
          `/api/search?searchQuery=${encodeURIComponent(newQuery)}&page=1&pageSize=${itemsPerPage}&sort=${newSort}` +
            (formattedStart
              ? `&startDate=${encodeURIComponent(formattedStart)}`
              : "") +
            (formattedEnd
              ? `&endDate=${encodeURIComponent(formattedEnd)}`
              : ""),
        );
        const data = await response.json();
        setItems(data.data.items);
        setTotalCount(data.data.totalCount);
        setSearchTitle(truncateText(newQuery, 30, "..."));
      } catch (error) {
        console.error("Error fetching search results:", error);
      } finally {
        setIsSearching(false);
      }
    };

    fetchNewResults();
  }, [queryParams]);

  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const isFetchingRef = useRef(false);
  useEffect(() => {
    if (!hasMore || isLoading || isSearching) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const firstEntry = entries[0];
        if (firstEntry?.isIntersecting && !isFetchingRef.current) {
          isFetchingRef.current = true;
          loadMore().finally(() => {
            isFetchingRef.current = false;
          });
        }
      },
      { rootMargin: "100px" },
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [hasMore, isLoading, isSearching, loadMore]);

  return (
    <>
      <div className="bg-grey-100">
        <div className="container mx-auto px-4 h-auto">
          <div className="w-full max-w-2xl py-6 mx-auto text-center">
            <h2 className="text-[18px] font-bold mb-4 text-left">SEARCH</h2>
            <form
              onSubmit={handleSearch}
              className="w-full max-w-2xl flex flex-wrap items-start gap-3 md:flex-nowrap"
            >
              {/* Input + Search Button Container */}
              <div className="flex flex-grow items-center border border-grey-200 rounded-xl overflow-hidden min-w-0 h-[46px]">
                <input
                  className="py-[11px] px-4 flex-grow min-w-0 rounded-xl rounded-e-none outline-none h-[46px]"
                  type="text"
                  placeholder="Search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Button
                  type="submit"
                  variant="primaryBlack"
                  className="hidden sm:flex flex-shrink-0 text-white w-12 h-[46px] !rounded-xl !rounded-s-none"
                  as="icon"
                  iconColor="white"
                  label="Perform Search"
                  iconName={isSearching ? "loading" : "searchWhite"}
                  iconClass={isSearching ? "animate-spin" : ""}
                />
              </div>

              {/* DatePicker Input */}
              <div className="w-full md:w-auto">
                <DueDateDropdown
                  value={dateRange}
                  onChange={(selected) => {
                    setDateRange(selected);
                    const queryParams = new URLSearchParams(
                      window.location.search,
                    );
                    if (selected.startDate)
                      queryParams.set(
                        "startDate",
                        selected.startDate.split("T")[0],
                      );
                    if (selected.endDate)
                      queryParams.set(
                        "endDate",
                        selected.endDate.split("T")[0],
                      );
                    router.push(`?${queryParams.toString()}`);
                  }}
                  onClose={() => {}}
                />
              </div>
              <div className="w-full md:w-44">
                <select
                  className="h-[46px] appearance-none border border-grey-200 px-4 pr-8 rounded-xl w-full bg-white text-left"
                  value={sortOrder}
                  onChange={(e) => {
                    const newSort = e.target.value;
                    setSortOrder(newSort);
                    const currentParams = new URLSearchParams(
                      window.location.search,
                    );
                    currentParams.set("sort", newSort);
                    const newUrl = `?${currentParams.toString()}`;
                    router.replace(newUrl);
                  }}
                  style={{
                    WebkitAppearance: "none",
                    MozAppearance: "none",
                    appearance: "none",
                    backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' stroke='black' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='6 9 12 15 18 9' /%3E%3C/svg%3E")`,
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 0.5rem center",
                    backgroundSize: "1rem",
                  }}
                >
                  <option value="newest">Sort by Newest</option>
                  <option value="oldest">Sort by Oldest</option>
                </select>
              </div>
              <div className="w-full sm:w-36 min-w-0 sm:hidden">
                <Button
                  type="submit"
                  variant="primaryBlack"
                  className="w-full h-[44px] flex items-center justify-center gap-2"
                  iconName={isSearching ? "loading" : "searchWhite"}
                  iconPosition="left"
                  iconClass={isSearching ? "animate-spin" : ""}
                >
                  {!isSearching && "Search"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-4 pt-12">
        <h1 className="font-titles font-semibold text-[32px] xl:text-[52px] leading-10 xl:leading-[56px]">
          &quot;{searchTitle}&quot;
        </h1>
        <h2 className="text-left text-LG font-bold pt-2">
          {isSearching ? (
            <p>Searching...</p>
          ) : (
            totalCount > 0 && (
              <p>We found {totalCount} articles matching your search</p>
            )
          )}
        </h2>
        <div className="lg:grid lg:grid-cols-12 lg:gap-4">
          {/* Left Column */}
          <div className="lg:col-span-9 pt-8">
            {totalCount === 0 && !isSearching ? (
              <div className="flex flex-col items-center text-center py-12">
                <Image
                  src="/images/404/404.png"
                  alt="Page not found"
                  className="w-[200px] h-[160px] mb-6 sm:w-[307px] sm:h-[247px] max-w-full"
                  width={307}
                  height={247}
                />
                <h2 className="font-semibold font-titles text-[18px] sm:text-[24px] mb-2">
                  <b>
                    No results found. Try refining your search terms or
                    selecting a different date.
                  </b>
                </h2>
              </div>
            ) : (
              <>
                <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {articles?.map((article, index) => (
                    <div
                      key={article.slug || `article-${index}`}
                      className="flex flex-col gap-2 pb-6 border-b border-b-grey-200 md:border-none"
                    >
                      {article.media && (
                        <Link
                          href={getArticleUrl(
                            article.category?.slug,
                            article.slug,
                            article.subcategory?.slug,
                          )}
                          className="relative w-full aspect-[16/9] transition-opacity hover:opacity-85"
                        >
                          <Image
                            src={getMediaImage(article.media)}
                            alt={article.title}
                            fill
                            className="object-cover rounded-2xl"
                          />
                        </Link>
                      )}
                      <p className="uppercase text-[13px]">
                        <Link
                          href={`/${article.category.slug}`}
                          className="hover-underline-animation"
                        >
                          {article.category.title}
                        </Link>
                      </p>
                      <h2 className="font-semibold font-titles text-[20px] leading-[26px]">
                        <Link
                          href={getArticleUrl(
                            article.category?.slug,
                            article.slug,
                            article.subcategory?.slug,
                          )}
                          className="hover-underline-animation"
                        >
                          {truncateTitle(article.title)}
                        </Link>
                      </h2>

                      <div className="text-[12px] -mt-3">
                        {article.authors && (
                          <AuthorsList
                            authors={article.authors}
                            publishedDate={article.publishedDate}
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </section>
                {hasMore && !isSearching && (
                  <div ref={loadMoreRef} className="h-10" />
                )}
              </>
            )}
          </div>

          {/* Right Column */}
          <div className="col-span-3">
            <div className="sticky top-24">
              <Trending
                oneColumn={true}
                trendingArticles={generalTrendingArticles}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SearchClient;
