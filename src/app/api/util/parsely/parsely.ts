import { getSlugFromArticleUrl } from "@/app/api/util";

export const fetchTrendingArticles = async ({
  limit,
  categorySlug,
  subcategorySlug,
  periodStart,
  periodEnd,
}: {
  limit?: number;
  categorySlug?: string | null;
  subcategorySlug?: string | null;
  periodStart?: string;
  periodEnd?: string;
}) => {
  const parselyURL = process.env.PARSELY_API_URL;
  const apiKey = process.env.PARSELY_API_KEY;
  const apiSecret = process.env.PARSELY_API_SECRET;

  if (!parselyURL || !apiKey || !apiSecret) {
    console.error("Missing environment variables for Parsely API");
    return [];
  }

  try {
    const params = new URLSearchParams({
      period_start: periodStart ?? "24h",
      limit: limit?.toString() ?? "5",
      apikey: apiKey,
      secret: apiSecret,
      tag: "dailyStory",
    });

    if (periodEnd) {
      params.append("period_end", periodEnd);
    }

    if (categorySlug) {
      params.append("section", categorySlug);
    }

    if (subcategorySlug) {
      params.append("tag", subcategorySlug);
    }

    const parselyAnalytics = await fetch(
      `${parselyURL}/analytics/posts?${params.toString()}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        next: {
          revalidate: 60,
        },
      },
    );

    const parselyAnalyticsData = await parselyAnalytics.json();

    if (!parselyAnalyticsData || !parselyAnalyticsData.success) {
      console.error(
        `Error fetching Parsely Analytics data with limit: ${limit}, categorySlug: ${categorySlug}, subcategorySlug: ${subcategorySlug}, periodStart: ${periodStart}, periodEnd: ${periodEnd}`,
        parselyAnalyticsData,
      );
      return [];
    }

    return parselyAnalyticsData.data
      .map((article: { url: string }) => ({
        slug: getSlugFromArticleUrl(article.url),
      }))
      .filter((article: { slug: string | null }) => article.slug !== null);
  } catch (error) {
    console.error("Error fetching trending articles:", error);
    return [];
  }
};
