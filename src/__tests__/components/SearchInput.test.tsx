import { render, screen, fireEvent } from "@testing-library/react";
import SearchInput from "@/components/ui/Form/SearchInput";

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock("@/components/ui/Button", () => ({
  __esModule: true,
  default: ({ onClick }: { onClick: () => void }) => (
    <button data-testid="mock-button" onClick={onClick}>
      Search
    </button>
  ),
}));

describe("SearchInput Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the input field and button", () => {
    render(<SearchInput />);

    const inputElement = screen.getByRole("textbox");
    expect(inputElement).toBeInTheDocument();

    const buttonElement = screen.getByTestId("mock-button");
    expect(buttonElement).toBeInTheDocument();
  });

  it("allows typing in the input field", () => {
    render(<SearchInput />);

    const inputElement = screen.getByRole("textbox");
    fireEvent.change(inputElement, { target: { value: "search term" } });

    expect(inputElement).toHaveValue("search term");
  });
});
