import { render, screen } from "@testing-library/react";
import OurFaith, { generateMetadata } from "@/app/our-faith/page";
import { sanityFetch } from "@/sanity/client";
import { OurFaithPageType } from "@/sanity/queries/OutFaithPage/ourFaith";

jest.mock("@/sanity/client", () => ({
  sanityFetch: jest.fn(),
}));

// Mock of next/server to verify HTTP status
jest.mock("next/server", () => {
  const originalModule = jest.requireActual("next/server");
  return {
    ...originalModule,
    NextResponse: {
      ...originalModule.NextResponse,
      json: jest
        .fn()
        .mockImplementation((data: unknown, options?: { status?: number }) => ({
          status: options?.status || 200,
          data,
        })),
    },
  };
});

jest.mock("@/components/OurFaith/FaithCard", () => {
  return function MockFaithCard(props: {
    title: string;
    description: string;
    imageSrc: string;
    link: string;
  }) {
    return <div data-testid="mock-faith-card">{props.title}</div>;
  };
});

jest.mock("@/components/Layout/PageHeader", () => {
  return function MockPageHeader({
    title,
    description,
  }: {
    title: string;
    description: string;
  }) {
    return (
      <div data-testid="mock-page-header">
        <h1>{title}</h1>
        <p>{description}</p>
      </div>
    );
  };
});

describe("OurFaith Page", () => {
  const mockFaithCards = [
    {
      id: "1",
      title: "Prayer",
      description: "Daily prayers for Catholics",
      imageUrl: "/test-image.jpg",
      link: "/prayers",
      published: true,
    },
    {
      id: "2",
      title: "Saints",
      description: "Learn about Catholic saints",
      imageUrl: "/saint-image.jpg",
      link: "/saints",
      published: true,
    },
  ];

  const mockPageData: OurFaithPageType = {
    id: "our-faith-page",
    title: "Our Catholic Faith",
    description: "Explore Catholic teachings and traditions",
    faithCards: mockFaithCards,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders the page with faith cards when data is fetched successfully", async () => {
    (sanityFetch as jest.Mock).mockResolvedValue(mockPageData);

    render(await OurFaith());

    expect(screen.getByTestId("mock-page-header")).toBeInTheDocument();
    expect(screen.getByText("Our Catholic Faith")).toBeInTheDocument();
    expect(
      screen.getByText("Explore Catholic teachings and traditions"),
    ).toBeInTheDocument();
    // We verify that the card titles are displayed (using getAllByText because they appear duplicated in mobile and desktop views)
    const prayerCards = screen.getAllByText("Prayer");
    const saintsCards = screen.getAllByText("Saints");
    expect(prayerCards.length).toBeGreaterThan(0);
    expect(saintsCards.length).toBeGreaterThan(0);
  });

  test("renders error message when data fetching fails", async () => {
    (sanityFetch as jest.Mock).mockRejectedValue(
      new Error("Failed to fetch data"),
    );

    render(await OurFaith());

    expect(screen.getByText("Content loading issue")).toBeInTheDocument();
    expect(
      screen.getByText(
        /We're experiencing difficulties retrieving the latest content/i,
      ),
    ).toBeInTheDocument();
  });

  test("renders no cards message when no faith cards are available", async () => {
    (sanityFetch as jest.Mock).mockResolvedValue({
      ...mockPageData,
      faithCards: [],
    });

    render(await OurFaith());

    expect(screen.getByText("No faith cards available")).toBeInTheDocument();
  });
  describe("generateMetadata", () => {
    test("returns the correct metadata when data is fetched successfully", async () => {
      (sanityFetch as jest.Mock).mockResolvedValue(mockPageData);

      const metadata = await generateMetadata();

      expect(metadata).toEqual({
        title: "Our Catholic Faith | EWTN News",
        description: "Explore Catholic teachings and traditions",
        openGraph: {
          title: "Our Catholic Faith | EWTN News",
          description: "Explore Catholic teachings and traditions",
          images: [
            {
              url: "/test-image.jpg",
              width: 1200,
              height: 630,
              alt: "Our Catholic Faith",
            },
          ],
          locale: "en_US",
          type: "website",
          siteName: "EWTN News",
        },
        twitter: {
          card: "summary_large_image",
          title: "Our Catholic Faith | EWTN News",
          description: "Explore Catholic teachings and traditions",
          images: ["/test-image.jpg"],
          creator: "@EWTNNews",
        },
      });
    });

    test("returns default metadata when data fetching fails", async () => {
      (sanityFetch as jest.Mock).mockRejectedValue(
        new Error("Failed to fetch data"),
      );

      const metadata = await generateMetadata();

      expect(metadata).toEqual({
        title: "Our Faith | EWTN News",
        description:
          "Discover the prayers, readings, saints, and spiritual tools that help Catholics live and grow in their faith every day.",
        openGraph: {
          title: "Our Faith | EWTN News",
          description:
            "Discover the prayers, readings, saints, and spiritual tools that help Catholics live and grow in their faith every day.",
          images: ["/ewtn_logo.png"],
          locale: "en_US",
          type: "website",
          siteName: "EWTN News",
        },
        twitter: {
          card: "summary_large_image",
          title: "Our Faith | EWTN News",
          description:
            "Discover the prayers, readings, saints, and spiritual tools that help Catholics live and grow in their faith every day.",
          images: ["/ewtn_logo.png"],
          creator: "@EWTNNews",
        },
      });
    });

    test("correctly includes SEO metadata from Sanity CMS", async () => {
      // Specific data to test SEO metadata
      const seoMockData: OurFaithPageType = {
        id: "seo-test",
        title: "SEO Optimized Faith Page",
        description:
          "This page is optimized for search engines with rich metadata",
        faithCards: [
          {
            id: "seo-card",
            title: "SEO Card",
            description: "Card with rich media for SEO",
            imageUrl: "/seo-optimized-image.jpg",
            link: "/seo-test",
            published: true,
          },
        ],
      };

      (sanityFetch as jest.Mock).mockResolvedValue(seoMockData);

      const metadata = await generateMetadata();

      // Verify that metadata includes correct SEO information
      expect(metadata.title).toBe("SEO Optimized Faith Page | EWTN News");
      expect(metadata.description).toBe(
        "This page is optimized for search engines with rich metadata",
      );

      // Verify that the object contains the expected properties
      expect(metadata).toHaveProperty("openGraph");
      expect(metadata).toHaveProperty("twitter");

      // Verify the basic structure of Open Graph
      const openGraph = metadata.openGraph as {
        title: string;
        description: string;
        images:
          | Array<{ url: string; width: number; height: number; alt: string }>
          | string[];
        locale: string;
        type: string;
        siteName: string;
      };
      expect(openGraph.title).toBe("SEO Optimized Faith Page | EWTN News");
      expect(openGraph.description).toBe(
        "This page is optimized for search engines with rich metadata",
      );
      expect(openGraph.images).toBeDefined();

      // Verify Twitter Card
      const twitter = metadata.twitter as {
        card: string;
        title: string;
        description: string;
        images: string[];
        creator: string;
      };
      expect(twitter.title).toBe("SEO Optimized Faith Page | EWTN News");
      expect(twitter.description).toBe(
        "This page is optimized for search engines with rich metadata",
      );
      expect(twitter.images).toBeDefined();
    });
  });

  test("renders without crashing when data is available", async () => {
    // Configure the mock for sanityFetch
    (sanityFetch as jest.Mock).mockResolvedValue(mockPageData);

    // Render the page
    const result = await OurFaith();

    // Verify that the component renders correctly
    expect(result).toBeDefined();

    // Render in virtual DOM
    const { container } = render(result);

    // Verify that it contains main elements
    expect(container.querySelector(".w-full")).toBeInTheDocument();
    expect(container.querySelector(".container")).toBeInTheDocument();
  });

  test("sanity data is properly integrated into the component structure", async () => {
    // Configure specific data for this test
    const detailedMockData: OurFaithPageType = {
      id: "detailed-faith-page",
      title: "Detailed Faith Page",
      description: "Testing detailed integration with Sanity data",
      faithCards: [
        {
          id: "test-1",
          title: "Rosary",
          description: "How to pray the Rosary",
          imageUrl: "/rosary-image.jpg",
          link: "/rosary",
          published: true,
        },
        {
          id: "test-2",
          title: "Daily Readings",
          description: "Today's scripture readings",
          imageUrl: "/readings-image.jpg",
          link: "/readings",
          published: true,
        },
      ],
    };

    (sanityFetch as jest.Mock).mockResolvedValue(detailedMockData);

    render(await OurFaith());

    // Verify that specific Sanity data appears in the UI
    expect(screen.getByText("Detailed Faith Page")).toBeInTheDocument();
    expect(
      screen.getByText("Testing detailed integration with Sanity data"),
    ).toBeInTheDocument();

    // Verify that cards are rendered with the correct information
    expect(screen.getAllByText("Rosary")[0]).toBeInTheDocument();
    expect(screen.getAllByText("Daily Readings")[0]).toBeInTheDocument();

    // Verify that sanityFetch was called with the correct parameters
    expect(sanityFetch).toHaveBeenCalledTimes(1);
  });
});
