import ArticleMediaDisplay from "@/components/DailyStory/ArticleMediaDisplay";
import { Media } from "@/sanity/queries/dailyStory";
import { useMemo } from "react";

export const ImageCarousel = ({ value }: { value: ImageCarouselBlock }) => {
  const memoizedImages = useMemo(() => value.images, [value.images]);
  return (
    <div className="mt-7 pb-1">
      <ArticleMediaDisplay
        title="Image Carousel"
        articleMedia={memoizedImages}
      />
    </div>
  );
};

export interface ImageCarouselBlock {
  _type: "imageCarousel";
  _key: string;
  images: Media[];
}
