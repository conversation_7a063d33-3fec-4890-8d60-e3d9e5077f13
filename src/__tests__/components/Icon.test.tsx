// components/Icon/__tests__/Icon.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { Icon } from "@/components/Icon";

describe("Icon Component", () => {
  describe("Material Icons", () => {
    test("renders material icon correctly", () => {
      render(<Icon icon="menu" />);

      const iconElement = screen.getByText("list");
      expect(iconElement).toBeInTheDocument();
      expect(iconElement).toHaveClass("material-symbols-rounded");
    });

    test("applies custom className to material icon", () => {
      const customClass = "text-red-500";
      render(<Icon icon="menu" className={customClass} />);

      const iconElement = screen.getByText("list");
      expect(iconElement).toHaveClass("material-symbols-rounded", customClass);
    });
  });

  describe("SVG Icons", () => {
    test("renders search icon as SVG", () => {
      render(<Icon icon="search" />);

      const svgElement = document.querySelector("svg");
      expect(svgElement).toBeInTheDocument();
    });

    test("applies custom className to SVG container", () => {
      const customClass = "text-blue-500";
      render(<Icon icon="search" className={customClass} />);

      const containerElement = screen.getByTestId("search");
      expect(containerElement).toHaveClass(customClass);
    });

    test("renders social media icons correctly", () => {
      const { rerender } = render(<Icon icon="facebook" />);
      expect(document.querySelector("svg")).toBeInTheDocument();

      rerender(<Icon icon="x" />);
      expect(document.querySelector("svg")).toBeInTheDocument();
    });
  });
});
