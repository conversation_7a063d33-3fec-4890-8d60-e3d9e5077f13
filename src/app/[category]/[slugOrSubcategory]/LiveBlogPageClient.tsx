"use client";

import LiveBlogBreakingNews from "@/components/LiveBlog/LiveBlogBreakingNews";
import { LiveBlogArticle } from "@/sanity/queries/liveBlog";
import LiveUpdates from "@/components/LiveBlog/LiveUpdates";

interface LiveBlogPageProps {
  liveBlogArticle: NonNullable<LiveBlogArticle>;
}

export default function LiveBlogPageClient({
  liveBlogArticle,
}: LiveBlogPageProps) {
  return (
    <div>
      <LiveBlogBreakingNews liveBlogArticle={liveBlogArticle} />
      <LiveUpdates liveBlogArticle={liveBlogArticle} />
    </div>
  );
}
