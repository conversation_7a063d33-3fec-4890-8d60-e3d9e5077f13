import { useId } from "react";
import { InputProps, InputSize } from "./types";

const Input = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  disabled,
  size = "sm",
  type = "text",
  ...props
}: InputProps) => {
  const id = useId();
  const inputId = `input-${id}`;

  const sizeClasses: Record<InputSize, string> = {
    xs: "text-xs py-2 px-3",
    sm: "text-[16px] py-4 px-4",
  };

  return (
    <div>
      {label && (
        <label htmlFor={inputId} className="block text-[16px] mb-3">
          {label}
        </label>
      )}

      <div
        className={`
          relative bg-white w-full border rounded-xl
          ${disabled ? "cursor-not-allowed opacity-70" : ""}
          ${error ? "border-red-700" : "border-grey-200"}
        `}
      >
        <input
          id={inputId}
          type={type}
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          placeholder={placeholder}
          className={`
            w-full appearance-none relative z-10 leading-none rounded-xl
            focus:outline-none
            ${error ? "bg-red-100" : "bg-white"}
            ${sizeClasses[size]}
          `}
          {...props}
          role="textbox"
          aria-label={label}
        />
      </div>

      {error && <p className="mt-1 text-red-700 text-sm">{error}</p>}
    </div>
  );
};

export default Input;
