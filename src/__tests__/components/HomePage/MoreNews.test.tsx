import { render, screen } from "@testing-library/react";
import MoreNewsSection, {
  LargerArticleCard,
} from "@/components/HomePage/MoreNewsSection";
import { Article } from "@/sanity/queries/Trending/trending";

// Mock components
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: { src: string; alt: string; className?: string }) => (
    <img {...props} />
  ),
}));

jest.mock("@/components/Icon", () => ({
  Icon: ({ icon }: { icon: string }) => <span data-testid={`icon-${icon}`} />,
}));

jest.mock("@/components/Timestamp", () => ({
  __esModule: true,
  default: ({ date }: { date: string }) => (
    <span data-testid="timestamp">{date}</span>
  ),
}));

const mockArticle: Article = {
  id: "1",
  title: "Test Article",
  truncatedTitle: "Test Article",
  excerpt: "This is an excerpt",
  slug: "test-article",
  publishedDate: "2024-02-14",
  updated_at: "2024-02-15",
  created_at: "2024-02-14",
  category: {
    id: "1",
    title: "Test Category",
    slug: "test-category",
    description: "Test category description",
  },
  subcategory: {
    id: "10",
    title: "Test Subcategory",
    slug: "test-subcategory",
  },
  tags: [{ id: "id", title: "tag-1", slug: "slug1" }],
  authors: [
    {
      name: "Author One",
      slug: "author-one",
      image: { resource_type: "image", type: "image", url: "author.jpg" },
    },
  ],
  media: [
    {
      url: "test-image.jpg",
      type: "image",
      text: "Test image",
    },
  ],
};
describe("ArticleCard", () => {
  it("renders category link correctly", () => {
    render(<LargerArticleCard article={mockArticle} />);

    const categoryLink = screen.getByRole("link", {
      name: mockArticle.category.title,
    });

    expect(categoryLink).toHaveAttribute(
      "href",
      `/${mockArticle.category.slug}`,
    );
  });

  it("shows gallery icon when article has multiple media items", () => {
    const articleWithMultipleMedia = {
      ...mockArticle,
      media: [
        { url: "img1.jpg", type: "image" },
        { url: "img2.jpg", type: "image" },
      ],
    };

    render(<LargerArticleCard article={articleWithMultipleMedia} />);
    expect(screen.getByTestId("icon-gallery")).toBeInTheDocument();
  });

  it("shows video icon when article has video media", () => {
    const articleWithVideo = {
      ...mockArticle,
      media: [{ url: "video.mp4", type: "video" }],
    };

    render(<LargerArticleCard article={articleWithVideo} />);
    expect(screen.getByTestId("icon-play")).toBeInTheDocument();
  });

  it("handles article without media", () => {
    const articleWithoutMedia = { ...mockArticle, media: [] };
    render(<LargerArticleCard article={articleWithoutMedia} />);

    expect(screen.queryByRole("img")).not.toBeInTheDocument();
  });
});

describe("MoreNewsSection", () => {
  const mockArticles = [mockArticle, { ...mockArticle, id: "2" }];

  it("renders section title and description", () => {
    render(<MoreNewsSection articles={mockArticles} />);

    expect(screen.getByText("More News")).toBeInTheDocument();
    expect(
      screen.getByText("A Deeper Look at the Top Stories"),
    ).toBeInTheDocument();
  });

  it("renders correct number of ArticleCards", () => {
    render(<MoreNewsSection articles={mockArticles} />);

    const articles = screen.getAllByRole("article");
    expect(articles).toHaveLength(mockArticles.length);
  });

  it("handles empty articles array", () => {
    render(<MoreNewsSection articles={[]} />);

    expect(screen.queryByRole("article")).not.toBeInTheDocument();
  });
});

describe("getMediaImage function", () => {
  it("returns original URL for image type", () => {
    const imageMedia = { type: "image", url: "test.jpg" };
    const article = { ...mockArticle, media: [imageMedia] };

    render(<LargerArticleCard article={article} />);

    const image = screen.getByRole("img");
    expect(image).toHaveAttribute("src", "test.jpg");
  });

  it("converts video URL to jpg for video type", () => {
    const videoMedia = { type: "video", url: "test.mp4" };
    const article = { ...mockArticle, media: [videoMedia] };

    render(<LargerArticleCard article={article} />);

    const image = screen.getByRole("img");
    expect(image).toHaveAttribute("src", "test.jpg");
  });
});
