import { sanityFetch } from "@/sanity/live";
import {
  moreSearchQueryAsc,
  moreSearchQueryDesc,
} from "@/sanity/queries/search";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const pageSize = parseInt(url.searchParams.get("pageSize") || "9", 10);
    const searchQuery = url.searchParams.get("searchQuery") || "";
    const rawStart = url.searchParams.get("startDate");
    const rawEnd = url.searchParams.get("endDate");

    const startDate = rawStart ? `${rawStart}T00:00:00Z` : null;
    const endDate = rawEnd ? `${rawEnd}T23:59:59Z` : null;
    const sortParam = url.searchParams.get("sort") || "newest";
    const sort = sortParam === "oldest" ? "asc" : "desc";

    const validPage = isNaN(page) ? 1 : page;
    const validPageSize = isNaN(pageSize) ? 9 : pageSize;

    const start = (validPage - 1) * validPageSize;
    const end = start + validPageSize;

    const query = sort === "asc" ? moreSearchQueryAsc : moreSearchQueryDesc;
    const response = await sanityFetch({
      query,
      params: {
        start,
        end,
        searchQuery,
        startDate,
        endDate,
        sort,
      },
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching articles:", error);

    return NextResponse.json(
      {
        error: "Error fetching articles",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
