export const getDaysInMonth = (month: number, year: number) => {
  return new Date(year, month, 0).getDate();
};

export const getCurrentYear = () => new Date().getFullYear();

export const generateYearOptions = () => {
  const currentYear = getCurrentYear();
  const years = [];
  for (let year = currentYear; year >= 1900; year--) {
    years.push({ value: year.toString(), label: year.toString() });
  }
  return years;
};
