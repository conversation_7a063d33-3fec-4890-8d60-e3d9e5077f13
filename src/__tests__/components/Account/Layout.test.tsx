import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import Layout from "@/components/Account/Layout";

jest.mock("@/components/Account/Sidebar/UserProfile", () => {
  const MockUserProfile = () => (
    <div>
      <span>User Profile</span>
    </div>
  );
  MockUserProfile.displayName = "MockUserProfile";
  return MockUserProfile;
});

describe("Layout", () => {
  test("renders left column with menu items", () => {
    render(
      <Layout>
        <Layout.Left>
          <div>Left Column Content</div>
        </Layout.Left>
      </Layout>,
    );

    expect(
      screen.getByRole("heading", { name: "My account" }),
    ).toBeInTheDocument();
    expect(screen.getByText("My Interests")).toBeInTheDocument();
    expect(screen.getByText("Saved Articles")).toBeInTheDocument();
    expect(screen.getByText("Get in Touch")).toBeInTheDocument();
    expect(screen.getByText("Left Column Content")).toBeInTheDocument();
  });

  test("renders main content", () => {
    render(
      <Layout>
        <Layout.Main>
          <div>Main Content</div>
        </Layout.Main>
      </Layout>,
    );

    expect(screen.getByText("Main Content")).toBeInTheDocument();
  });

  // Right column removed for the time being

  // test("renders right column", () => {
  //   render(
  //     <Layout>
  //       <Layout.Right>
  //         <div>Right Column Content</div>
  //       </Layout.Right>
  //     </Layout>,
  //   );

  //   expect(screen.getByText("Right Column Content")).toBeInTheDocument();
  // });

  test("renders all columns", () => {
    render(
      <Layout>
        <Layout.Left>
          <div>Left Column Content</div>
        </Layout.Left>
        <Layout.Main>
          <div>Main Content</div>
        </Layout.Main>
        <Layout.Right>
          <div>Right Column Content</div>
        </Layout.Right>
      </Layout>,
    );

    expect(screen.getByText("Left Column Content")).toBeInTheDocument();
    expect(screen.getByText("Main Content")).toBeInTheDocument();
    //expect(screen.getByText("Right Column Content")).toBeInTheDocument();
  });
});
