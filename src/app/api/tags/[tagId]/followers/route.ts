import { NextRequest } from "next/server";
import {
  addFollower,
  getFollowerCount,
  getUserFollows,
  removeFollower,
} from "@/app/api/services/tags/followers";
import {
  unauthorizedResponse,
  badRequestResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { getAuth0UserId, extractParam } from "@/app/api/util";

/**
 * Handles the POST request to follow an tag.
 *
 * @param params.tagId - The ID of the tag to follow.
 * @returns A JSON response containing a success or error message.
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ tagId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const tagId = await extractParam(params, "tagId");
    if (!tagId) {
      return badRequestResponse("Missing tagId in request params");
    }

    await addFollower(auth0UserId, tagId);
    return successResponse("Author followed", 201);
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the GET request to retrieve follower information for a specific tag.
 *
 * @param params.tagId - The ID of the tag for which to retrieve follower information.
 * @returns A JSON response containing the follower count and whether the user follows the tag.
 *
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ tagId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    const tagId = await extractParam(params, "tagId");
    if (!tagId) {
      return badRequestResponse("Missing tagId in request params");
    }
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const [countData, userData] = await Promise.all([
      getFollowerCount(auth0UserId),
      getUserFollows(auth0UserId, tagId),
    ]);

    return successResponseWithData(
      {
        followerCount: countData?.follower_count || 0,
        userFollows: !!userData,
      },
      200,
    );
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the DELETE request to remove a saved tag.
 *
 * @param params.tagId - The ID of the tag.
 * @returns A JSON response containing a success or error message.
 *
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ tagId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const tagId = await extractParam(params, "tagId");
    if (!tagId) {
      return badRequestResponse("Missing tagId in request params");
    }

    await removeFollower(auth0UserId, tagId);
    return successResponse("Author follower removed", 200);
  } catch (error) {
    return errorResponse(error);
  }
}
