"use client";
import { useState, useRef, useEffect } from "react";
import Button, { ButtonProps } from "./../Button";
import { IconName } from "@/components/Icon";

export type DropdownProps = {
  options: Array<{ label: string; value: string | number }>;
  onOptionSelect: (value: string | number) => void;
  defaultValue?: string | number;
  buttonProps?: Omit<
    ButtonProps,
    "children" | "onClick" | "isDropdown" | "dropdownOpen"
  >;
  iconName?: IconName;
};

const Dropdown = ({
  options,
  onOptionSelect,
  defaultValue,
  buttonProps,
  iconName,
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string | number>(
    defaultValue ?? options[0]?.value,
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const handleOptionClick = (value: string | number) => {
    setSelectedValue(value);
    onOptionSelect(value);
    setIsOpen(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const selectedLabel =
    options.find((option) => option.value === selectedValue)?.label ||
    "Select an option";

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      {/* Dropdown Button */}
      <Button
        iconName={iconName}
        {...buttonProps}
        onClick={toggleDropdown}
        isDropdown={true}
        dropdownOpen={isOpen}
      >
        {selectedLabel}
      </Button>

      {/* Dropdown Menu */}
      {isOpen && (
        <ul className="absolute z-10 mt-2 w-56 bg-grey-100 rounded-md shadow-lg">
          {options.map((option) => (
            <li
              key={option.value}
              className="px-4 py-2 cursor-pointer hover:bg-gray-100"
              onClick={() => handleOptionClick(option.value)}
            >
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Dropdown;
