import Link from "next/link";

export default function CookiePolicyLink({
  variant = "default",
}: {
  variant?: "default" | "small";
}) {
  const iubendaPolicyId = process.env.NEXT_PUBLIC_IUBENDA_POLICY_ID;
  const baseClasses =
    "iubenda-white iubenda-noiframe iubenda-embed hover-underline-animation";
  const variantClasses =
    variant === "default" ? "text-gray-600 hover:text-gray-900" : "text-sm";
  return (
    <Link
      href={`https://www.iubenda.com/privacy-policy/${iubendaPolicyId}/cookie-policy`}
      className={`${baseClasses} ${variantClasses}`}
      title="Cookie Policy"
      target="_blank"
    >
      Cookie Policy
    </Link>
  );
}
