import { Media } from "@/sanity/queries/HomePage/categoryArticles";
import { toast } from "react-toastify";

const getMediaImage = function (media: Media) {
  if (media.type == "image") return media.url;

  return media.url?.replace(/\.[^/.]+$/, ".jpg");
};

const getFirstMediaImageUrl = function (media: Media[]) {
  if (!media) return;
  if (media.length == 0) return;

  const filteredMedia = media.filter((m) => m.type == "image");
  if (filteredMedia.length == 0) return;

  return filteredMedia[0].url;
};

const getArticleUrl = function (
  category: string,
  slug?: string,
  subcategory?: string,
) {
  if (subcategory) {
    return `/${category}/${subcategory}/${slug}`;
  }

  return `/${category}/${slug}`;
};

const truncateTitle = function (text: string, charLength = 100) {
  return truncateText(text, charLength, "...");
};

const truncateDescription = function (text: string) {
  return truncateText(text, 200, "...");
};

const sanitizeString = function (str: string) {
  return str.replace(
    /[\u200B-\u200D\uFEFF\u00A0\u2060\u2061\u2062\u2063\u2064]/g,
    "",
  );
};

const truncateText = function (
  text: string,
  maxLength: number,
  ellipsis: string = "...",
) {
  if (!text || text.length <= maxLength) {
    return text;
  }

  const sanitizedText = sanitizeString(text.trim());

  if (sanitizedText.length <= maxLength) {
    return sanitizedText;
  }

  const truncatedText = sanitizedText.substring(0, maxLength);

  const lastSpaceIndex = truncatedText.lastIndexOf(" ");

  if (lastSpaceIndex === -1) {
    return truncatedText + ellipsis;
  }

  let result = truncatedText.substring(0, lastSpaceIndex);

  result = result.replace(/[^\w\s]$/, "");

  result = result.trim();

  return result + ellipsis;
};

const slugToTitle = function (slug: string) {
  const title = slug
    .replace(/-/g, " ")
    .replace(/\b\w/g, (char) => char.toUpperCase())
    .replace(/\s+/g, " ")
    .trim();
  return title;
};

function throttle<T extends (...args: unknown[]) => void>(
  func: T,
  limit: number,
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  return function (this: unknown, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

const handleCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    toast.success("Link successfully copied to the clipboard!", {
      position: "top-right",
      autoClose: 2500,
      hideProgressBar: false,
      closeOnClick: false,
      pauseOnHover: false,
      draggable: true,
      progress: undefined,
      theme: "colored",
    });
  } catch (error) {
    console.error("Failed to copy:", error);
    toast.error("Failed to copy the link!");
  }
};

interface ImageConfig {
  src: string;
}

function imageLoaderUtility(config: ImageConfig) {
  if (!config.src.includes("/upload/")) {
    return config.src;
  }

  const [urlStart, urlEnd] = config.src.split("/upload/");
  const transformations = `c_thumb,g_face,h_200,w_200`;
  const new_url = `${urlStart}/upload/${transformations}/${urlEnd}`;
  return new_url;
}

export {
  getMediaImage,
  getFirstMediaImageUrl,
  getArticleUrl,
  truncateText,
  truncateTitle,
  truncateDescription,
  slugToTitle,
  throttle,
  handleCopy,
  imageLoaderUtility,
};
