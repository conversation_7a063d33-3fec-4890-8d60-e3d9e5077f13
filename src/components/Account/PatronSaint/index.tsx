"use client";
import { useState, useEffect, useRef } from "react";
import Select from "@/components/ui/Form/Select";
import { SelectOption } from "@/components/ui/Form/Select/types";
import { DateSelectionState, Saint } from "./types";
import { MONTHS } from "./constants";
import { getDaysInMonth, generateYearOptions } from "./utils";
import { Icon } from "@/components/Icon";
import Button from "@/components/ui/Button";
import { IMAGES_PATH } from "@/constants";
import Image from "next/image";

const PatronSaint = () => {
  const [date, setDate] = useState<DateSelectionState>({
    month: { value: null, error: false },
    day: { value: null, error: false },
    year: { value: null, error: false },
  });

  const [daysOptions, setDaysOptions] = useState<SelectOption[]>([]);

  const [showResult, setShowResult] = useState(false);

  const [scrollPosition, setScrollPosition] = useState<
    "top" | "bottom" | "middle"
  >("top");
  const descriptionRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const handleScroll = (element: HTMLElement) => {
      const { scrollTop, scrollHeight, clientHeight } = element;

      if (scrollTop < 15) {
        setScrollPosition("top");
      } else if (scrollTop + clientHeight >= scrollHeight - 16) {
        setScrollPosition("bottom");
      } else {
        setScrollPosition("middle");
      }
    };

    const element = descriptionRef.current;
    if (element) {
      element.addEventListener("scroll", () => handleScroll(element));
    }

    return () => {
      if (element) {
        element.removeEventListener("scroll", () => handleScroll(element));
      }
    };
  }, []);

  const saint: Saint = {
    name: "St. Albert of Jerusalem",
    date: {
      month: "SEP",
      day: "14",
    },
    description:
      "St. Albert of Jerusalem (c. 1149-1214) was an influential bishop, canon lawyer, and the Patriarch of Jerusalem, best known for drafting the foundational Rule of Life for the Carmelite Order. Born in Italy, likely in Parma, Albert grew up with a passion for the Church, entering the religious life early. His brilliance and deep sense of justice led him to become the Bishop of Vercelli, where he earned a reputation as a skilled mediator and reformer. His abilities in settling disputes and guiding church affairs were highly respected throughout Italy.",
    image: "/saint-image.png",
    audioUrl: "/saint-audio.mp3",
  };

  useEffect(() => {
    if (date.month.value && date.year.value) {
      const daysInMonth = getDaysInMonth(
        parseInt(date.month.value),
        parseInt(date.year.value),
      );

      const days = Array.from({ length: daysInMonth }, (_, i) => ({
        value: (i + 1).toString(),
        label: (i + 1).toString(),
      }));

      setDaysOptions(days);
    }
  }, [date.month.value]);

  const handleDiscover = () => {
    const newDate = {
      month: { ...date.month, error: !date.month.value },
      day: { ...date.day, error: !date.day.value },
      year: { ...date.year, error: !date.year.value },
    };

    setDate(newDate);

    if (newDate.month.error || newDate.day.error || newDate.year.error) {
      return;
    }

    setShowResult(true);
  };

  if (showResult) {
    return (
      <div className="bg-grey-100 p-4 rounded-xl">
        <h2 className="text-[22px] font-semibold font-titles mb-6">
          Discover Your Patron <span className="text-red-700">Saint</span>
        </h2>

        <div className="relative mb-6">
          <Image
            src={saint.image}
            alt={saint.name}
            layout="responsive"
            width={1}
            height={1}
          />

          <div className="absolute bottom-0 left-0 right-0 top-1/2 inset-0 bg-gradient-to-b from-transparent to-black rounded-2xl" />

          <div className="absolute bottom-4 left-4 right-4 flex gap-2">
            <div className=" bg-red-700 text-white px-3 py-1 rounded-2xl w-14 h-14 flex flex-col justify-center items-center">
              <div className="text-xs text-center leading-none">
                {saint.date.month}
              </div>
              <div className="text-[27px] font-bold text-center leading-none">
                {saint.date.day}
              </div>
            </div>

            <h3 className="text-[22px] font-titles font-semibold text-white leading-none pt-1">
              {saint.name}
            </h3>

            <button>
              <Icon icon="share" className="text-white" />
            </button>
          </div>
        </div>

        {saint.audioUrl && (
          <div className="mb-6">
            <div className="flex items-center gap-4">
              <button className="p-2 rounded-full bg-black text-white w-7 h-7 flex items-center justify-center">
                <Icon icon="play" />
              </button>

              <div className="flex-1 pt-2">
                <div className="h-1 bg-grey-200 rounded-full">
                  <div className="w-1/4 h-full bg-black rounded-full" />
                </div>

                <div className="flex justify-between text-sm mt-2">
                  <span>0:57</span>
                  <span>3:47</span>
                </div>
              </div>

              <button className="px-2 py-1 border text-xs rounded-2xl leading-none">
                2X
              </button>
            </div>
          </div>
        )}

        <div className="mb-2 relative pt-6">
          <div className="absolute top-0 -left-4 -right-4 h-10 inset-0 bg-gradient-to-b from-black/10 to-transparent" />

          <h4 className="font-bold mb-2">Listen</h4>

          <div className="relative">
            <p
              ref={descriptionRef}
              data-testid="saint-description"
              className="relative z-10 text-sm text-grey-600 max-h-[200px] overflow-y-scroll scrollbar-thin scrollbar-track-transparent scrollbar-thumb-black pb-2"
            >
              {saint.description}
            </p>

            {scrollPosition != "top" ? (
              <div className="absolute top-0 right-4 h-16 inset-0 bg-gradient-to-b from-grey-100 to-transparent z-20" />
            ) : (
              ""
            )}

            {scrollPosition != "bottom" ? (
              <div className="absolute top-auto bottom-0 right-4 h-16 inset-0 bg-gradient-to-b from-transparent to-grey-100 z-20" />
            ) : (
              ""
            )}
          </div>
        </div>

        <Button iconName="cart" className="w-full" variant="primaryBlack">
          Get your saint card
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-grey-100 p-6 rounded-2xl">
      <div className="mb-4">
        <figure className="flex flex-1 justify-center">
          <Image
            className="max-w-full"
            src={`${IMAGES_PATH}/account/discover-your-patron-saint.png`}
            alt="Discover your patron saint"
            layout="responsive"
            width={16}
            height={9}
          />
        </figure>

        <h2 className="text-[22px] font-semibold font-titles mb-2 mt-4">
          Discover Your Patron <span className="text-red-700">Saint</span>
        </h2>

        <p className="text-[16px] text-grey-600">
          Tell us your birthdate, and we&apos;ll remind you which saint shares
          your special day, connecting you to the lives of the saints in a
          meaningful way
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <div className="flex gap-2">
            <Select
              placeholder="Year"
              options={generateYearOptions()}
              onChange={(value) =>
                setDate({
                  ...date,
                  year: { value, error: false },
                  month: { value: null, error: date.month.error },
                })
              }
              error={date.year.error}
              value={date.year.value}
              size="xs"
            />

            <Select
              placeholder="Month"
              options={MONTHS}
              onChange={(value) =>
                setDate({
                  ...date,
                  month: { value, error: false },
                  day: { value: null, error: date.day.error },
                })
              }
              disabled={!date.year.value}
              error={date.month.error}
              value={date.month.value}
              size="xs"
            />

            <Select
              placeholder="Day"
              options={daysOptions}
              onChange={(value) =>
                setDate({
                  ...date,
                  day: { value, error: false },
                })
              }
              disabled={!date.month.value}
              error={date.day.error}
              value={date.day.value}
              size="xs"
            />
          </div>

          {date.year.error || date.month.error || date.day.error ? (
            <p className="mt-1 text-red-700 text-sm">
              {date.year.error
                ? "Year, month and day are required"
                : date.month.error
                  ? "Month and day are required"
                  : "Day is required"}
            </p>
          ) : (
            ""
          )}
        </div>

        <Button className="w-full" variant="primary" onClick={handleDiscover}>
          Discover your Patron Saint
        </Button>
      </div>
    </div>
  );
};

export default PatronSaint;
