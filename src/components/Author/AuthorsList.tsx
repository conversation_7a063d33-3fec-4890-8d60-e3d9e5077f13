import "swiper/css";
import Timestamp from "../Timestamp";
import Link from "next/link";
import AuthorsDialog from "./AuthorsDialog";
import { useRef, useState } from "react";
import Image from "next/image";

interface AuthorsListProps {
  authors: {
    slug: string;
    name: string;
    image?: {
      url: string;
    };
  }[];
  publishedDate: string | undefined;
  showPublishedDate?: boolean;
  showImages?: boolean;
  size?: "small" | "medium";
  showInOneLine?: boolean;
}

const AuthorsList: React.FC<AuthorsListProps> = ({
  authors,
  publishedDate,
  showPublishedDate = true,
  showImages = false,
  size = "small",
  showInOneLine = true,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  if (!authors?.length) return null;

  const authorCount = authors.length;
  const extraAuthors = authors.slice(1);

  const renderAuthors = () => {
    if (authorCount === 1) {
      return (
        <div className="flex items-center gap-2">
          {showImages && (
            <div className="w-[38px] h-[38px] flex-shrink-0 relative">
              <Link
                role="link"
                href={`/author/${authors[0]?.slug}`}
                className="transition-opacity hover:opacity-85 block w-full h-full"
              >
                <Image
                  src={
                    authors[0]?.image
                      ? authors[0].image.url
                      : "/images/default-author.png"
                  }
                  alt={authors[0]?.name}
                  fill
                  className="rounded-full mr-3"
                />
              </Link>
            </div>
          )}
          <div className={`${size == "small" ? "text-[12px]" : ""} flex-grow`}>
            <div className="inline-block">
              By{" "}
              <strong className="transition-color">
                <Link
                  role="link"
                  href={`/author/${authors[0]?.slug}`}
                  className="hover-underline-animation"
                >
                  {authors[0]?.name}
                </Link>
              </strong>
            </div>{" "}
            {showInOneLine && showPublishedDate && publishedDate && (
              <span>
                · <Timestamp date={publishedDate} />
              </span>
            )}
            {!showInOneLine && showPublishedDate && publishedDate && (
              <div>
                <Timestamp date={publishedDate} />
              </div>
            )}
          </div>
        </div>
      );
    }

    if (authorCount === 2) {
      return (
        <>
          <div className="flex items-center gap-2">
            {showImages && (
              <div className="flex">
                {authors[0]?.image && (
                  <div className="w-[38px] h-[38px] flex-shrink-0 relative">
                    <Link
                      role="link"
                      href={`/author/${authors[0]?.slug}`}
                      className="transition-opacity hover:opacity-85 block w-full h-full"
                    >
                      <Image
                        src={authors[0].image?.url}
                        alt={authors[0]?.name}
                        fill
                        className="rounded-full mr-3"
                      />
                    </Link>
                  </div>
                )}

                {authors[1]?.image && (
                  <div className="w-[38px] h-[38px] flex-shrink-0 relative -ml-4">
                    <Link
                      role="link"
                      href={`/author/${authors[1]?.slug}`}
                      className="transition-opacity hover:opacity-85 block w-full h-full"
                    >
                      <Image
                        src={authors[1]?.image?.url}
                        alt={authors[1]?.name}
                        fill
                        className="rounded-full mr-3"
                      />
                    </Link>
                  </div>
                )}
              </div>
            )}
            <div
              className={`
                flex-grow
                ${size == "small" ? "text-[12px]" : ""} 
                ${showInOneLine ? "flex items-center gap-2" : ""} 
              `}
            >
              <div>
                By{" "}
                <strong className="transition-color">
                  <Link
                    role="link"
                    href={`/author/${authors[0]?.slug}`}
                    className="hover-underline-animation"
                  >
                    {authors[0]?.name}
                  </Link>
                </strong>{" "}
                and{" "}
                <strong>
                  <Link
                    href={`/author/${authors[1]?.slug}`}
                    className="hover-underline-animation"
                  >
                    {authors[1]?.name}
                  </Link>
                </strong>{" "}
                {showInOneLine && showPublishedDate && publishedDate && (
                  <span>
                    · <Timestamp date={publishedDate} />
                  </span>
                )}
              </div>

              {!showInOneLine && showPublishedDate && publishedDate && (
                <div>
                  <Timestamp date={publishedDate} />
                </div>
              )}
            </div>
          </div>
        </>
      );
    }

    return (
      <>
        <div className="flex items-center gap-2">
          {showImages && authors[0]?.image && (
            <div className="w-[38px] h-[38px] flex-shrink-0 relative">
              <Link
                role="link"
                href={`/author/${authors[0]?.slug}`}
                className="transition-opacity hover:opacity-85 block w-full h-full"
              >
                <Image
                  src={authors[0]?.image?.url}
                  alt={authors[0]?.name}
                  fill
                  className="rounded-full mr-3"
                />
              </Link>
            </div>
          )}

          <div>
            <div className="inline-block">
              {authors[0]?.image ? "by " : "By "}
              <strong>
                <Link
                  href={`/author/${authors[0]?.slug}`}
                  className="hover-underline-animation"
                >
                  {authors[0]?.name}
                </Link>
              </strong>
              ,{" "}
              <span className="relative inline-block">
                <button
                  ref={buttonRef}
                  onClick={() => setIsDialogOpen(!isDialogOpen)}
                  className="underline hover:no-underline focus:outline-none"
                >
                  +{authorCount - 1}
                </button>
                <AuthorsDialog
                  isOpen={isDialogOpen}
                  onClose={() => setIsDialogOpen(false)}
                  authors={extraAuthors}
                  buttonRef={buttonRef}
                  showImages={showImages}
                />
              </span>
            </div>{" "}
            {showPublishedDate && publishedDate && (
              <div className="inline-block">
                · <Timestamp date={publishedDate} />
              </div>
            )}
          </div>
        </div>
      </>
    );
  };

  return (
    <div className={`${size == "small" ? "text-[12px]" : ""} mt-4`}>
      {renderAuthors()}
    </div>
  );
};

export default AuthorsList;
