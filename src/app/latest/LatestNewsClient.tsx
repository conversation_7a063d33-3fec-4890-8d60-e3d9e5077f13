"use client";
import React from "react";
import { Article, LatestNewsResponse } from "@/sanity/queries/latest-news";
import Link from "next/link";
import Image from "next/image";
import {
  getArticleUrl,
  getMediaImage,
  truncateDescription,
  truncateTitle,
} from "@/utils/utils";
import Button from "@/components/ui/Button";
import { Icon } from "@/components/Icon";
import { usePagination } from "@/utils/usePagination";
import { Trending } from "@/components/HomePage/Trending";
import { Article as TrendingArticle } from "@/sanity/queries/Trending/trending";
import AuthorsList from "@/components/Author/AuthorsList";

interface LatestNewsClientProps {
  latestNews: LatestNewsResponse;
  generalTrendingArticles: TrendingArticle[];
}

const LatestNewsClient: React.FC<LatestNewsClientProps> = ({
  latestNews,
  generalTrendingArticles,
}) => {
  const itemsPerPage = 10;

  const {
    items: articles,
    isLoading,
    hasMore,
    loadMore,
  } = usePagination<Article>({
    initialItems: latestNews.articles,
    itemsPerPage,
    totalCount: latestNews.articlesCount,
    apiUrl: `/api/latest-news`,
  });

  return (
    <div className="container mx-auto p-4 pt-12">
      <div className="header">
        <h1 className="font-titles font-semibold text-[32px] xl:text-[52px] leading-10 xl:leading-[56px]">
          Latest News
        </h1>
        <p className="pt-3 text-[18px] hidden md:block">
          Our Experts and Contributors Explore the Latest Events and Trends
          Shaping Our Community
        </p>
      </div>
      <div className="lg:grid lg:grid-cols-12 lg:gap-4 mt-4 pt-2 border-t border-grey-200">
        {/* Left Column */}
        <div className="lg:col-span-9 pt-8">
          <section className="overflow-hidden xl:pb-4">
            {articles?.map((article, index) => (
              <div
                key={article.slug || `article-${index}`}
                className="pb-6 mb-4 border-b border-b-grey-200 sm:flex sm:gap-4 lg:gap-8 sm:items-start"
              >
                {article.media && (
                  <div className="w-full flex-shrink-0 aspect-[16/9] lg:aspect-[16/12] xl:aspect-[16/9] relative sm:w-1/2 lg:order-2 lg:w-1/3 mt-4">
                    <Link
                      href={getArticleUrl(
                        article.category?.slug,
                        article.slug,
                        article.subcategory?.slug,
                      )}
                      className="transition-opacity hover:opacity-85"
                    >
                      <Image
                        src={getMediaImage(article.media)}
                        alt={article.title}
                        fill
                        className="object-cover rounded-2xl w-full h-full"
                      />
                    </Link>
                  </div>
                )}

                <div className="sm:w-1/2 lg:w-2/3">
                  <p className="uppercase text-[13px] mt-4 mb-2 sm:mt-0">
                    <Link
                      href={`/${article.category.slug}`}
                      className="hover-underline-animation"
                    >
                      {article.category.title}
                    </Link>
                  </p>

                  <h2 className="font-semibold font-titles text-[24px] leading-[28px] lg:text-[28px] lg:leading-[36px]">
                    <Link
                      href={getArticleUrl(
                        article.category?.slug,
                        article.slug,
                        article.subcategory?.slug,
                      )}
                      className="hover-underline-animation"
                    >
                      {truncateTitle(article.title)}
                    </Link>
                  </h2>

                  <p className="hidden lg:block mt-5 text-16px leading-[24px]">
                    {truncateDescription(article.description)}
                  </p>

                  {article.authors && (
                    <AuthorsList
                      authors={article.authors}
                      publishedDate={article.updated_at}
                    />
                  )}
                </div>
              </div>
            ))}

            {latestNews.articlesCount > itemsPerPage && hasMore && (
              <div className="flex justify-center mt-8">
                <div className="w-52">
                  <Button
                    data-testid="load-more-button"
                    onClick={loadMore}
                    state={isLoading ? "disabled" : "enabled"}
                    className="w-full"
                  >
                    {isLoading ? (
                      <Icon
                        icon="loading"
                        color="white"
                        className="animate-spin inline-block -mb-[5px]"
                      />
                    ) : (
                      "Load More"
                    )}
                  </Button>
                </div>
              </div>
            )}
          </section>
        </div>

        {/* Right Column */}
        <div className="col-span-3">
          <div className="sticky top-24">
            <Trending
              oneColumn={true}
              trendingArticles={generalTrendingArticles}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LatestNewsClient;
