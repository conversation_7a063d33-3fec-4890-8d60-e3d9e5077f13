import { defineQuery } from "next-sanity";
import { Slug } from "../types";
import { workspace } from "@/sanity/env";

export const navigationMenuQuery = defineQuery(`
  *[
    _type == "navigationMenu" && 
    agency == '${workspace}' && 
    !(_id in path("drafts.**")) &&
    title == "Main Menu"
  ][0]{
      title,
      sections[]{
      title,
      slug,
      menuItems[]{
          title,
          linkType,
          internalLink,
          externalLink
      }
    }
  }
`);

export const watchNavigationMenuQuery = defineQuery(`
  *[
    _type == "navigationMenu" && 
    agency == '${workspace}' && 
    title == "Watch Menu"
  ][0]{
      title,
      sections[]{
      title,
      slug,
      menuItems[]{
          title,
          linkType,
          internalLink,
          externalLink
      }
    }
  }
`);

export type Section = {
  title: string;
  slug: Slug;
  menuItems: Array<{
    title: string;
    linkType: "external" | "internal";
    internalLink: Slug;
    externalLink: string;
  }>;
};

export type NavigationMenuQueryResult = {
  title: string;
  sections: Section[];
};

export const socialMediaQuery = defineQuery(`
  *[
    _type == "socialMedia" &&
    agency == '${workspace}' && 
    !(_id in path("drafts.**"))
  ]{
    ...,
    link,
    icon,
    _updatedAt
  } | order(_updatedAt asc)
`);

export type SocialMediaQueryResult = Array<{
  _id: string;
  _type: "socialMedia";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  link: string;
  icon:
    | "android"
    | "facebook"
    | "googleNews"
    | "instagram"
    | "ios"
    | "linkedin"
    | "x"
    | "youtube"
    | "tiktok";
}>;

export const categoryQuery = defineQuery(`
  *[_type == "category" && agency == '${workspace}' && defined(slug.current)]{ slug }
  `);

export type CategoryQueryResult = Array<{
  slug: Slug;
}>;

export const categoryValidationQuery = defineQuery(`
  *[_type == "category" && agency == '${workspace}' && slug.current == $category][0]{
    title,
    slug,
    description,
    seoMetadata,
    pinnedArticles[]->{
      "id": _id,
      "created_at": dateTime(_createdAt),
      "updated_at": dateTime(_updatedAt),
      "publishedDate": dateTime(publishedDate),
      "slug": slug.current,
      title,
      truncatedTitle,
      "excerpt": description,
      "media": featuredMedia[] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "tags": tags[]-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
    },
  }
  `);

export const complianceLinkQuery = defineQuery(`
 *[
    _type == "complianceLink" &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**"))
  ]{
  ...,
  title,
  slug,
  isOnReducedFooter,
  externalUrl
  } | order(_updatedAt asc)
`);

export type ComplianceLinkQueryResult = Array<{
  _id: string;
  _type: "complianceLink";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: string;
  slug: Slug;
  isOnReducedFooter: boolean;
  externalUrl: string;
}>;

export type DonationLinkQueryResult = {
  label: string;
  url: string;
};

export const ewtnNetworkLinksQuery = defineQuery(`
  *[
     _type == "networkLink" &&
     !(_id in path("drafts.**")) &&
     slug != '${workspace}'
   ]{
   ...,
   title,
   url
   } | order(title asc)
 `);

export type EwtnNetworkLinksQueryResult = Array<{
  title: string;
  url: string;
}>;
