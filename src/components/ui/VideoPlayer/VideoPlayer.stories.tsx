import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import VideoPlayer, { VideoPlayerProps } from ".";

export default {
  title: "Components/VideoPlayer",
  component: VideoPlayer,
  argTypes: {
    src: {
      control: {
        type: "text",
      },
    },
  },
} as Meta;

const Template: StoryFn<VideoPlayerProps> = (args) => <VideoPlayer {...args} />;

export const VideoPlayerComponent = Template.bind({});
VideoPlayerComponent.args = {
  src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4",
};
