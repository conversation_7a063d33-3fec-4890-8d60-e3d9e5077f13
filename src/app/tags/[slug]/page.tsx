import { sanityFetch } from "@/sanity/live";
import TagClient from "@/app/tags/[slug]/TagClient";
import { tagQuery } from "@/sanity/queries/Tag/tag";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { fetchTrendingArticlesFromAnalytics } from "@/app/api/trending";
import { buildTagJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const { data: tag } = await fetchTag({ slug: slug });

  if (!tag) {
    return notFound();
  }

  return generatePageMetadata(
    `/tags/${slug}`,
    tag.seoMetadata?.title ?? tag.title,
    tag.seoMetadata?.description ?? tag.description,
    tag.image,
  );
}

interface TagProps {
  params: Promise<{ slug: string }>;
}

async function fetchTag(options: { slug: string }) {
  return await sanityFetch({
    query: tagQuery,
    params: { slug: options.slug },
  });
}

export default async function TagPage({ params }: TagProps) {
  const { slug } = await params;
  const { data: tag } = await fetchTag({ slug: slug });

  if (!tag) {
    return notFound();
  }

  const generalTrendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: null,
    subcategorySlug: null,
    periodStart: undefined,
    periodEnd: undefined,
  });

  const jsonLd = buildTagJsonLd(tag.title, slug, tag.description);
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <TagClient
        tag={tag}
        generalTrendingArticles={generalTrendingArticles}
      ></TagClient>
    </>
  );
}
