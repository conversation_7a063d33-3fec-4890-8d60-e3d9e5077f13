import { FacebookShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const FacebookButton = ({ shareUrl }: { shareUrl: string }) => {
  return (
    <FacebookShareButton
      url={shareUrl}
      title="Check this out"
      aria-label="Share on Facebook"
    >
      <ShareIconButton
        icon={(hover) => (
          <Icon icon="facebook" size={24} color={hover ? "#254da0" : "black"} />
        )}
      />
    </FacebookShareButton>
  );
};

export default FacebookButton;
