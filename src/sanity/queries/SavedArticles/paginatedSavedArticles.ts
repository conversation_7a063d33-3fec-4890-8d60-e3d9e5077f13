import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const paginatedSavedArticlesQuery = defineQuery(`
    {
    "articles": *[
      _type == "dailyStoryArticle" &&
      agency == '${workspace}' &&
      !(_id in path("drafts.**")) &&
      _id in $ids
    ][0...10] {
      "id": _id,
      title,
      "slug": slug.current,
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type
      },
      "updated_at": dateTime(_updatedAt),
      "category_slug": category.slug.current,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      authors[]->{_id, "name": fullName, "slug": slug.current}
    }
    }
`);
