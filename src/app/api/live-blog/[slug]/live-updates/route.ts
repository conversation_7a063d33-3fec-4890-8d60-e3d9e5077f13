import { sanityFetch } from "@/sanity/live";
import {
  liveBlogArticleQueryAsc,
  liveBlogArticleQueryDesc,
} from "@/sanity/queries/liveBlog";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> },
) {
  try {
    const liveBlogSlug = (await params).slug;

    const url = new URL(request.url);
    const pageQuery = Number(url.searchParams.get("page")) || 1;
    const itemsPerPageQuery =
      Number(url.searchParams.get("itemsPerPage")) || 20;
    const searchQuery = url.searchParams.get("search") || "";
    const dateQuery = url.searchParams.get("date") || "";
    const orderQuery = url.searchParams.get("order") || "latest";

    const response = await sanityFetch({
      query:
        orderQuery == "latest"
          ? liveBlogArticleQueryDesc
          : liveBlogArticleQueryAsc,
      params: {
        slug: liveBlogSlug,
        pageQuery,
        itemsPerPageQuery,
        searchQuery,
        dateQuery,
      },
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching live news:", error);

    return NextResponse.json(
      {
        error: "Error fetching live news",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
