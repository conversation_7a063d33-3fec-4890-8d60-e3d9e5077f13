import { useEffect, useState } from "react";
import { Tweet } from "react-tweet";

const getTweetId = (url: string): string => {
  try {
    const parts = url.split("/");
    return parts[parts.length - 1];
  } catch (e) {
    console.error(e);
    return "";
  }
};

const XEmbed = ({ url }: { url: string }) => {
  const [canRender, setCanRender] = useState(false);

  useEffect(() => {
    const handleConsent = () => {
      if (window._iub?.cs?.isConsentGiven?.()) {
        setCanRender(true);
      }
    };

    if (window._iub?.cs?.isConsentGiven?.()) {
      setCanRender(true);
    } else {
      window.addEventListener("iubenda_cs_consent_given", handleConsent);
    }

    return () => {
      window.removeEventListener("iubenda_cs_consent_given", handleConsent);
    };
  }, []);

  if (!canRender) {
    return;
  }

  const id = getTweetId(url);
  return id ? <Tweet id={id} /> : null;
};

export default XEmbed;
