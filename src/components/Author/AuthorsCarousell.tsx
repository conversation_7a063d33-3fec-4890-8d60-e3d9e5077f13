import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { Author } from "@/sanity/queries/dailyStory";
import { Navigation } from "swiper/modules";
import RequireLoginTooltip from "../Auth/RequireLoginTooltip";
import { useEffect, useRef, useState } from "react";
import { useUser } from "@auth0/nextjs-auth0";
import Image from "next/image";
import Button from "../ui/Button";
import Link from "next/link";
import { Icon } from "../Icon";
import { handleCopy, imageLoaderUtility } from "@/utils/utils";

interface AuthorCardProps {
  author: Author;
}

export function AuthorCard({ author }: AuthorCardProps) {
  const { user } = useUser();
  const [showNeedLogin, setShowNeedLogin] = useState<boolean>(false);
  const [isFollowed, setIsFollowed] = useState<boolean>(false);
  const [showLimitTooltipClicked, setShowLimitTooltipClicked] = useState(false);
  const [isFollowedCheckComplete, setIsFollowedCheckComplete] = useState(false);
  const [showReachFollowLimitTooltip, setReachFollowLimitTooltip] =
    useState<boolean>(false);

  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkIfAuthorIsFollowed = async () => {
      if (!user) {
        setIsFollowedCheckComplete(true);
        return;
      }

      try {
        const res = await fetch(`/api/authors/${author.id}/followers`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!res.ok) {
          console.error("Failed to fetch followed status");
          return;
        }
        const data = await res.json();
        if (data?.followerCount >= 5) {
          setReachFollowLimitTooltip(true);
        } else {
          setReachFollowLimitTooltip(false);
        }

        setIsFollowed(data?.userFollows ?? false);
      } catch (err) {
        console.error("Error checking bookmark:", err);
      } finally {
        setIsFollowedCheckComplete(true);
      }
    };

    checkIfAuthorIsFollowed();
  }, [user, author.slug]);

  const handleFollow = async () => {
    if (!user) {
      setShowNeedLogin(true);
      return;
    }

    try {
      const response = await fetch(`/api/authors/${author.id}/followers`, {
        method: isFollowed ? "DELETE" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const data = await response.json();

        if (
          response.status === 400 ||
          data?.error?.toLowerCase().includes("limit")
        ) {
          setShowLimitTooltipClicked(true);
        } else {
          console.error("Failed to update followers status", data?.error);
        }

        return;
      }

      setIsFollowed(!isFollowed);
    } catch (error) {
      console.error("Error updating follow:", error);
    }
  };

  return (
    <div className="border border-grey-200 rounded-2xl p-4 h-full">
      <div className="flex items-top gap-4">
        <figure className="relative w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
          <Link
            role="link"
            href={`/author/${author.slug}`}
            className="transition-opacity hover:opacity-85 block w-full h-full"
          >
            <Image
              loader={imageLoaderUtility}
              src={
                author?.image ? author.image.url : "/images/default-author.png"
              }
              alt={author.name}
              fill
            />
          </Link>
        </figure>

        <div className="flex gap-2 flex-wrap flex-col">
          <h1 className="font-titles font-semibold text-[16px] leading-4">
            <Link
              role="link"
              href={`/author/${author.slug}`}
              className="hover-underline-animation"
            >
              {author.name}
            </Link>
          </h1>

          {author.jobTitle && (
            <div className="text-[12px]">{author.jobTitle}</div>
          )}

          <div className="flex gap-2 items-center">
            {author.location && (
              <div className="text-[14px] leading-4">{author.location}</div>
            )}

            <div>
              <div className="relative z-20" ref={wrapperRef}>
                {isFollowedCheckComplete && (
                  <>
                    <Button
                      variant="outlinedBlack"
                      size="xs"
                      className={`font-normal ${isFollowed ? "!border-black !bg-black !text-white" : ""}`}
                      onClick={() => {
                        if (showReachFollowLimitTooltip && !isFollowed) {
                          setShowLimitTooltipClicked(true);
                          return;
                        }
                        setShowLimitTooltipClicked(false);
                        if (user) {
                          handleFollow();
                        } else {
                          setShowNeedLogin(true);
                        }
                      }}
                    >
                      {isFollowed ? "Following" : "Follow +"}
                    </Button>

                    {showLimitTooltipClicked && !isFollowed && (
                      <div
                        ref={tooltipRef}
                        className="absolute top-full mt-2 left-1/2 -translate-x-1/2 w-[240px] bg-black text-white text-sm p-3 rounded-xl z-30 shadow-lg"
                      >
                        <div className="text-center font-semibold text-[15px]">
                          You’ve reached the limit of 5 followed authors.
                        </div>
                        <div className="mt-1 text-center">
                          Unfollow one to follow a new author.
                        </div>
                        <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-black" />
                      </div>
                    )}
                  </>
                )}

                <RequireLoginTooltip
                  show={showNeedLogin}
                  centerOnMobile={true}
                  onHideNeedLogin={(show: boolean) => setShowNeedLogin(show)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <p className="mt-4 text-[16px] lg:text-[18px]">{author.shortBio}</p>

      <div className="mt-2 font-bold">
        <Link
          role="link"
          href={`/author/${author.slug}`}
          className="hover-underline-animation"
        >
          See full bio
        </Link>
      </div>

      {(author.socialMedia?.length || author.email) && (
        <div className="border-t border-grey-200 pt-4 mt-4">
          <div className="flex mt-1 pb-2 flex-wrap gap-2">
            {author.email && (
              <div
                onClick={() => handleCopy(author.email)}
                className="flex gap-1 hover-underline-animation-child pr-4"
              >
                <Icon icon="email" />

                <span className="inline-block hover-underline-animation-child-item">
                  {author.email}
                </span>
              </div>
            )}

            {author.socialMedia?.map((socialMediaItem) => (
              <Link
                key={socialMediaItem.icon}
                href={socialMediaItem.link}
                target="_blank"
                className="flex items-center gap-1 hover-underline-animation-child pr-4"
              >
                <Icon icon={socialMediaItem.icon} className="w-6 h-6" />

                <span className="inline-block hover-underline-animation-child-item">
                  {socialMediaItem.nickname}
                </span>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

interface AuthorsCarousellProps {
  authors: Author[];
}

export default function AuthorsCarousell({ authors }: AuthorsCarousellProps) {
  return (
    <div className="relative mt-8 overflow-hidden">
      {authors.length == 1 ? (
        <AuthorCard author={authors[0]} />
      ) : (
        <>
          <div className="h-full">
            <Swiper
              modules={[Navigation]}
              navigation
              loop={false}
              centeredSlides={false}
              breakpoints={{
                320: {
                  slidesPerView: 1,
                  spaceBetween: 16,
                },
                650: {
                  slidesPerView: 2,
                  spaceBetween: 16,
                },
              }}
              className="relative authors-swiper"
            >
              {authors.map((author) => (
                <SwiperSlide key={author.slug} className="w-full h-full">
                  <AuthorCard author={author} />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </>
      )}
    </div>
  );
}
