"use client";

import LatestNews from "@/components/HomePage/LatestNews";
import BreakingNews from "@/components/HomePage/BreakingNews";
import { BreakingNewsResult } from "@/sanity/queries/HomePage/breakingNews";
import MainSection, { TertiaryNews } from "@/components/HomePage/MainSection";
import { HomePageResult } from "@/sanity/queries/HomePage/homePage";
import SectionRender from "@/components/HomePage/SectionRender";
import { LatestNewsHomePageResponse } from "@/sanity/queries/latest-news";
import { TrendingBar } from "@/components/HomePage/TrendingBar";
import { RightColumn } from "@/components/HomePage/RightColumn";

interface HomePageClientProps {
  homePage: NonNullable<HomePageResult>;
  breakingNews: NonNullable<BreakingNewsResult>;
  latestNews: NonNullable<LatestNewsHomePageResponse>;
}

export default function HomePageClient({
  homePage,
  breakingNews,
  latestNews,
}: HomePageClientProps) {
  const homePageSections = homePage.sections;
  const tertiaryArticles = homePage.mainArticles?.tertiary;
  const trendingArticles = homePage.trendingArticles;
  const rightColumn = homePage.rightColumn;
  return (
    <>
      {breakingNews && <BreakingNews breakingNews={breakingNews} />}
      <div>
        <div className="container mx-auto sm:pt-8 px-4">
          <TrendingBar trendingArticles={trendingArticles} />
          <div className="grid grid-cols-1 md:grid-cols-12 md:gap-4 lg:pb-8">
            {/* Left Column (Latest News) - Second on Mobile, Default on Desktop */}
            <div className="order-2 md:order-none md:col-span-4 lg:col-span-3 md:pr-4 md:border-r md:border-grey-200">
              <div className="sticky top-24">
                <LatestNews latestNews={latestNews} />
              </div>
            </div>

            {/* Middle Column (Main News) - First on Mobile, Default on Desktop */}
            <div className="order-1 md:order-none md:col-span-4 lg:col-span-6 bg-gray-300 rounded-2xl">
              <MainSection mainArticles={homePage.mainArticles} />
            </div>

            {/* Right Column (Latest News) - Third on Mobile, Default on Desktop */}
            <div className="order-3 md:order-none md:col-span-4 lg:col-span-3 md:pl-4 md:border-l md:border-grey-200">
              <div className="sticky top-24">
                <RightColumn
                  rightColumn={rightColumn}
                  className="hidden md:block"
                />
              </div>
            </div>
          </div>

          <div className="hidden md:block lg:hidden max-w-[630px] mx-auto mt-4 border-grey-200 border-t">
            <TertiaryNews articles={tertiaryArticles} />
          </div>
        </div>
      </div>

      <SectionRender
        sections={homePageSections}
        trendingArticles={trendingArticles}
      />
    </>
  );
}
