"use client";

import { useEffect, useState } from "react";

declare global {
  interface Window {
    tumblr: {
      embed: () => void;
    };
  }
}

const TumblrEmbed = ({ embedUrl }: { embedUrl: string }) => {
  const [canRender, setCanRender] = useState(false);

  useEffect(() => {
    const handleConsent = () => {
      if (window._iub?.cs?.isConsentGiven?.()) {
        setCanRender(true);
      }
    };

    if (window._iub?.cs?.isConsentGiven?.()) {
      setCanRender(true);
    } else {
      window.addEventListener("iubenda_cs_consent_given", handleConsent);
    }

    return () => {
      window.removeEventListener("iubenda_cs_consent_given", handleConsent);
    };
  }, []);

  useEffect(() => {
    if (!canRender) {
      return;
    }

    const scriptId = "tumblr-embed-script";

    // Remove existing script if it exists (force reload)
    const existingScript = document.getElementById(scriptId);
    if (existingScript) {
      existingScript.remove();
    }

    // Create and append Tumblr script
    const script = document.createElement("script");
    script.id = scriptId;
    script.src = "https://secure.assets.tumblr.com/post.js";
    script.async = true;
    script.onload = () => {
      if (window.tumblr && typeof window.tumblr.embed === "function") {
        window.tumblr.embed();
      }
    };

    document.body.appendChild(script);

    return () => {
      script.remove(); // Cleanup on unmount
    };
  }, [canRender]);

  if (!canRender) {
    return;
  }

  return <div className="tumblr-post" data-href={embedUrl}></div>;
};

export default TumblrEmbed;
