"use client";
import { useState, useEffect } from "react";
import Pagination from "@/components/ui/Pagination";
import Button from "@/components/ui/Button";
import { IMAGES_PATH } from "@/constants";
import Link from "next/link";
import { Author } from "@/sanity/queries/Author/authorFollowers";
import { Article } from "@/app/api/articles/bookmarks/route";
import NewsCard from "@/components/Account/Interests/newsCard";
import Image from "next/image";
import Skeleton from "@/components/ui/Skeleton";

export default function Authors() {
  const [currentPage, setCurrentPage] = useState(1);
  const [articles, setArticles] = useState<Article[]>([]);
  const [selectedAuthors, setSelectedAuthors] = useState([] as Author[]);
  const [loadingAuthors, setLoadingAuthors] = useState(true);
  const [loadingArticles, setLoadingArticles] = useState(true);
  const [hasFetchedOnce, setHasFetchedOnce] = useState(false);
  const itemsPerPage = 5;
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentNews = articles.slice(indexOfFirstItem, indexOfLastItem);

  useEffect(() => {
    const fetchArticles = async () => {
      setLoadingArticles(true);
      if (!selectedAuthors.length) {
        setArticles([]);
        setLoadingArticles(false);
        setHasFetchedOnce(true);
        return;
      }
      const selectedAuthorsIds = selectedAuthors.map((author) => author.id);

      try {
        const res = await fetch("/api/articles/by-authors", {
          method: "POST",
          body: JSON.stringify({ authorsIds: selectedAuthorsIds }),
          headers: { "Content-Type": "application/json" },
        });

        const json = await res.json();
        setArticles(json.items || []);
      } catch (err) {
        console.error("Failed to load articles by tag:", err);
      } finally {
        setLoadingArticles(false);
        setHasFetchedOnce(true);
      }
    };

    fetchArticles();
  }, [selectedAuthors]);

  useEffect(() => {
    const fetchFollowedAuthors = async () => {
      setLoadingAuthors(true);

      try {
        const res = await fetch("/api/authors/followers");
        const json = await res.json();
        setSelectedAuthors(json.userFollows ?? []);
      } catch (error) {
        console.error("Error loading followed tags:", error);
      } finally {
        setLoadingAuthors(false);
      }
    };

    fetchFollowedAuthors();
  }, []);

  const handleRemoveAuthor = async (id: string): Promise<void> => {
    try {
      setSelectedAuthors((prev) => prev.filter((author) => author.id !== id));
      await fetch(`/api/authors/${id}/followers`, {
        method: "DELETE",
      });
    } catch (error) {
      console.error("Failed to unfollow author:", id, error);
    }
  };

  return (
    <div>
      <div className="bg-grey-100 p-4 rounded-xl">
        <div className="mb-4 border-b border-grey-200 pb-4">
          <h2 className="text-2xl font-titles font-bold mb-4">
            Authors you follow
          </h2>
          <div className="flex flex-col gap-4">
            {selectedAuthors.map((author) => (
              <div key={author.id} className="flex justify-between items-start">
                <div className="flex-1" key={author.id}>
                  <h3 className="text-[17px] font-bold mb-1 leading-none">
                    <Link
                      href={`/author/${author.slug}`}
                      className="hover-underline-animation"
                    >
                      {author.name}
                    </Link>
                  </h3>

                  <p className="text-[13px]">{author.jobTitle}</p>
                </div>

                <Button
                  variant="secondary"
                  className="!bg-transparent"
                  size="xs"
                  onClick={() => handleRemoveAuthor(author.id)}
                >
                  Following
                </Button>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-4 mt-5">
          <h2 className="text-2xl font-bold mb-6 font-titles">
            Latest updates from your favorite authors
          </h2>
          {loadingArticles || loadingAuthors ? (
            Array.from({ length: 5 }, (_, i) => (
              <div
                className="flex gap-4 items-center group"
                data-testid="news-card-container"
                key={`authors-article-skeleton-${i}`}
              >
                <div className="flex gap-4 pb-4 w-full">
                  <div className="w-full">
                    <Skeleton
                      imagePosition="right"
                      imageHeight="5rem"
                      imageWidth="8rem"
                      titleWidth="100%"
                      titleHeight="1.5rem"
                    />
                  </div>
                </div>
              </div>
            ))
          ) : currentNews.length ? (
            <div className="space-y-4">
              {currentNews.map((item) => (
                <NewsCard key={item.id} item={item} />
              ))}
            </div>
          ) : hasFetchedOnce ? (
            <div className="mt-8 max-w-96 mx-auto flex flex-col items-center">
              <Image
                src={`${IMAGES_PATH}/account/no-authors-selected.png`}
                alt="We suggest selecting at least one topic to personalize your experience"
                width={162}
                height={162}
                className="max-w-full"
              />

              <p className="text-sm text-center">
                We suggest selecting at least one author to personalize your
                experience
              </p>
            </div>
          ) : null}

          <Pagination
            totalItems={articles.length}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
    </div>
  );
}
