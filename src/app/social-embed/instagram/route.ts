// app/social-embed/instagram/route.ts
import { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const url = searchParams.get("url");

  if (!url) {
    return new Response(
      `<!DOCTYPE html><html><body><div>URL no válida</div></body></html>`,
      { headers: { "Content-Type": "text/html" } },
    );
  }

  // This was added to embed the Instagram widget in our own ifram
  // to aboid conflicts with Iubenda, after loading the widget it sends
  // a message via browser communication with the widget height, it does
  // that after 1 second to allow widget visual render to finish and
  // send the correct height
  const html = `
    <!DOCTYPE html>
    <html lang="es">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Instagram Embed</title>
      <script async src="//www.instagram.com/embed.js"></script>
    </head>
    <body style="margin: 0; padding: 0; background: transparent; overflow: hidden;">
      <blockquote 
        class="instagram-media" 
        style="max-width:600px; min-width:326px; padding:0; margin:0; width:99.375%;"
        data-instgrm-permalink="${url}"
        data-instgrm-version="14">
      </blockquote>
      
      <script>
        function notifyResize() {
          const height = document.body.scrollHeight;

          window.parent.postMessage({
            type: 'instagram-resize-${url}',
            height: height,
          }, '*');
        }
        
        window.addEventListener('load', () => {
          const checkLoaded = setInterval(() => {
            if (document.querySelector('.instagram-media')) {
              notifyResize();

              clearInterval(checkLoaded);
            }
          }, 500);
        });
      </script>
    </body>
    </html>
  `;

  return new Response(html, {
    headers: { "Content-Type": "text/html" },
  });
}
