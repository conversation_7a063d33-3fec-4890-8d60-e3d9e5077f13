import { useState } from "react";

interface PaginationOptions<T> {
  initialItems: T[];
  itemsPerPage: number;
  totalCount: number;
  apiUrl: string;
  initialPage?: number;
  extraParams?: Record<string, string | number | boolean | null | string[]>;
}

interface PaginationResult<T> {
  items: T[];
  page: number;
  isLoading: boolean;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  resetPagination: () => void;
  setItems: (newItems: T[]) => void;
}

export function usePagination<T>({
  initialItems,
  itemsPerPage,
  totalCount,
  apiUrl,
  initialPage = 1,
  extraParams = {},
}: PaginationOptions<T>): PaginationResult<T> {
  const [items, setItems] = useState<T[]>(initialItems);
  const [page, setPage] = useState<number>(initialPage);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(totalCount > itemsPerPage);

  const buildQueryParams = (
    params: Record<string, string | number | boolean | null | string[]>,
  ) => {
    return (
      Object.entries(params)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .filter(([_key, value]) => value !== null && value !== undefined)
        .map(([key, value]) => {
          if (Array.isArray(value)) {
            return value
              .map(
                (item) =>
                  `${encodeURIComponent(key)}[]=${encodeURIComponent(item)}`,
              )
              .join("&");
          }
          return `${encodeURIComponent(key)}=${encodeURIComponent(value as string)}`;
        })
        .join("&")
    );
  };

  const fetchItems = async (page: number, pageSize: number) => {
    const queryParams = buildQueryParams({ ...extraParams, page, pageSize });
    const response = await fetch(`${apiUrl}?${queryParams}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Error loading more items");
    }

    const { data } = await response.json();
    return {
      data: data.items,
      totalCount: data.totalCount,
    };
  };

  const loadMore = async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);

    try {
      const nextPage = page + 1;
      const result = await fetchItems(nextPage, itemsPerPage);

      if (result?.data?.length) {
        setItems((prev) => [...prev, ...result.data]);
        setPage(nextPage);
        setHasMore(result.totalCount > nextPage * itemsPerPage);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error loading more items:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Reset pagination when a new search is triggered
  const resetPagination = () => {
    setPage(1);
    setItems([]);
    setHasMore(true);
  };

  return {
    items,
    page,
    isLoading,
    hasMore,
    loadMore,
    resetPagination,
    setItems,
  };
}
