"use client";

import AccountLayout from "@/components/Account/Layout";
import PatronSaint from "@/components/Account/PatronSaint";
import Interests from "./interests";
import Authors from "./authors";
import { SearchTag } from "@/sanity/queries/Tag/tag";

type Props = {
  tags: SearchTag[];
};

export default function MyInterestsClient({ tags }: Props) {
  return (
    <AccountLayout>
      <AccountLayout.Left>
        <div className="hidden lg:hidden scroll-mt-80">
          <PatronSaint />
        </div>
      </AccountLayout.Left>

      <AccountLayout.Main>
        <Interests tags={tags} />

        <div className="mt-6">
          <Authors />
        </div>
      </AccountLayout.Main>

      <AccountLayout.Right>
        <PatronSaint />
      </AccountLayout.Right>
    </AccountLayout>
  );
}
