import Checkbox from "@/components/ui/Form/Checkbox";
import { render, screen, fireEvent } from "@testing-library/react";

describe("Checkbox", () => {
  it("Renders Checkbox component", () => {
    render(<Checkbox label="Test" checked={false} onChange={() => {}} />);
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("Calls onChange when clicked", () => {
    const onChange = jest.fn();
    render(<Checkbox label="Test" checked={false} onChange={onChange} />);
    fireEvent.click(screen.getByText("Test"));
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it("Renders error message when provided", () => {
    render(
      <Checkbox
        label="Test"
        checked={false}
        onChange={() => {}}
        error="Error message"
      />,
    );
    expect(screen.getByText("Error message")).toBeInTheDocument();
  });

  it("Does not render error message when not provided", () => {
    render(<Checkbox label="Test" checked={false} onChange={() => {}} />);
    expect(screen.queryByText("Error message")).not.toBeInTheDocument();
  });
});
