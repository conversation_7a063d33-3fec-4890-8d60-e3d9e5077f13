import { render, screen, fireEvent } from "@testing-library/react";
import MultiSelect from "@/components/ui/Form/MultiSelect";

const mockOptions = [
  { id: 1, name: "Option 1" },
  { id: 2, name: "Option 2" },
  { id: 3, name: "Option 3" },
  { id: 4, name: "Test Option" },
];

const defaultProps = {
  options: mockOptions,
  selectedOptions: [],
  maxSelectedOptions: 20,
  onSelect: jest.fn(),
  onRemove: jest.fn(),
  placeholder: "Select options...",
  onEmptyText: "Maximum options selected",
};

describe("MultiSelect", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders with placeholder text", () => {
    render(<MultiSelect {...defaultProps} />);
    expect(
      screen.getByPlaceholderText("Select options..."),
    ).toBeInTheDocument();
  });

  it("shows filtered options when typing in search", () => {
    render(<MultiSelect {...defaultProps} />);
    const input = screen.getByPlaceholderText("Select options...");

    fireEvent.change(input, { target: { value: "Test" } });

    expect(screen.getByText("Test Option")).toBeInTheDocument();
    expect(screen.queryByText("Option 1")).not.toBeInTheDocument();
  });

  it("calls onSelect when clicking an option", () => {
    render(<MultiSelect {...defaultProps} />);
    const input = screen.getByPlaceholderText("Select options...");

    fireEvent.change(input, { target: { value: "Option 1" } });
    fireEvent.click(screen.getByText("Option 1"));

    expect(defaultProps.onSelect).toHaveBeenCalledWith(1);
  });

  it("calls onRemove when clicking a selected option", () => {
    render(
      <MultiSelect
        {...defaultProps}
        selectedOptions={[{ id: 1, name: "Option 1" }]}
      />,
    );

    fireEvent.click(screen.getByText("Option 1"));

    expect(defaultProps.onRemove).toHaveBeenCalledWith(1);
  });

  it("clears search text when selecting an option", () => {
    render(<MultiSelect {...defaultProps} />);
    const input = screen.getByPlaceholderText("Select options...");

    fireEvent.change(input, { target: { value: "Option 1" } });
    fireEvent.click(screen.getByText("Option 1"));

    expect(input).toHaveValue("");
  });

  it("shows no matches message when search has no results", () => {
    render(<MultiSelect {...defaultProps} />);
    const input = screen.getByPlaceholderText("Select options...");

    fireEvent.change(input, { target: { value: "xyz" } });

    expect(screen.getByText("No matches for that search.")).toBeInTheDocument();
  });

  it("disables input when max options are selected", () => {
    const selectedOptions = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      name: `Option ${i + 1}`,
    }));

    render(<MultiSelect {...defaultProps} selectedOptions={selectedOptions} />);

    const input = screen.getByPlaceholderText("Select options...");
    expect(input).toBeDisabled();
    expect(screen.getByText("Maximum options selected")).toBeInTheDocument();
  });

  it("clears search text when pressing Escape", () => {
    render(<MultiSelect {...defaultProps} />);
    const input = screen.getByPlaceholderText("Select options...");

    fireEvent.change(input, { target: { value: "Test" } });
    fireEvent.keyDown(input, { key: "Escape" });

    expect(input).toHaveValue("");
  });

  it("shows correct selected/max count", () => {
    const selectedOptions = [
      { id: 1, name: "Option 1" },
      { id: 2, name: "Option 2" },
    ];

    render(<MultiSelect {...defaultProps} selectedOptions={selectedOptions} />);

    expect(screen.getByText("2")).toBeInTheDocument();
    expect(screen.getByText("20")).toBeInTheDocument();
  });
});
