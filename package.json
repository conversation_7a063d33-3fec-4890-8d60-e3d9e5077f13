{"name": "cna-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "storybook": "storybook build && storybook dev -p 3100", "format": "pretty-quick --check && prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky"}, "dependencies": {"@auth0/nextjs-auth0": "^4.6.1", "@portabletext/react": "^3.2.0", "@sanity/client": "^7.8.1", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "disqus-react": "^1.1.7", "framer-motion": "^12.4.2", "material-symbols": "^0.27.2", "next": "15.3.3", "next-sanity": "^10.0.3", "nodemailer": "^7.0.3", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-select": "^5.10.1", "react-share": "^5.2.2", "react-social-media-embed": "^2.5.18", "react-toastify": "^11.0.5", "react-tweet": "^3.2.2", "schema-dts": "^1.1.5", "swiper": "^11.2.2", "tailwind-scrollbar-hide": "^2.0.0", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@chromatic-com/storybook": "^1.9.0", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-interactions": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/addon-onboarding": "^8.3.5", "@storybook/blocks": "^8.3.5", "@storybook/nextjs": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/test": "^8.3.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "eslint": "^9.18.0", "eslint-config-next": "15.0.2", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.4.2", "pretty-quick": "^4.0.0", "storybook": "^8.3.5", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "packageManager": "pnpm@9.12.2+sha1.3012e6dd27e70ec4e185be062e8a124523dccfc4"}