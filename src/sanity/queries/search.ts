import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";
export const searchQueryAsc = defineQuery(`
  {
    "articles": *[
      _type in ["dailyStoryArticle", "liveBlogArticle"] && 
      agency == '${workspace}' &&
      !(_id in path("drafts.**")) &&
      defined(publishedDate) &&
      (title match $searchQuery + "*" || 
       description match $searchQuery + "*" || 
       body[].children[].text match $searchQuery + "*") &&
     (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
    ]  
    | order(publishedDate asc) [0...9]{  
      title,  
      "slug": slug.current,
      description,  
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type
      },
      "publishedDate": dateTime(publishedDate),  
      authors[]->{
        _id, 
        "name": fullName, 
        "slug": slug.current,
        image {
          "url": secure_url
        }
      },
    },
    "articlesCount": count(
      *[_type in ["dailyStoryArticle", "liveBlogArticle"] && 
       agency == '${workspace}' &&
        !(_id in path("drafts.**")) && 
        defined(publishedDate) &&
        (title match $searchQuery + "*" || 
         description match $searchQuery + "*" || 
         body[].children[].text match $searchQuery + "*") &&
       (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
      ]
    )
  }
  `);

export const searchQueryDesc = defineQuery(`
    {
      "articles": *[
        _type in ["dailyStoryArticle", "liveBlogArticle"] && 
          agency == '${workspace}' &&
        !(_id in path("drafts.**")) &&
        defined(publishedDate) &&
        (title match $searchQuery + "*" || 
         description match $searchQuery + "*" || 
         body[].children[].text match $searchQuery + "*") &&
        (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
      ]  
      | order(publishedDate desc) [0...9]{  
        title,  
        "slug": slug.current,
        description,  
        "category": category-> {
          "id": _id,
          title,
          "slug": slug.current
        },
        "subcategory": subcategory-> {
          "id": _id,
          title,
          "slug": slug.current
        },
        "media": featuredMedia[0] {
          "url": secure_url,
          "type": resource_type
        },
        "publishedDate": dateTime(publishedDate),  
        authors[]->{
          _id, 
          "name": fullName, 
          "slug": slug.current,
          image {
            "url": secure_url
          }
        },
      },
      "articlesCount": count(
        *[_type in ["dailyStoryArticle", "liveBlogArticle"] &&
          agency == '${workspace}' && 
          !(_id in path("drafts.**")) && 
          defined(publishedDate) &&
          (title match $searchQuery + "*" || 
           description match $searchQuery + "*" || 
           body[].children[].text match $searchQuery + "*") &&
         (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
        ]
      )
    }
    `);

export const moreSearchQueryAsc = defineQuery(`
      {
        "items": *[  
          _type in ["dailyStoryArticle", "liveBlogArticle"] &&  
          agency == '${workspace}' &&
          !(_id in path("drafts.**")) &&  
          defined(dateTime(publishedDate)) &&
          (title match $searchQuery + "*" ||  
           description match $searchQuery + "*" ||  
           body[].children[].text match $searchQuery + "*") &&  
         (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
        ]  
        | order(publishedDate asc) [$start...$end]{
          title,  
          "slug": slug.current,
          description,  
          "category": category-> {
            "id": _id,
            title,
            "slug": slug.current
          },
          "subcategory": subcategory-> {
            "id": _id,
            title,
            "slug": slug.current
          },
          "media": featuredMedia[0] {
            "url": secure_url,
            "type": resource_type
          },
          "publishedDate": dateTime(publishedDate),  
          authors[]->{_id, "name": fullName, "slug": slug.current, image { "url": secure_url }},
        },
        "totalCount": count(
          *[_type in ["dailyStoryArticle", "liveBlogArticle"] &&  
          agency == '${workspace}' &&
            !(_id in path("drafts.**")) &&  
            defined(dateTime(publishedDate)) &&
            (title match $searchQuery + "*" ||  
             description match $searchQuery + "*" ||  
             body[].children[].text match $searchQuery + "*") &&  
            (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
          ]
        )
      }
      `);

export const moreSearchQueryDesc = defineQuery(`
{
  "items": *[  
    _type in ["dailyStoryArticle", "liveBlogArticle"] && 
    agency == '${workspace}' && 
    !(_id in path("drafts.**")) &&  
    defined(dateTime(publishedDate)) &&
    (title match $searchQuery + "*" ||  
     description match $searchQuery + "*" ||  
     body[].children[].text match $searchQuery + "*") &&  
    (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
  ]  
  | order(publishedDate desc) [$start...$end]{
    title,  
    "slug": slug.current,
    description,  
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "media": featuredMedia[0] {
      "url": secure_url,
      "type": resource_type
    },
    "publishedDate": dateTime(publishedDate),  
    authors[]->{_id, "name": fullName, "slug": slug.current, image { "url": secure_url }},
  },
  "totalCount": count(
    *[_type in ["dailyStoryArticle", "liveBlogArticle"] && 
    agency == '${workspace}' && 
      !(_id in path("drafts.**")) &&  
      defined(dateTime(publishedDate)) &&
      (title match $searchQuery + "*" ||  
       description match $searchQuery + "*" ||  
       body[].children[].text match $searchQuery + "*") &&  
     (!defined($startDate) || dateTime(publishedDate) >= dateTime($startDate)) &&
(!defined($endDate) || dateTime(publishedDate) <= dateTime($endDate))
    ]
  )
}
`);

type Article = {
  publishedDate: string;
  title: string;
  slug: string;
  description: string;
  category: {
    id: string;
    title: string;
    slug: string;
  };
  subcategory?: {
    id: string;
    title: string;
    slug: string;
  };
  media: {
    url: string;
    type: string;
    created_at: string;
  };
  authors: {
    _id: string;
    name: string;
    slug: string;
    image?: {
      url: string;
    };
  }[];
};

type SearchResponse = {
  articlesCount: number;
  articles: Article[];
};

export type { Article, SearchResponse };
