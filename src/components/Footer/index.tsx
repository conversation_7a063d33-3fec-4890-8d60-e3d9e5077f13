import FooterDesktop, { FooterDesktopProps } from "./footerDesktop";
import FooterMobile from "./footerMobile";

const Footer = ({
  navigationMenu,
  socialMediaItems,
  complianceLinkItems,
  donationLink,
  isFooterReduced,
}: FooterDesktopProps) => {
  return (
    <>
      <div className="block lg:hidden">
        <FooterMobile
          socialMediaItems={socialMediaItems}
          complianceLinkItems={complianceLinkItems}
          isFooterReduced={isFooterReduced}
          donationLink={donationLink}
        />
      </div>

      <div className="hidden lg:block">
        <FooterDesktop
          navigationMenu={navigationMenu}
          socialMediaItems={socialMediaItems}
          complianceLinkItems={complianceLinkItems}
          donationLink={donationLink}
          isFooterReduced={isFooterReduced}
        />
      </div>
    </>
  );
};

export default Footer;
