import { auth0 } from "@/lib/auth0";

export async function getAuth0UserId() {
  const session = await auth0.getSession();
  return session?.user?.sub ?? null;
}

export async function extractParam(
  params: Promise<{ [key: string]: string }>,
  paramName: string,
): Promise<string | null> {
  const paramObj = await params;
  return paramObj[paramName] ?? null;
}

export const getSlugFromArticleUrl = (url: string): string | null => {
  try {
    const { pathname } = new URL(url);
    const segments = pathname.split("/").filter(Boolean);
    return segments.length ? segments[segments.length - 1] : null;
  } catch (error) {
    console.error("Invalid URL passed to getSlugFromArticleUrl:", url, error);
    return null;
  }
};
