import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const tagQuery = `
  *[
    _type == "tag" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    slug.current == $slug
  ][0] {
    "id": _id,
    title,
    "slug": slug.current,
    "image": image.secure_url,
    seoMetadata,
    subtitle,
    "articlesCount": count(*[
      _type == "dailyStoryArticle" && 
      agency == '${workspace}' &&
      ^._id in tags[]._ref
    ]),
    "articles": *[
        _type == "dailyStoryArticle" && 
        agency == '${workspace}' &&
        ^._id in tags[]._ref
    ] | order(publishedDate desc) [0...10] {
      "id": _id,
      title,
      "slug": slug.current,
      publishedDate,
      description,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type
      },
      "authors": authors[]-> {
          "slug": slug.current,
          "name": fullName,
          image {
          "url": secure_url
          }
      },
      "created_at": _createdAt,
    }
  }
`;

export const moreArticlesQuery = defineQuery(`
  {
    "items": *[
      _type == "dailyStoryArticle" && 
      agency == '${workspace}' &&
      !(_id in path("drafts.**")) &&
      $tagId in tags[]._ref
    ] | order(publishedDate desc) [$start...$end] {
      "id": _id,
      title,
      "slug": slug.current,
      publishedDate,
      description,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
      "created_at": _createdAt
    },
    "totalCount": count(*[
      _type == "dailyStoryArticle" && 
      agency == '${workspace}' &&
      !(_id in path("drafts.**")) &&
      $tagId in tags[]._ref
    ])
  }
`);

export const allTagsQuery = `
  *[_type == "tag" && 
    !(_id in path("drafts.**"))] {
  "id": _id,
  "name": title
}
`;

export const articlesByTagsQuery = `
*[
  _type == "dailyStoryArticle" &&
  agency == '${workspace}' &&
  !(_id in path("drafts.**")) &&
  defined(tags) &&
  count(tags[@._ref in $tagIds]) > 0
] | order(publishedDate desc)[0...30] {
  "id": _id,
  title,
  "slug": slug.current,
  publishedDate,
  description,
  "category": category-> {
    "id": _id,
    title,
    "slug": slug.current
  },
  "media": featuredMedia[0] {
    "url": secure_url,
    "type": resource_type
  },
  "authors": authors[]-> {
    "slug": slug.current,
    "name": fullName,
    image {
      "url": secure_url
    }
  },
  "created_at": _createdAt,
  "updated_at": dateTime(_updatedAt),
  "subcategory": subcategory-> {
    "id": _id,
    title,
    "slug": slug.current
  }
}
`;

export type SearchTag = {
  id: string;
  name: string;
};

export type Tag = {
  id: string;
  title: string;
  slug: string;
  subtitle: string;
  articles: Article[];
  articlesCount: number;
};

export type Article = {
  id: string;
  title: string;
  slug: string;
  publishedDate: string;
  description: string;
  category: {
    id: string;
    title: string;
    slug: string;
  };
  subcategory?: {
    id: string;
    title: string;
    slug: string;
  };
  media: {
    url: string;
    type: string;
  };
  created_at: string;
  authors: Author[];
};

export type Author = {
  slug: string;
  name: string;
  image?: {
    url: string;
  };
};
