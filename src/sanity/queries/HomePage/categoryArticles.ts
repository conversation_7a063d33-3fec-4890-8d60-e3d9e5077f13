import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const categoryArticlesQuery = defineQuery(`
  *[
    _type == "dailyStoryArticle" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    defined(category) &&
    category->_id != null &&
    category->slug.current == $category
  ] {
    "id": _id,
    title,
    "excerpt": description,
    "slug": slug.current,
    "publishedDate": publishedDate,
    "created_at": _createdAt,
    "featuredMedia": featuredMedia[0],
    "author": authors[0] -> {
      "slug": slug.current,
      "name": fullName,
      "image": image {
        "url": secure_url
      }
    },
    "media": featuredMedia[0] {
      "url": secure_url,
      "type": resource_type,
      "text": context.custom.caption
    },
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current,
      description
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current,
    },
    "firstTag": tags[]-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "isPinned": _id in *[_type=="category" && agency == '${workspace}' && slug.current == $category][0].pinnedArticles[]._ref
  } | order(isPinned desc, publishedDate desc) [0...$limit]
`);

export type Media = {
  url: string;
  type: string;
  text?: string;
};

export type Author = {
  slug: string;
  name: string;
  image?: {
    url: string;
  };
};

export type Category = {
  id: string;
  title: string;
  slug: string;
  description?: string;
};

export type Subcategory = {
  id: string;
  title: string;
  slug: string;
};

export type Tag = {
  id: string;
  title: string;
  slug: string;
};

export type Article = {
  id: string;
  slug?: string;
  subcategory?: Subcategory;
  title: string;
  excerpt?: string;
  author: Author;
  publishedDate: string;
  created_at: string;
  media?: Media;
  category: Category;
  firstTag: Tag;
};
