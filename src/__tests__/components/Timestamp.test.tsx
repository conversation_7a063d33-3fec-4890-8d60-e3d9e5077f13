import { render } from "@testing-library/react";
import Timestamp from "@/components/Timestamp";

describe("Timestamp", () => {
  beforeEach(() => {
    // Mock current date to 2024-02-05 15:00:00
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2024-02-05T15:00:00"));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test("shows relative time for timestamps within 12 hours", () => {
    const { getByText } = render(<Timestamp date="2024-02-05T14:30:00" />);
    expect(getByText("30 mins ago")).toBeInTheDocument();
  });

  test("shows clock format for same day timestamps over 12 hours", () => {
    const { getByText } = render(<Timestamp date="2024-02-05T02:00:00" />);
    expect(getByText(/2:00.*EST/i)).toBeInTheDocument();
  });

  test("shows Yesterday for previous day timestamps", () => {
    const { getByText } = render(<Timestamp date="2024-02-04T15:00:00" />);
    expect(getByText("Yesterday")).toBeInTheDocument();
  });

  test("shows full date for timestamps older than 2 days", () => {
    const { getByText } = render(<Timestamp date="2024-02-01T15:00:00" />);
    expect(getByText("February 1st, 2024")).toBeInTheDocument();
  });

  test("returns null if no date provided", () => {
    const { container } = render(<Timestamp />);
    expect(container).toBeEmptyDOMElement();
  });

  test("shows minutes for timestamps less than an hour ago", () => {
    const { getByText } = render(<Timestamp date="2024-02-05T14:05:00" />);
    expect(getByText("55 mins ago")).toBeInTheDocument();
  });

  test("shows hours for timestamps more than an hour ago", () => {
    const { getByText } = render(<Timestamp date="2024-02-05T13:00:00" />);
    expect(getByText("2 hours ago")).toBeInTheDocument();
  });
});
