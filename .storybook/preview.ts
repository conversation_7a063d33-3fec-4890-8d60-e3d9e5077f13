import type { Preview } from "@storybook/react";
import "../src/styles/globals.css";
import "material-symbols";

const preview: Preview = {
  parameters: {
    backgrounds: {
      default: "black",
      values: [
        { name: "black", value: "#000000" },
        { name: "white", value: "#ffffff" },
      ],
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
