"use client";
import { useForm, Controller } from "react-hook-form";
import Input from "@/components/ui/Form/Input";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Form/Form/textarea";
import { FormValues } from "@/types/contact-us";

interface ContactUsFormProps {
  onSubmit: (data: FormValues) => void;
  isSubmitting: boolean;
}

export default function ContactUsForm({
  onSubmit,
  isSubmitting,
}: ContactUsFormProps) {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      name: "",
      lastName: "",
      phoneNumber: "",
      email: "",
      message: "",
    },
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-4 lg:mt-6">
      <Controller
        name="name"
        control={control}
        rules={{
          required: "Please enter your first name",
          maxLength: {
            value: 50,
            message: "First name cannot exceed 50 characters",
          },
          minLength: {
            value: 2,
            message: "First name must be at least 2 characters",
          },
          validate: (value) =>
            value.trim().length > 0 ||
            "First name cannot be empty or contain only spaces",
        }}
        render={({ field }) => (
          <Input
            label="First Name"
            placeholder="First Name"
            value={field.value}
            onChange={field.onChange}
            error={errors.name?.message}
            disabled={isSubmitting}
          />
        )}
      />

      <Controller
        name="lastName"
        control={control}
        rules={{
          required: "Please enter your last name",
          maxLength: {
            value: 50,
            message: "Last name cannot exceed 50 characters",
          },
          minLength: {
            value: 2,
            message: "Last name must be at least 2 characters",
          },
          validate: (value) =>
            value.trim().length > 0 ||
            "Last name cannot be empty or contain only spaces",
        }}
        render={({ field }) => (
          <Input
            label="Last Name"
            placeholder="Last Name"
            value={field.value}
            onChange={field.onChange}
            error={errors.lastName?.message}
            disabled={isSubmitting}
          />
        )}
      />

      <Controller
        name="phoneNumber"
        control={control}
        rules={{
          required: "Please enter your phone number",
          maxLength: {
            value: 20,
            message: "Phone number cannot exceed 15 characters",
          },
          pattern: {
            value: /^[+\d\s\-()]*$/,
            message:
              "Phone number can only have numeric values, spaces and the following symbol: '+', '-', '(' and ')'",
          },
          minLength: {
            value: 2,
            message: "Phone number must be at least 2 characters",
          },
          validate: (value) =>
            value.trim().length > 0 ||
            "Phone number cannot be empty or contain only spaces",
        }}
        render={({ field }) => (
          <Input
            label="Phone Number"
            placeholder="Phone Number"
            value={field.value}
            onChange={field.onChange}
            error={errors.phoneNumber?.message}
            disabled={isSubmitting}
          />
        )}
      />

      <Controller
        name="email"
        control={control}
        rules={{
          required: "Please enter your email",
          maxLength: {
            value: 254,
            message: "Email cannot exceed 254 characters",
          },
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: "Please enter a valid email address",
          },
        }}
        render={({ field }) => (
          <Input
            label="Email"
            placeholder="Email"
            value={field.value}
            onChange={field.onChange}
            error={errors.email?.message}
            disabled={isSubmitting}
          />
        )}
      />

      <Controller
        name="message"
        control={control}
        rules={{
          required: "Please enter your message",
          maxLength: {
            value: 2000,
            message: "Message cannot exceed 2000 characters",
          },
          minLength: {
            value: 2,
            message: "Message must be at least 2 characters",
          },
          validate: (value) =>
            value.trim().length > 0 ||
            "Message cannot be empty or contain only spaces",
        }}
        render={({ field }) => (
          <Textarea
            label="Message"
            value={field.value}
            onChange={field.onChange}
            error={errors.message?.message}
            className="min-h-[200px] resize-none text-base"
            disabled={isSubmitting}
          />
        )}
      />

      <div>
        <Button
          type="submit"
          className="w-full lg:w-36 mx-auto"
          state={isSubmitting ? "disabled" : "enabled"}
        >
          {!isSubmitting ? "Send message" : "Sending..."}
        </Button>
      </div>
    </form>
  );
}
