import { render, screen } from "@testing-library/react";
import { Author } from "@/sanity/queries/dailyStory";
import AuthorsSection from "@/components/Author/AuthorsSection";

const mockAuthors: <AUTHORS>
  {
    slug: "author-1",
    name: "Author One",
    image: { url: "/author1.jpg", resource_type: "image", type: "jpg" },
  },
  {
    slug: "author-2",
    name: "Author Two",
    image: { url: "/author2.jpg", resource_type: "image", type: "jpg" },
  },
  {
    slug: "author-3",
    name: "Author Three",
    image: { url: "/author3.jpg", resource_type: "image", type: "jpg" },
  },
];

describe("AuthorsSection Component", () => {
  test("renders a single author correctly", () => {
    render(
      <AuthorsSection
        authors={[mockAuthors[0]]}
        publishedDate="2024-03-10"
        _updatedAt="2024-03-11"
      />,
    );

    expect(screen.getByText("Author One")).toBeInTheDocument();
    expect(
      screen.queryByRole("button", { name: /arrow_back/i }),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByRole("button", { name: /arrow_forward/i }),
    ).not.toBeInTheDocument();
  });

  test("renders two authors statically (not in Swiper)", () => {
    render(
      <AuthorsSection
        authors={mockAuthors.slice(0, 2)}
        publishedDate="2024-03-10"
        _updatedAt="2024-03-11"
      />,
    );

    expect(screen.getByText("Author One")).toBeInTheDocument();
    expect(screen.getByText("Author Two")).toBeInTheDocument();
    expect(
      screen.queryByRole("button", { name: /arrow_back/i }),
    ).not.toBeInTheDocument();
    expect(
      screen.queryByRole("button", { name: /arrow_forward/i }),
    ).not.toBeInTheDocument();
  });

  test("renders multiple authors inside Swiper and shows navigation buttons", () => {
    render(
      <AuthorsSection
        authors={mockAuthors}
        publishedDate="2024-03-10"
        _updatedAt="2024-03-11"
      />,
    );

    expect(screen.getByText("Author One")).toBeInTheDocument();
    expect(screen.getByText("Author Two")).toBeInTheDocument();
    expect(screen.getByText("Author Three")).toBeInTheDocument();
  });
});
