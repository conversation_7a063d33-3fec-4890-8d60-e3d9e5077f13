import { createClient, errorCodes } from "@/app/api/util/db/supabase";
import { workspace } from "@/sanity/env";

export async function addBookmark(
  user_id: string,
  article_id: string,
): Promise<void> {
  const supabase = await createClient();
  const { error, status } = await supabase
    .from("article_bookmarks")
    .insert([{ user_id, article_id, agency: workspace }]);

  if (error) {
    console.log(error);
    if (status === errorCodes.ALREADY_EXIST_STATUS) {
      throw new Error("Already bookmarked");
    }
    if (status === errorCodes.FORBIDDEN) {
      throw new Error("Forbidden");
    }
    throw new Error("Unexpected database error");
  }
}

export async function removeBookmark(
  user_id: string,
  article_id: string,
): Promise<void> {
  const supabase = await createClient();
  const { error, status } = await supabase
    .from("article_bookmarks")
    .delete()
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .eq("article_id", article_id);

  if (error) {
    if (status === errorCodes.NOT_FOUND) {
      throw new Error("Not found");
    }
    throw new Error("Unexpected database error");
  }
}

export async function getBookmarkCount(
  article_id: string,
): Promise<{ bookmark_count: number }> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("article_bookmark_counts")
    .select("bookmark_count")
    .eq("agency", workspace)
    .eq("article_id", article_id)
    .maybeSingle();

  if (error) {
    throw new Error("Unexpected database error");
  }
  return data || { bookmark_count: 0 };
}

export async function getUserBookmark(
  user_id: string,
  article_id: string,
): Promise<{ id: string; user_id: string; article_id: string }> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("article_bookmarks")
    .select("*")
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .eq("article_id", article_id)
    .maybeSingle();

  if (error) {
    throw new Error("Unexpected database error");
  }

  return data;
}

export async function getUserBookmarks(
  user_id: string,
  start: number,
  end: number,
): Promise<{
  data: { id: string; user_id: string; article_id: string; created_at: Date }[];
  totalCount: number;
}> {
  const supabase = await createClient();

  const { data, error, count } = await supabase
    .from("article_bookmarks")
    .select("*", { count: "exact" })
    .eq("agency", workspace)
    .eq("user_id", user_id)
    .order("created_at", { ascending: false })
    .range(start, end - 1);

  if (error) {
    throw new Error("Unexpected database error");
  }

  return {
    data: data ?? [],
    totalCount: count ?? 0,
  };
}
