import React from "react";

interface SkeletonProps {
  imagePosition?: "top" | "left" | "right";
  showTitle?: boolean;
  showSubTitle?: boolean;
  showDescription?: boolean;
  showImage?: boolean;
  overrideImageWidth?: boolean;
  imageWidth?: string;
  imageHeight?: string;
  imageClass?: string;
  titleHeight?: string;
  titleWidth?: string;
  subtitleHeight?: string;
  subtitleWidth?: string;
  descriptionLineCount?: number;
  descriptionLineHeight?: string;
  descriptionWidths?: string[];
}

const Skeleton: React.FC<SkeletonProps> = ({
  imagePosition = "top",
  showTitle = true,
  showSubTitle = false,
  showDescription = true,
  showImage = true,
  overrideImageWidth = false,
  imageWidth = "18rem",
  imageHeight = "18rem",
  imageClass = "",
  titleHeight = "1.5rem",
  titleWidth = "75%",
  subtitleHeight = "1rem",
  subtitleWidth = "25%",
  descriptionLineCount = 2,
  descriptionLineHeight = "1rem",
  descriptionWidths = [],
}) => {
  const isHorizontal = imagePosition === "left" || imagePosition === "right";

  const shimmerClass =
    "bg-[linear-gradient(90deg,#e0e0e0_25%,#f5f5f5_50%,#e0e0e0_75%)] bg-[length:200%_100%] bg-gray-300 animate-shimmer";

  return (
    <div
      className={`flex ${
        imagePosition === "right"
          ? "flex-row-reverse"
          : isHorizontal
            ? "flex-row"
            : "flex-col"
      } items-center gap-4`}
      aria-hidden="true"
    >
      {showImage && (
        <div
          className={`${shimmerClass} ${imageClass} rounded-2xl`}
          style={{
            width: isHorizontal || overrideImageWidth ? imageWidth : "100%",
            height: imageHeight,
          }}
        />
      )}
      <div className="flex flex-col flex-1 gap-2 w-full self-start">
        {showSubTitle && (
          <div
            className={`${shimmerClass} rounded`}
            style={{
              height: subtitleHeight,
              width: subtitleWidth,
            }}
          />
        )}
        {showTitle && (
          <div
            className={`${shimmerClass} rounded`}
            style={{
              height: titleHeight,
              width: titleWidth,
            }}
          />
        )}
        {showDescription &&
          [...Array(descriptionLineCount)].map((_, i) => (
            <div
              key={i}
              className={`${shimmerClass} rounded`}
              style={{
                height: descriptionLineHeight,
                width: descriptionWidths[i] || "100%",
              }}
            />
          ))}
      </div>
    </div>
  );
};

export default Skeleton;
