import { sanityFetch } from "@/sanity/client";
import { sanityFetch as sanityLiveFetch } from "@/sanity/live";
import { breakingNewsQuery } from "@/sanity/queries/HomePage/breakingNews";
import HomePageClient from "@/app/HomePageClient";
import {
  homePageQuery,
  HomePageResult,
  HomeSection,
} from "@/sanity/queries/HomePage/homePage";
import {
  latestArticlesHomePageQuery,
  latestLiveBlogsHomePageQuery,
  HomePageArticle,
} from "@/sanity/queries/latest-news";
import { Metadata } from "next";
import {
  fetchTrendingArticlesFromAnalytics,
  fillHomeSectionsWithTrendingArticles,
} from "@/app/api/trending";
import { buildWebsitePageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata(
    "",
    "Catholic News Agency",
    "Daily news about <PERSON>, the Vatican and the Catholic Church",
  );
}

async function getSubcategoryArticles(
  subcategory: HomePageResult["rightColumn"]["subcategory"],
  sections: Extract<
    HomeSection,
    { _type: "categoryWithSubcategoriesSection" }
  >[],
) {
  const matchingSection = sections.find((section) =>
    section.category?.subcategories?.some((x) => x._id === subcategory.id),
  );

  if (matchingSection) {
    const matchedSubcategory = matchingSection.category?.subcategories?.find(
      (x) => x._id === subcategory.id,
    );
    const articles = matchedSubcategory?.articles || [];

    if (articles.length > 0) {
      return articles;
    }
  }

  return await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: subcategory.category?.slug || null,
    subcategorySlug: subcategory.slug || null,
    periodEnd: undefined,
    periodStart: undefined,
  });
}

async function getCategoryArticles(
  category: HomePageResult["rightColumn"]["category"],
  sections: Extract<HomeSection, { _type: "categorySection" }>[],
) {
  const matchingSection = sections.find(
    (section) => section.category?.slug === category.slug,
  );

  if (matchingSection) {
    return matchingSection.category?.articles || [];
  }

  return await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: category.slug,
    subcategorySlug: null,
    periodEnd: undefined,
    periodStart: undefined,
  });
}

async function populateRightColumnArticles(
  rightColumn: HomePageResult["rightColumn"],
  dataWithSections: HomePageResult,
) {
  if (rightColumn.isSubcategory) {
    const subcategorySections =
      dataWithSections?.sections?.filter(
        (section) => section._type === "categoryWithSubcategoriesSection",
      ) || [];

    rightColumn.subcategory.articles = await getSubcategoryArticles(
      rightColumn.subcategory,
      subcategorySections,
    );
  } else {
    const categorySections =
      dataWithSections?.sections?.filter(
        (section) => section._type === "categorySection",
      ) || [];

    rightColumn.category.articles = await getCategoryArticles(
      rightColumn.category,
      categorySections,
    );
  }
}

async function fetchBreakingNews() {
  return await sanityLiveFetch({ query: breakingNewsQuery });
}

async function fetchHomePage() {
  const data = await sanityFetch<HomePageResult>({
    query: homePageQuery,
    params: {},
    tags: [],
  });
  const dataWithSections = await fillHomeSectionsWithTrendingArticles(data);
  const trendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 12,
    categorySlug: null,
    subcategorySlug: null,
    periodEnd: undefined,
    periodStart: undefined,
  });
  const rightColumn = data.rightColumn;
  await populateRightColumnArticles(rightColumn, dataWithSections);
  return { ...dataWithSections, rightColumn, trendingArticles };
}

async function fetchLatestNews() {
  const latestArticles = await sanityFetch<HomePageArticle[]>({
    query: latestArticlesHomePageQuery,
    params: {},
    tags: [],
  });
  const latestLiveBlogs = await sanityFetch<HomePageArticle[]>({
    query: latestLiveBlogsHomePageQuery,
    params: {},
    tags: [],
  });
  if (!latestArticles && !latestLiveBlogs) {
    return { articles: [] };
  }
  const combined = [...latestArticles, ...latestLiveBlogs];

  const articles = combined
    .sort(
      (a, b) => new Date(b.sortDate).getTime() - new Date(a.sortDate).getTime(),
    )
    .slice(0, 5);
  return {
    articles,
  };
}

export default async function HomePage() {
  const homePageData = await fetchHomePage();
  const latestNewsData = await fetchLatestNews();
  const { data: breakingNews } = await fetchBreakingNews();

  const jsonLd = buildWebsitePageJsonLd(
    "EWTN News",
    "",
    "Daily news about Pope Francis, the Vatican and the Catholic Church",
  );
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <HomePageClient
        homePage={homePageData}
        breakingNews={breakingNews}
        latestNews={latestNewsData}
      />
    </>
  );
}
