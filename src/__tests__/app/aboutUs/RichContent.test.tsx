import { render, screen } from "@testing-library/react";
import RichContent from "@/app/about-us/richContent";

describe("RichContent", () => {
  const mockContent = [
    {
      _type: "block",
      style: "normal",
      _key: "123",
      children: [
        {
          _type: "span",
          marks: [],
          text: "Test content",
          _key: "456",
        },
      ],
    },
  ];

  test("renders basic content", () => {
    render(<RichContent content={mockContent} firstLetterBig={false} />);
    expect(screen.getByText("Test content")).toBeInTheDocument();
  });

  test("renders content with first letter big", () => {
    render(<RichContent content={mockContent} firstLetterBig={true} />);
    const element = screen.getByText("Test content");
    expect(element).toHaveClass("first-letter:text-5xl");
  });

  test("renders timeline block", () => {
    const timelineContent = [
      {
        _type: "timeline",
        _key: "789",
        items: [
          {
            _key: "101",
            title: "Test Event",
            year: 2023,
            highlighted: false,
          },
        ],
      },
    ];

    render(<RichContent content={timelineContent} firstLetterBig={false} />);
    expect(screen.getByText("Test Event")).toBeInTheDocument();
    expect(screen.getByText("2023")).toBeInTheDocument();
  });

  test("renders gray area block", () => {
    const grayAreaContent = [
      {
        _type: "grayArea",
        imageUrl: "/test.jpg",
        description: [
          {
            _type: "block",
            children: [
              {
                _type: "span",
                marks: [],
                text: "Gray area description",
                _key: "202",
              },
            ],
          },
        ],
      },
    ];

    render(<RichContent content={grayAreaContent} firstLetterBig={false} />);
    expect(screen.getByText("Gray area description")).toBeInTheDocument();
  });
});
