"use client";

import { useState, useRef } from "react";
import { BreakingNewsResult } from "@/sanity/queries/HomePage/breakingNews";
import Timestamp from "@/components/Timestamp";
import { Icon } from "@/components/Icon";
import VideoPlayer from "@/components/ui/VideoPlayer";
import Link from "next/link";
import { getArticleUrl, truncateTitle } from "@/utils/utils";
import Image from "next/image";

interface BreakingNewsProps {
  breakingNews: NonNullable<BreakingNewsResult>;
}

export default function BreakingNews({ breakingNews }: BreakingNewsProps) {
  const media =
    breakingNews.media ?? breakingNews.relatedLiveBlogArticle?.media;
  const title =
    breakingNews.title ?? breakingNews.relatedLiveBlogArticle?.title;
  const lastUpdate = breakingNews.relatedLiveBlogArticle
    ? breakingNews.relatedLiveBlogArticle._updatedAt
    : breakingNews.lastUpdate;
  const isVideo = media?.type === "video";
  const [mediaExpanded, setMediaExpanded] = useState(false);
  const [isClose, setIsClose] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleClose = () => {
    if (containerRef.current) {
      const currentHeight = containerRef.current.clientHeight;
      containerRef.current.style.height = currentHeight + "px";

      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.style.height = "0px";
        }
      }, 1);

      setTimeout(() => {
        setIsClose(true);
      }, 500);
    }
  };

  return (
    !isClose && (
      <div
        ref={containerRef}
        className={`w-full bg-black text-white transition-all duration-500 overflow-hidden`}
        data-testid="breaking-news-main"
      >
        <div className="container mx-auto relative p-4">
          <button
            onClick={() => handleClose()}
            className="absolute top-4 right-4 w-6 lg:w-9 h-6 lg:h-9 rounded-full border border-white/50 transition-all hover:border-white bg-black flex items-center justify-center z-20"
            data-testid="close-breaking-news"
          >
            <Icon icon="close" className="!text-[20px] lg:!text-[24px]" />
          </button>

          <div
            className={`${mediaExpanded ? "hidden" : "flex items-center gap-2 md:hidden mb-4"}`}
          >
            <span className="font-bold text-[14px]">BREAKING NEWS</span>

            <span className="text-[11px] leading-none">
              Updated <Timestamp date={lastUpdate} />
            </span>
          </div>

          <div
            className={`container mx-auto lg:items-center gap-4 relative ${mediaExpanded ? "flex-col lg:flex lg:flex-row" : "flex"}`}
          >
            {media && (
              <div
                className={`relative transition-all duration-300 ${mediaExpanded ? "lg:pr-0 w-[calc(100%-2rem)] lg:w-[600px]" : "w-28 lg:w-56"}`}
              >
                {isVideo ? (
                  <button
                    className="w-full outline-none block"
                    aria-label="Play video"
                    onClick={() => !mediaExpanded && setMediaExpanded(true)}
                  >
                    {!mediaExpanded && (
                      <div className="absolute bottom-2 lg:bottom-4 left-2 lg:left-4 w-6 lg:w-9 h-6 lg:h-9 rounded-md lg:rounded-xl bg-black/70 flex items-center justify-center">
                        <Icon icon="play" className="w-3 lg:w-5" />
                      </div>
                    )}

                    <div
                      className={` transition-all duration-300${mediaExpanded ? "w-[calc(100%)] lg:w-[600px]" : "w-28 lg:w-56"}`}
                    >
                      <VideoPlayer
                        loop={true}
                        src={media.url}
                        controls={mediaExpanded}
                        onPause={() => setMediaExpanded(false)}
                        pauseOnClickOutside={true}
                      />
                    </div>
                  </button>
                ) : (
                  <button
                    className="w-full outline-none block"
                    aria-label="Play video"
                    onClick={() => setMediaExpanded((self) => !self)}
                  >
                    <div className="absolute top-2 lg:top-4 right-2 lg:right-4 w-6 lg:w-9 h-6 lg:h-9 rounded-md lg:rounded-xl bg-black/70 flex items-center justify-center">
                      <Icon
                        icon={
                          mediaExpanded
                            ? "collapseMediaSquare"
                            : "expandMediaSquare"
                        }
                        className="w-3 lg:w-5"
                      />
                    </div>

                    <Image
                      src={media.url}
                      alt={breakingNews.title}
                      className={`object-cover transition-all duration-300 ${mediaExpanded ? "w-[calc(100%)] lg:w-[600px]" : "w-28 lg:w-56"}`}
                      layout="responsive"
                      width={16}
                      height={9}
                    />
                  </button>
                )}
              </div>
            )}

            <div className="flex-1">
              <div
                className={`${mediaExpanded || !media ? "flex mb-1 lg:mb-0 mt-4" : "hidden md:flex"} items-center gap-2`}
              >
                <span className="font-bold text-[14px] lg:text-xl">
                  BREAKING NEWS
                </span>

                <span className="text-[11px] lg:text-[13px] leading-none">
                  Updated <Timestamp date={lastUpdate} />
                </span>
              </div>

              <h2 className="text-[18px] leading-[24px] lg:text-[42px] lg:leading-[48px] font-titles font-semibold leading-0 mb-1 lg:mb-2">
                {truncateTitle(title)}
              </h2>

              <div>
                {breakingNews.relatedLiveBlogArticle?.slug &&
                breakingNews.relatedLiveBlogArticle?.category?.slug ? (
                  <Link
                    href={getArticleUrl(
                      breakingNews.relatedLiveBlogArticle.category.slug.current,
                      breakingNews.relatedLiveBlogArticle.slug.current,
                      breakingNews.relatedLiveBlogArticle.subcategory?.slug
                        .current,
                    )}
                    className="text-xs lg:text-15px hover-underline-animation-white"
                  >
                    See full coverage
                    <Icon icon="seeMore" className="absolute" />
                  </Link>
                ) : (
                  "(Developing story)"
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  );
}
