import { ReactNode, useState } from "react";

interface IconButtonProps {
  icon: (hover: boolean) => ReactNode;
  size?: number;
  bgColor?: string;
  hoverBgColor?: string;
}

const ShareIconButton = ({
  icon,
  size = 34,
  bgColor = "transparent",
  hoverBgColor = "#E3E3E3",
}: IconButtonProps) => {
  const [hover, setHover] = useState(false);

  return (
    <div
      className="flex items-center justify-center rounded-full transition-colors"
      style={{
        width: size,
        height: size,
        backgroundColor: hover ? hoverBgColor : bgColor,
      }}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {icon(hover)}
    </div>
  );
};

export default ShareIconButton;
