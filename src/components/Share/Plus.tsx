import { useState, useEffect, useRef } from "react";
import ShareIconButton from "./ShareIconButton";
import LinkedinButton from "./Linkedin";
import PinterestButton from "./Pinterest";
import Facebook<PERSON>essengerButton from "./FacebookMessenger";
import TelegramButton from "./Telegram";
import { Icon } from "../Icon";

const PlusShareButton = ({
  shareUrl,
  size = 34,
  articleTitle,
  articleImageUrl,
}: {
  shareUrl: string;
  size?: number;
  articleTitle?: string;
  articleImageUrl?: string;
}) => {
  const [expanded, setExpanded] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const toggleExpanded = () => {
    setExpanded((prev) => !prev);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      containerRef.current &&
      !containerRef.current.contains(event.target as Node)
    ) {
      setExpanded(false);
    }
  };

  useEffect(() => {
    if (expanded) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [expanded]);

  return (
    <div className="relative inline-block" ref={containerRef}>
      <div
        className="flex items-center justify-center rounded-full cursor-pointer"
        style={{
          width: size,
          height: size,
        }}
        onClick={toggleExpanded}
      >
        <ShareIconButton icon={() => <Icon icon="plus" size={24} />} />
      </div>

      {expanded && (
        <div
          className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 p-2 w-full bg-white rounded-lg shadow-lg z-[100]"
          style={{
            minWidth: size * 1.5,
          }}
        >
          <div className="flex flex-col items-center space-y-4">
            <FacebookMessengerButton shareUrl={shareUrl} />
            <TelegramButton shareUrl={shareUrl} />
            <LinkedinButton shareUrl={shareUrl} />
            {articleTitle && articleImageUrl && (
              <PinterestButton
                shareUrl={shareUrl}
                articleTitle={articleTitle}
                articleImageUrl={articleImageUrl}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PlusShareButton;
