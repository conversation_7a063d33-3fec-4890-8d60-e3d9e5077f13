"use client";

import { StaticTabbedPageQueryResult } from "@/sanity/queries/static-tabbed-page";
import { Fragment, useEffect } from "react";
import Button from "@/components/ui/Button";
import RichContent, { Content } from "./richContent";
import Image from "next/image";

type Props = {
  pageData: StaticTabbedPageQueryResult;
};

export default function AboutUsClient({ pageData }: Props) {
  const handleHashChange = (section = "") => {
    if (section || window.location.hash) {
      const targetId = section.length
        ? section
        : window.location.hash.substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        const elementPosition = targetElement.getBoundingClientRect().top;
        const offset = 170;
        const scrollPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
          top: scrollPosition,
          behavior: "smooth",
        });
      }
    }
  };

  useEffect(() => {
    handleHashChange();
  }, []);

  const handleButtonClick = (section: string) => {
    window.history.pushState(null, "", `#${section}`);

    handleHashChange(section);
  };

  return (
    <main className="container mx-auto px-4 lg:px-8 py-8 mb-6">
      <h2 className="font-titles lg:-mx-4 text-[25px] lg:text-[34px] font-semibold">
        {pageData.title}
      </h2>

      <div className="relative -mx-4 mt-2">
        <nav className="flex gap-2 overflow-x-auto hide-scrollbar px-4 lg:px-0">
          {pageData.content.tabBody.map((tab, index) => (
            <div key={index}>
              {tab.slug.current.includes("http") ? (
                <Button
                  variant="secondary"
                  size="xs"
                  className="w-full rounded-full py-2 !text-[14px]"
                  href={tab.slug.current}
                  target="_blank"
                  role="tab"
                  iconName="newWindow"
                  iconPosition="right"
                >
                  {tab.title}
                </Button>
              ) : (
                <Button
                  variant="secondary"
                  size="xs"
                  className="w-full rounded-full py-2 !text-[14px]"
                  onClick={() => handleButtonClick(tab.slug.current)}
                  role="tab"
                >
                  {tab.title}
                </Button>
              )}
            </div>
          ))}
        </nav>

        <figure className="mt-4">
          <Image
            src={pageData.imageUrl}
            alt="About Us"
            layout="responsive"
            width={16}
            height={9}
          />
        </figure>
      </div>

      <div className="max-w-[850px] mx-auto">
        <div className="mt-4 lg:mt-8">
          <RichContent
            content={pageData.description as Content[]}
            firstLetterBig={true}
          />
        </div>

        {pageData.content.tabBody.map((tab) => (
          <Fragment key={tab.slug.current}>
            {!tab.slug.current.includes("http") ? (
              <div id={tab.slug.current}>
                <div className="flex items-center gap-2">
                  {tab.imageUrl ? (
                    <figure className="mt-4 lg:mt-8">
                      <Image
                        src={tab.imageUrl}
                        alt={tab.title}
                        layout="responsive"
                        width={16}
                        height={9}
                      />
                    </figure>
                  ) : (
                    ""
                  )}
                  <h2
                    className={`font-titles  font-semibold mt-4 lg:mt-8 text-[20px] ${tab.imageUrl ? "lg:text-[30px]" : "lg:text-[42px]"}`}
                  >
                    {tab.title}
                  </h2>
                </div>

                <RichContent content={tab.textBody as Content[]} />
              </div>
            ) : (
              ""
            )}
          </Fragment>
        ))}
      </div>
    </main>
  );
}
