import { Auth0Client } from "@auth0/nextjs-auth0/server";

function computeBaseUrl() {
  if (process.env.APP_BASE_URL) return process.env.APP_BASE_URL;
  if (process.env.VERCEL_BRANCH_URL) return `https://${process.env.VERCEL_BRANCH_URL}`;
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;
  return "http://localhost:3000";
}

export const auth0 = new Auth0Client({
  appBaseUrl: computeBaseUrl(),
});
