"use client";

import Image from "next/image";
import Link from "next/link";
import { Icon } from "@/components/Icon";
import Button from "../../ui/Button";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { useState, useRef } from "react";
import type { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import { BigImageSectionItem } from "@/app/watch/mocks";
import { VideoCard } from "../VideoCard";

type BigImageSectionProps = {
  bigImageItem: BigImageSectionItem;
};

export default function BigImageSection({
  bigImageItem,
}: BigImageSectionProps) {
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const swiperRef = useRef<SwiperType | null>(null);

  return (
    <div className="bg-black text-white w-full border-t border-grey-400">
      <div className="relative w-full max-w-screen-2xl mx-auto">
        <div className="relative w-full h-[657.71px] overflow-visible rounded-2xl pt-[80px] lg:pt-0">
          <div className="lg:absolute inset-0 flex justify-end items-center overflow-hidden">
            <div className="relative w-[1040px] h-[657.71px] z-0 overflow-hidden rounded-2xl">
              <Image
                src={bigImageItem.heroImage}
                alt={bigImageItem.title}
                fill
                className="object-cover"
                priority
              />
              <div className="absolute top-0 left-0 w-[200px] h-full pointer-events-none bg-gradient-to-r from-black via-black/80 to-transparent hidden lg:block" />
              <div className="absolute top-0 right-0 w-[200px] h-full pointer-events-none bg-gradient-to-l from-black via-black/80 to-transparent hidden lg:block" />
              <div className="absolute top-0 left-0 w-full h-[30px] pointer-events-none bg-gradient-to-b from-black via-black/50 to-transparent" />
              <div className="absolute bottom-0 left-0 w-full h-[80px] bg-gradient-to-t from-black via-black/50 to-transparent pointer-events-none z-10 lg:block" />
            </div>
          </div>

          <div className="absolute top-0 left-0 right-0 z-20 pt-4 flex justify-between items-start">
            <h2 className="text-white text-xl lg:text-2xl font-bold uppercase">
              {bigImageItem.tag}
            </h2>
            <div className="pr-4 sm:pr-0 lg:pr-0">
              <Link
                href="/pope-leo-xiv"
                className="text-white flex items-center gap-1 text-sm"
              >
                View All <Icon icon="arrow_forward" className="!text-sm" />
              </Link>
            </div>
          </div>
        </div>

        <div className="lg:absolute lg:inset-0 flex flex-col justify-start sm:px-0 lg:px-0 z-20 mt-4 pt-[80px] mb-12 lg:mt-0 lg:pt-24">
          <span className="text-xs sm:text-sm text-white/70 tracking-widest uppercase mb-2">
            HABEMUS PAPAM
          </span>
          <h1 className="text-[28px] sm:text-[36px] lg:text-[42px] leading-tight font-semibold max-w-[600px]">
            {bigImageItem.title}
          </h1>
          <p className="text-sm mt-3 max-w-[480px] text-white/80">
            {bigImageItem.description}
          </p>
          <p className="text-xs mt-4 text-white/80">
            By <span className="font-semibold">Walter Sánchez Silva</span> ·
            8:22 PM ET
          </p>
        </div>

        <div className="relative -mt-8 pb-12 overflow-visible z-[20]">
          <div className="-mx-4 sm:-mx-6 px-4 sm:px-6 !overflow-hidden pt-1">
            <Swiper
              spaceBetween={30}
              navigation={{
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
              }}
              modules={[Navigation]}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              breakpoints={{
                0: { slidesPerView: 1.3 },
                768: { slidesPerView: 2 },
                1024: { slidesPerView: 3 },
                1280: { slidesPerView: 4 },
              }}
              className="!overflow-visible px-2 sm:px-0"
            >
              {bigImageItem.relatedVideos.map((video, index) => (
                <SwiperSlide key={index}>
                  <VideoCard
                    showDescription={false}
                    video={video}
                    thumbnail={video.imageUrl}
                    mode="column"
                    showCaption={false}
                    showTimestamp={true}
                  />
                </SwiperSlide>
              ))}
            </Swiper>

            {!isBeginning && (
              <Button
                as="icon"
                onClick={() => swiperRef.current?.slidePrev()}
                iconName="arrow_back"
                variant="secondary"
                className="hidden lg:flex w-[50px] h-[50px] absolute top-[calc(50%-60px)] left-0 -ml-[30px] z-10"
              />
            )}
            {!isEnd && (
              <Button
                as="icon"
                onClick={() => swiperRef.current?.slideNext()}
                iconName="arrow_forward"
                variant="secondary"
                className="hidden lg:flex w-[50px] h-[50px] absolute top-[calc(50%-60px)] right-0 -mr-[30px] z-10"
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
