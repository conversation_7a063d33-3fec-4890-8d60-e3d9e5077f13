import { render, screen, fireEvent } from "@testing-library/react";
import <PERSON>ton, { ButtonProps } from "@/components/ui/Button";

describe("Button Component", () => {
  const defaultProps: ButtonProps = {
    variant: "primary",
    state: "enabled",
    onClick: jest.fn(),
    children: "Click Me",
  };

  test("renders the button with default props", () => {
    render(<Button {...defaultProps} />);
    const button = screen.getByRole("button", { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent("Click Me");
  });

  test("applies the correct classes for the primary variant", () => {
    render(<Button {...defaultProps} variant="primary" />);
    const button = screen.getByRole("button");
    expect(button).toHaveClass("bg-red-700 text-white");
  });

  test("applies the correct classes for the secondary variant", () => {
    render(<Button {...defaultProps} variant="secondary" />);
    const button = screen.getByRole("button");
    expect(button).toHaveClass("bg-white text-black");
  });

  test("applies the correct classes for the outlined variant", () => {
    render(<Button {...defaultProps} variant="outlined" />);
    const button = screen.getByRole("button");
    expect(button).toHaveClass(
      "border border-white/40 hover:border-white text-white active:border-white disabled:text-grey-700 disabled:border-grey-300 disabled:bg-transparent",
    );
  });

  test("disables the button when state is 'disabled'", () => {
    render(<Button {...defaultProps} state="disabled" />);
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
    expect(button).toHaveClass("cursor-not-allowed");
  });

  test("calls onClick when the button is clicked", () => {
    render(<Button {...defaultProps} />);
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(defaultProps.onClick).toHaveBeenCalled();
  });

  test("does not call onClick when the button is disabled", () => {
    render(<Button {...defaultProps} state="disabled" />);
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(defaultProps.onClick).not.toHaveBeenCalled();
  });

  test("applies the correct classes for the primaryBlack variant", () => {
    render(<Button {...defaultProps} variant="primaryBlack" />);
    const button = screen.getByRole("button");
    expect(button).toHaveClass("bg-black", "text-white");
  });

  test("renders without icon when iconName is not provided", () => {
    render(<Button {...defaultProps}>Click Me</Button>);
    const button = screen.getByRole("button");
    expect(
      button.querySelector(".material-symbols-rounded"),
    ).not.toBeInTheDocument();
  });

  test("renders with icon when iconName is provided", () => {
    render(
      <Button {...defaultProps} iconName="cart">
        Click Me
      </Button>,
    );
    const button = screen.getByRole("button");
    const iconWrapper = button.querySelector("div.flex.items-center.gap-2");
    expect(iconWrapper).toBeInTheDocument();
    expect(
      button.querySelector(".material-symbols-rounded"),
    ).toBeInTheDocument();
  });
});
