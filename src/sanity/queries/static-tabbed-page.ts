import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

type Slug = {
  current: string;
  _type?: string;
};

export const staticTabbedPageQuery = defineQuery(`
  *[
    _type == "staticTabbedPage" &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) && 
    slug.current == $slug
  ][0]{
    title,
    slug,
    "imageUrl": image.secure_url,
    description,
    content{
      tabBody[]{
        title,
        "imageUrl": image.secure_url,
        slug,
        textBody[]{
          ...,
          _type == "grayArea" => {
            "imageUrl": image.secure_url,
            description
          },
         _type == "timeline" => {
           items[] {
             _key,
             title,
             year,
             highlighted,
             "imageUrl": image.secure_url
           }
         }
        }
      }
    }
  }
`);

export type TimelineItem = {
  _key: string;
  title: string;
  year: number;
  highlighted: boolean;
  imageUrl?: string;
};

export type StaticTabbedPageQueryResult = {
  title: string;
  slug: Slug;
  imageUrl: string;
  description: Array<{
    _key: string;
    _type: string;
    style?: string;
    children: Array<{
      _key: string;
      _type: string;
      marks: string[];
      text: string;
    }>;
  }>;
  content: {
    tabBody: Array<{
      title: string;
      imageUrl?: string;
      slug: Slug;
      textBody: Array<{
        _key: string;
        _type: string;
        children?: Array<{
          _key: string;
          _type: string;
          marks: string[];
          text: string;
        }>;
        imageUrl?: string;
        description?: Array<{
          _key: string;
          _type: string;
          children: Array<{
            _key: string;
            _type: string;
            marks: string[];
            text: string;
          }>;
        }>;
        items?: TimelineItem[];
      }>;
    }>;
  };
};
