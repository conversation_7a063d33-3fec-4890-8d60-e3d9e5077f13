"use client";

import Link from "next/link";
import { VideoCard } from "@/components/Watch/VideoCard";
import { VideoItem } from "../mocks";
import { useEffect, useRef, useState } from "react";
import Skeleton from "@/components/ui/Skeleton";
import { useRouter, useSearchParams } from "next/navigation";
import Select from "react-select";

type Props = {
  topic: string;
  videos: VideoItem[];
};

export const TopicClient: React.FC<Props> = ({ topic, videos }) => {
  const router = useRouter();
  const [showMore, setShowMore] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const videoContainer = useRef<HTMLDivElement | null>(null);
  const topicTitle = `${topic.replace("-", " ")[0].toUpperCase()}${topic.replace("-", " ").slice(1)}`;
  const queryParams = useSearchParams();
  const initialSort = queryParams?.get("sort") || "popular";
  const [sortOrder, setSortOrder] = useState(initialSort);

  const handleScroll = () => {
    if (!videoContainer.current || loading || showMore) return;

    const scrollPosition = window.scrollY;
    const windowHeight = window.innerHeight;
    const containerRect = videoContainer.current.getBoundingClientRect();
    const containerBottom = containerRect.bottom + scrollPosition;

    const threshold = -200;
    const isNearBottom =
      scrollPosition + windowHeight >= containerBottom - threshold;

    if (isNearBottom) {
      setLoading(true);

      setTimeout(() => {
        setShowMore(true);
        setLoading(false);
      }, 1000);
    }
  };

  useEffect(() => {
    const newSort = queryParams?.get("sort") || "newest";

    setSortOrder(newSort);
  }, [queryParams]);

  useEffect(() => {
    handleScroll();

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const sortOptions = [
    { value: "newest", label: "Sort by Newest" },
    { value: "oldest", label: "Sort by Oldest" },
    { value: "popular", label: "Sort by Popular" },
  ];

  type SortOption = "newest" | "oldest" | "popular";

  const handleDateChange = async (selectedOption: {
    value: SortOption;
    label: string;
  }) => {
    setSortOrder(selectedOption.value);

    const currentParams = new URLSearchParams(window.location.search);

    currentParams.set("sort", selectedOption.value);

    const newUrl = `?${currentParams.toString()}`;
    router.replace(newUrl);
  };

  const sortVideos = (videos: VideoItem[]) => {
    const videosCopy = [...videos];

    switch (sortOrder) {
      case "newest":
        return videosCopy.sort(
          (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
        );
      case "oldest":
        return videosCopy.sort(
          (a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
        );
      case "popular":
      default:
        return videosCopy;
    }
  };

  return (
    <div className="bg-black text-white">
      <div className="container mx-auto pt-8 px-4 h-auto">
        <header>
          <nav className="mt-2">
            <ol className="flex items-center gap-2 text-[13px] lg:text-[14px] text-grey-300 font-normal">
              <li className="underline hover:text-red-700 text-white transition-all">
                <Link href="/">Home</Link>
              </li>
              <li>·</li>
              <li className="underline hover:text-red-700 text-white transition-all">
                <Link href="/watch">Watch</Link>
              </li>
              <li>·</li>
              <li>All in {topicTitle}</li>
            </ol>
          </nav>
        </header>

        {videos.length > 0 && (
          <section className="pt-3 pb-8">
            <div className="flex flex-col md:flex-row gap-4 md:justify-between md:items-end mb-6 md:mb-8">
              <h1 className="text-[32px] lg:text-[52px] font-bold font-titles">
                All in {topicTitle}
              </h1>

              <div className="flex">
                <div>
                  <Select
                    inputId="date-filter"
                    value={sortOptions.find((o) => o.value === sortOrder)}
                    onChange={(value) =>
                      handleDateChange(
                        value as { value: SortOption; label: string },
                      )
                    }
                    options={sortOptions}
                    isSearchable={false}
                    classNamePrefix="react-select"
                    components={{
                      IndicatorSeparator: () => null,
                    }}
                    styles={{
                      control: (base) => ({
                        ...base,
                        fontSize: "0.875rem",
                        padding: "0",
                        border: "none",
                        color: "white",
                        whiteSpace: "nowrap",
                        backgroundColor: "transparent",
                        boxShadow: "none",
                        outline: "none",
                        "&:hover": {
                          border: "none",
                          boxShadow: "none",
                        },
                      }),
                      valueContainer: (base) => ({
                        ...base,
                        padding: "0",
                      }),
                      singleValue: (base) => ({
                        ...base,
                        color: "white",
                      }),
                      dropdownIndicator: (base) => ({
                        ...base,
                        padding: "4px",
                        color: "white",
                      }),
                      indicatorsContainer: (base) => ({ ...base, height: "" }),
                      menu: (base) => ({
                        ...base,
                        borderRadius: "0.75rem",
                        overflow: "hidden",
                        width: "150px",
                        minWidth: "100%",
                      }),
                      option: (base, state) => ({
                        ...base,
                        color: "black",
                        borderBottom: "1px solid #e5e7eb",
                        whiteSpace: "nowrap",
                        backgroundColor: state.isSelected
                          ? "#f3f4f6"
                          : state.isFocused
                            ? "#f9fafb"
                            : "white",
                        ":last-of-type": {
                          borderBottom: "none",
                        },
                      }),
                    }}
                  />
                </div>
              </div>
            </div>

            <div
              className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6"
              ref={videoContainer}
            >
              {sortVideos(videos)
                .slice(0, showMore ? videos.length : 24)
                .map((video, index) => (
                  <VideoCard
                    key={index}
                    video={video}
                    thumbnail={video.imageUrl}
                    mobileView="landscape"
                  />
                ))}

              {loading && !showMore && (
                <>
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div
                      key={index}
                      className={`
                      ${index == 3 ? "md:hidden lg:block" : ""}
                    `}
                    >
                      <div className="md:hidden">
                        <Skeleton
                          overrideImageWidth
                          imageHeight=""
                          showImage={true}
                          imageClass={`aspect-[16/9] ${index < 4 ? "!w-[136px] !md:w-full" : ""}`}
                          showTitle={true}
                          showDescription={false}
                          imagePosition="left"
                        />
                      </div>
                      <div className="hidden md:block">
                        <Skeleton
                          overrideImageWidth
                          imageHeight=""
                          imageWidth="100%"
                          showImage={true}
                          imageClass="aspect-[16/9]"
                          showTitle={true}
                          showDescription={false}
                        />
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};
