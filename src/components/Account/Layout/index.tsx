import { ReactNode } from "react";
import UserProfile from "@/components/Account/Sidebar/UserProfile";
import MenuItem from "@/components/Account/Sidebar/MenuItem";

const LeftColumn = ({ children }: { children?: ReactNode }) => {
  const menuItems = [
    { href: "/account/interests", label: "My Interests" },
    { href: "/account/saved-articles", label: "Saved Articles" },
    { href: "/account/get-in-touch", label: "Get in Touch" },
  ];

  return (
    <div className="w-full md:w-80 shrink-0" data-testid="account-layout-left">
      <div className="lg:sticky md:top-28">
        <UserProfile />

        <div className="relative -mx-4 px-4 sm:mx-0 md:px-0 border-b border-grey-200 md:border-0">
          <nav className="flex md:flex-col space-x-4 md:space-x-0 md:space-y-1 overflow-x-auto pb-4 md:pb-0 hide-scrollbar">
            {menuItems.map((item) => (
              <MenuItem key={item.href} href={item.href} label={item.label} />
            ))}
          </nav>
        </div>

        {children}
      </div>
    </div>
  );
};

const MainContent = ({ children }: { children: ReactNode }) => {
  return (
    <div className="flex-1 min-w-0" data-testid="account-layout-main">
      {children}
    </div>
  );
};

const RightColumn = ({}: { children: ReactNode }) => {
  return (
    <div
      className="w-full lg:w-80 md:hidden lg:block shrink-0"
      data-testid="account-layout-right"
    >
      {/* <div className="lg:sticky lg:top-28">{children}</div> */}
    </div>
  );
};

const AccountLayout = ({ children }: { children: ReactNode }) => {
  return (
    <div className="min-h-screen bg-white" data-testid="account-layout">
      <main className="container mx-auto px-4 py-8">
        <h1 className="mb-4 lg:mb-8 lg:pt-4 font-semibold font-titles text-[32px] lg:text-[52px]">
          My account
        </h1>

        <div className="flex flex-col md:flex-row gap-6">{children}</div>
      </main>
    </div>
  );
};

export default Object.assign(AccountLayout, {
  Left: LeftColumn,
  Main: MainContent,
  Right: RightColumn,
});
