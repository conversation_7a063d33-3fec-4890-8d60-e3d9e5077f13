import { NextRequest } from "next/server";
import { articlesByTagsQuery } from "@/sanity/queries/Tag/tag";
import { sanityFetch } from "@/sanity/client";

export async function POST(req: NextRequest) {
  try {
    const { tagIds } = await req.json();

    if (!Array.isArray(tagIds) || tagIds.length === 0) {
      return new Response(JSON.stringify({ items: [] }), { status: 200 });
    }

    const articles = await sanityFetch({
      query: articlesByTagsQuery,
      params: { tagIds },
    });

    return new Response(JSON.stringify({ items: articles }), { status: 200 });
  } catch (err) {
    console.error("Error fetching articles by tags:", err);
    return new Response(JSON.stringify({ error: "Server error" }), {
      status: 500,
    });
  }
}
