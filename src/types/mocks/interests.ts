import { NewsItem, Option } from "@/types/interests";
import { MultiSelectOption } from "@/components/ui/Form/MultiSelect/types";

export const authorsData: Option[] = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON> I<PERSON>umi<PERSON>",
    role: "Dolor Editor",
  },
  {
    id: 2,
    name: "Amet Consectur",
    role: "Adipiscing Publisher",
  },
  {
    id: 3,
    name: "<PERSON><PERSON> Seddo",
    role: "Eiusmod Content Manager",
  },
  {
    id: 4,
    name: "Tempor Incidid",
    role: "Labore Editor",
  },
  {
    id: 5,
    name: "<PERSON><PERSON> Magna",
    role: "Aliqua Publisher",
  },
];

export const moreAuthorsData: Option[] = [
  {
    id: 6,
    name: "Utenim Minim",
    role: "Veniam Content Manager",
  },
  {
    id: 7,
    name: "<PERSON><PERSON><PERSON>",
    role: "Exercitation Editor",
  },
];

const allAuthors = [...authorsData, ...moreAuthorsData];

export const topicsData: MultiSelectOption[] = [
  { id: "1", name: "Top stories" },
  { id: "2", name: "World" },
];

export const newsData: NewsItem[] = [
  {
    id: 1,
    category: "LOREM",
    topicIds: [1, 2],
    title: "Lorem ipsum dolor sit amet: Consectetur adipiscing elit sed do",
    author: allAuthors.find((author) => author.id === 1) || allAuthors[0],
    time: "8:34 PM ET",
  },
  {
    id: 2,
    category: "IPSUM",
    topicIds: [3, 4, 5],
    title: "Eiusmod tempor incididunt: Ut labore et dolore magna aliqua",
    author: allAuthors.find((author) => author.id === 2) || allAuthors[0],
    time: "17:56 PM ET",
  },
  {
    id: 3,
    category: "DOLOR",
    topicIds: [6],
    title: "Ut enim ad minim veniam: Quis nostrud exercitation ullamco",
    author: allAuthors.find((author) => author.id === 3) || allAuthors[0],
    time: "November 3, 2024",
  },
  {
    id: 4,
    category: "AMET",
    topicIds: [7, 8],
    title: "Laboris nisi ut aliquip: Ex ea commodo consequat aute",
    author: allAuthors.find((author) => author.id === 4) || allAuthors[0],
    time: "November 3, 2024",
  },
  {
    id: 5,
    category: "CONSECTETUR",
    topicIds: [9, 10, 11],
    title: "Duis aute irure dolor: In reprehenderit voluptate velit esse",
    author: allAuthors.find((author) => author.id === 5) || allAuthors[0],
    time: "November 3, 2024",
  },
  {
    id: 6,
    category: "ADIPISCING",
    topicIds: [12, 13],
    title: "Cillum dolore eu fugiat: Nulla pariatur excepteur sint occaecat",
    author: allAuthors.find((author) => author.id === 6) || allAuthors[0],
    time: "November 2, 2024",
  },
  {
    id: 7,
    category: "ELIT",
    topicIds: [14, 15],
    title: "Cupidatat non proident: Sunt in culpa qui officia deserunt",
    author: allAuthors.find((author) => author.id === 7) || allAuthors[0],
    time: "November 2, 2024",
  },
  {
    id: 13,
    category: "NOSTRUD",
    topicIds: [1, 15, 28],
    title: "Fugit sed quia: Consequuntur magni dolores eos qui ratione",
    author: allAuthors.find((author) => author.id === 1) || allAuthors[0],
    time: "October 30, 2024",
  },
  {
    id: 14,
    category: "EXERCITATION",
    topicIds: [5, 10, 20],
    title: "Voluptatem sequi: Nesciunt neque porro quisquam est qui",
    author: allAuthors.find((author) => author.id === 5) || allAuthors[0],
    time: "October 29, 2024",
  },
];
