import { render, screen } from "@testing-library/react";
import NotFound from "@/app/not-found";
import { sanityFetch } from "@/sanity/client";

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock("@/sanity/client", () => ({
  sanityFetch: jest.fn(),
}));

describe("NotFound Component", () => {
  it("renders sections if data is fetched successfully", async () => {
    // Mock the data returned by the fetch
    const mockSections = [
      { slug: { current: "section-1" }, title: "Section 1" },
      { slug: { current: "section-2" }, title: "Section 2" },
    ];

    (sanityFetch as jest.Mock).mockResolvedValueOnce({
      sections: mockSections,
    });

    render(await NotFound());

    // Wait for the sections to appear
    for (const section of mockSections) {
      expect(await screen.findByText(section.title)).toBeInTheDocument();
    }
  });

  it("renders fallback message when no sections are available", async () => {
    // Mock empty sections
    (sanityFetch as jest.Mock).mockResolvedValueOnce({
      sections: [],
    });

    render(await NotFound());

    // Wait for the fallback message to appear
    expect(
      await screen.findByText("No sections available"),
    ).toBeInTheDocument();
  });

  it("handles fetch errors gracefully", async () => {
    // Mock an error being thrown
    (sanityFetch as jest.Mock).mockRejectedValueOnce(
      new Error("Failed to fetch"),
    );

    render(await NotFound());

    // Ensure the fallback is rendered
    expect(
      await screen.findByText("No sections available"),
    ).toBeInTheDocument();
  });
});
