import { NextRequest } from "next/server";
import {
  addBookmark,
  getBookmarkCount,
  getUserBookmark,
  removeBookmark,
} from "@/app/api/services/articles/bookmarks";
import {
  unauthorizedResponse,
  badRequestResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { getAuth0UserId, extractParam } from "@/app/api/util";

/**
 * Handles the POST request to save an article.
 *
 * @param params.articleId - The ID of the article to bookmark.
 * @returns A JSON response containing a success or error message.
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const articleId = await extractParam(params, "articleId");
    if (!articleId) {
      return badRequestResponse("Missing articleId in request params");
    }

    await addBookmark(auth0UserId, articleId);
    return successResponse("Article bookmarked", 201);
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the GET request to retrieve bookmark information for a specific article.
 *
 * @param params.articleId - The ID of the article for which to retrieve bookmark information.
 * @returns A JSON response containing the bookmark count and whether the user has bookmarked the article.
 *
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    const articleId = await extractParam(params, "articleId");
    if (!articleId) {
      return badRequestResponse("Missing articleId in request params");
    }

    const [countData, userData] = await Promise.all([
      getBookmarkCount(articleId),
      auth0UserId ? getUserBookmark(auth0UserId, articleId) : null,
    ]);

    return successResponseWithData(
      {
        bookmarkCount: countData?.bookmark_count || 0,
        userBookmarked: !!userData,
      },
      200,
    );
  } catch (error) {
    return errorResponse(error);
  }
}

/**
 * Handles the DELETE request to remove a saved article.
 *
 * @param params.articleId - The ID of the article.
 * @returns A JSON response containing a success or error message.
 *
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ articleId: string }> },
) {
  try {
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }

    const articleId = await extractParam(params, "articleId");
    if (!articleId) {
      return badRequestResponse("Missing articleId in request params");
    }

    await removeBookmark(auth0UserId, articleId);
    return successResponse("Article bookmark removed", 200);
  } catch (error) {
    return errorResponse(error);
  }
}
