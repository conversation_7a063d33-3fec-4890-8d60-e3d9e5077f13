import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import MenuItem from "@/components/Account/Sidebar/MenuItem";

jest.mock("next/navigation", () => ({
  usePathname: jest.fn(),
}));

describe("MenuItem", () => {
  test("renders menu item with label", () => {
    render(<MenuItem href="/example" label="Example" />);

    expect(screen.getByText("Example")).toBeInTheDocument();
  });

  test("renders menu item with active state", () => {
    const usePathnameMock = jest.requireMock("next/navigation").usePathname;
    usePathnameMock.mockReturnValue("/example");

    render(<MenuItem href="/example" label="Example" />);

    expect(screen.getByText("Example")).toHaveClass("underline");
  });

  test("renders menu item with inactive state", () => {
    const usePathnameMock = jest.requireMock("next/navigation").usePathname;
    usePathnameMock.mockReturnValue("/other-page");

    render(<MenuItem href="/example" label="Example" />);

    expect(screen.getByText("Example")).not.toHaveClass("underline");
  });

  test("renders menu item with icon", () => {
    render(<MenuItem href="/example" label="Example" />);

    expect(screen.getByTestId("goto")).toBeInTheDocument();
  });
});
