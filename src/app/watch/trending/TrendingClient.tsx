"use client";

import Link from "next/link";
import { VideoItem } from "../mocks";
import Select from "react-select";
import { useState } from "react";
import Skeleton from "@/components/ui/Skeleton";
import Image from "next/image";
import { truncateDescription, truncateTitle } from "@/utils/utils";
import Timestamp from "@/components/Timestamp";
import { Article as TrendingArticle } from "@/sanity/queries/Trending/trending";
import { Trending } from "@/components/HomePage/Trending";
import { Icon } from "@/components/Icon";

type Props = {
  initialVideos: VideoItem[];
  generalTrendingArticles: TrendingArticle[];
};

type DateOption = "yesterday" | "past_week" | "past_month";

export const TrendingClient: React.FC<Props> = ({
  initialVideos,
  generalTrendingArticles,
}) => {
  const [videos, setVideos] = useState<VideoItem[]>(initialVideos);
  const [dateOption, setDateOption] = useState<DateOption>("yesterday");
  const [loading, setLoading] = useState(false);

  const handleDateChange = async (selectedOption: {
    value: DateOption;
    label: string;
  }) => {
    const value = selectedOption.value;
    setDateOption(value);
    setLoading(true);

    // TODO: Conect with analytics

    setTimeout(() => {
      setVideos([...videos].sort(() => Math.random() - 0.5));

      setLoading(false);
    }, 1000);
  };

  const options = [
    { value: "yesterday", label: "Yesterday" },
    { value: "past_week", label: "Past Week" },
    { value: "past_month", label: "Past Month" },
  ];

  return (
    <div className="bg-black text-white">
      <div className="container mx-auto pt-8 px-4 h-auto">
        <header>
          <nav className="mt-2">
            <ol className="flex items-center gap-2 text-[13px] lg:text-[14px] text-grey-300 font-normal">
              <li className="underline hover:text-red-700 text-white transition-all">
                <Link href="/">Home</Link>
              </li>
              <li>·</li>
              <li className="underline hover:text-red-700 text-white transition-all">
                <Link href="/watch">Watch</Link>
              </li>
              <li>·</li>
              <li>Trending Videos</li>
            </ol>
          </nav>
        </header>

        {/* More Videos Section */}
        <section className="pt-3 pb-8">
          <div className="flex flex-col md:flex-row gap-4 md:justify-between md:items-end mb-6 md:mb-8">
            <h1 className="text-[32px] lg:text-[52px] font-bold font-titles">
              Trending Videos
            </h1>

            <div className="flex">
              <div>
                <Select
                  inputId="date-filter"
                  value={options.find((o) => o.value === dateOption)}
                  onChange={(value) =>
                    handleDateChange(
                      value as { value: DateOption; label: string },
                    )
                  }
                  options={options}
                  isSearchable={false}
                  classNamePrefix="react-select"
                  components={{
                    IndicatorSeparator: () => null,
                  }}
                  styles={{
                    control: (base) => ({
                      ...base,
                      fontSize: "0.875rem",
                      padding: "0",
                      border: "none",
                      color: "white",
                      whiteSpace: "nowrap",
                      backgroundColor: "transparent",
                      boxShadow: "none",
                      outline: "none",
                      "&:hover": {
                        border: "none",
                        boxShadow: "none",
                      },
                    }),
                    valueContainer: (base) => ({
                      ...base,
                      padding: "0",
                    }),
                    singleValue: (base) => ({
                      ...base,
                      color: "white",
                    }),
                    dropdownIndicator: (base) => ({
                      ...base,
                      padding: "4px",
                      color: "white",
                    }),
                    indicatorsContainer: (base) => ({ ...base, height: "" }),
                    menu: (base) => ({
                      ...base,
                      borderRadius: "0.75rem",
                      overflow: "hidden",
                      width: "120px",
                      minWidth: "100%",
                    }),
                    option: (base, state) => ({
                      ...base,
                      color: "black",
                      borderBottom: "1px solid #e5e7eb",
                      whiteSpace: "nowrap",
                      backgroundColor: state.isSelected
                        ? "#f3f4f6"
                        : state.isFocused
                          ? "#f9fafb"
                          : "white",
                      ":last-of-type": {
                        borderBottom: "none",
                      },
                    }),
                  }}
                />
              </div>
            </div>
          </div>

          <div className="xl:grid xl:grid-cols-12 xl:gap-4 mt-4 pt-2 border-t border-grey-200">
            {/* Left Column */}
            <div className="xl:col-span-9 pt-3">
              <section className="overflow-hidden xl:pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-1 gap-6">
                  {loading
                    ? Array.from({ length: 10 }).map((_, i) => (
                        <Skeleton
                          key={`skeleton-${i}`}
                          imagePosition="left"
                          imageHeight="12rem"
                          imageWidth="22rem"
                          showTitle={true}
                        />
                      ))
                    : videos?.slice(0, 10).map((video, index) => (
                        <div
                          key={`video-${index}`}
                          className="flex gap-4 pb-6 mb-4 border-b border-b-grey-200 xl:block xl:gap-0 xl:flex-none sm:items-start"
                        >
                          {/* Index */}
                          <div className="text-[52px] font-semibold text-gray-500 pt-2 pr-2 w-6 flex-shrink-0 font-titles -mt-1 xl:float-left mr-4 xl:mr-8">
                            {index + 1}
                          </div>

                          {/* Image + Title */}
                          <div className="flex flex-col flex-1 gap-4 md:flex-row xl:items-start xl:gap-8 items-start">
                            {/* Image */}
                            {video.imageUrl && (
                              <div className="relative aspect-[16/9] w-full md:w-[240px] xl:w-1/3 flex-shrink-0">
                                <Link
                                  href={video.href}
                                  className="transition-opacity hover:opacity-85"
                                >
                                  <Image
                                    src={video.imageUrl}
                                    alt={video.title}
                                    fill
                                    className="object-cover rounded-2xl w-full h-full"
                                  />
                                </Link>

                                <div className="absolute w-[35px] h-[35px] rounded bg-black/70 text-white flex items-center justify-center bottom-2 left-2 md:bottom-4 md:left-4">
                                  <Icon icon="play" className="w-4 h-4" />
                                </div>
                              </div>
                            )}

                            {/* Title */}
                            <div className="xl:w-2/3 mt-1 xl:mt-6">
                              <h2 className="font-semibold font-titles text-[24px] leading-[28px] xl:text-[28px] xl:leading-[36px]">
                                <Link
                                  href={video.href}
                                  className="hover-underline-animation"
                                >
                                  <>
                                    <span className="block xl:hidden">
                                      {truncateTitle(video.title, 100)}
                                    </span>
                                    <span className="hidden xl:block">
                                      {video.title}
                                    </span>
                                  </>
                                </Link>
                              </h2>

                              <p className="hidden lg:block mt-5 text-16px leading-[24px]">
                                {truncateDescription(video.description)}
                              </p>

                              <div className="text-[12px] text-grey-500 mt-2">
                                <Timestamp date={video.timestamp} />
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                </div>
              </section>
            </div>

            {/* Right Column */}
            <div className="col-span-3">
              <div className="sticky top-24">
                <Trending
                  oneColumn={true}
                  trendingArticles={generalTrendingArticles}
                  showViewAll
                />
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};
