"use client";

import { useEffect, useState } from "react";

declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _iub?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    FB?: any;
  }
}

interface FacebookEmbedProps {
  url: string;
}

const FacebookEmbed: React.FC<FacebookEmbedProps> = ({ url }) => {
  const [canRender, setCanRender] = useState(false);

  useEffect(() => {
    const handleConsent = () => {
      if (window._iub?.cs?.isConsentGiven?.()) {
        setCanRender(true);
      }
    };

    if (window._iub?.cs?.isConsentGiven?.()) {
      setCanRender(true);
    } else {
      window.addEventListener("iubenda_cs_consent_given", handleConsent);
    }

    return () => {
      window.removeEventListener("iubenda_cs_consent_given", handleConsent);
    };
  }, []);

  useEffect(() => {
    if (!window.FB) {
      const script = document.createElement("script");
      script.src =
        "https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0";
      script.async = true;
      script.defer = true;
      document.body.appendChild(script);
    } else {
      window.FB.XFBML.parse();
    }
  }, [canRender]);

  if (!canRender) {
    return;
  }

  return <div className="fb-post" data-href={url} data-width="350"></div>;
};

export default FacebookEmbed;
