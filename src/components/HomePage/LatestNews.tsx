import Link from "next/link";
import Timestamp from "../Timestamp";
import Button from "../ui/Button";
import { getArticleUrl, truncateTitle } from "@/utils/utils";
import { LatestNewsHomePageResponse } from "@/sanity/queries/latest-news";
import AuthorsList from "../Author/AuthorsList";

interface LatestNewsClientProps {
  latestNews: LatestNewsHomePageResponse;
}

export default function LatestNews({ latestNews }: LatestNewsClientProps) {
  return (
    <div className="bg-gray-100 border-t pt-6 pb-4">
      <h2 className="text-xl font-bold mb-4">LATEST NEWS</h2>
      <ul>
        {latestNews.articles.map((news, index) => (
          <li
            key={news.id}
            className={`mb-4 pb-4 ${index !== latestNews.articles.length - 1 ? "border-b border-grey-200" : ""}`}
          >
            <div className="flex items-center space-x-2 pb-2">
              {news.isLive ? (
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-red-600 rounded-full"></span>
                  <span className="text-red-600 font-semibold text-[14px]">
                    LIVE UPDATES
                  </span>
                </div>
              ) : (
                <span className="text-sm text-gray-600">
                  <Timestamp date={news.updated_at} />
                </span>
              )}
            </div>

            <h3
              className={`font-semibold font-titles text-[20px] leading-snug md:text-[24px] lg:text-[20px]`}
            >
              <Link
                href={getArticleUrl(
                  news.category?.slug,
                  news.slug,
                  news.subcategory?.slug,
                )}
                className="hover-underline-animation"
              >
                {truncateTitle(news.title)}
              </Link>
            </h3>

            {news.authors && (
              <AuthorsList
                authors={news.authors}
                publishedDate={news.publishedDate}
                showPublishedDate={false}
              ></AuthorsList>
            )}
          </li>
        ))}
      </ul>
      <Button
        className="w-full"
        variant="secondary"
        state="enabled"
        testId="login-button"
        iconName="arrow_forward"
        iconPosition="right"
        href="/latest"
      >
        View All
      </Button>
    </div>
  );
}
