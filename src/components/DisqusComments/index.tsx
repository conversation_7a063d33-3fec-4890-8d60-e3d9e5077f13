"use client";

import { DiscussionEmbed } from "disqus-react";

export type DisqusCommentsProps = {
  shortname: string;
  identifier: string;
  url: string;
  title: string;
  remoteAuthS3: string | null;
  apiKey: string | null;
  sso?: {
    name: string;
    button: string;
    icon: string;
    url: string;
    logout: string;
    profile_url?: string;
    width: string;
    height: string;
  };
};

export default function DisqusComments({
  shortname,
  identifier,
  url,
  title,
  remoteAuthS3,
  apiKey,
  sso,
}: DisqusCommentsProps) {
  if (!shortname) return null;

  const config: Record<string, unknown> = {
    url,
    identifier,
    title,
    language: "en",
  };

  if (remoteAuthS3 && apiKey) {
    config.remoteAuthS3 = remoteAuthS3;
    config.apiKey = apiKey;
  }

  if (sso) {
    config.sso = sso;
  }

  return <DiscussionEmbed shortname={shortname} config={config} />;
}
