"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import Link from "next/link";
import "swiper/css";
import "swiper/css/navigation";
import Timestamp from "../Timestamp";
import { LiveBlogArticle, Media } from "@/sanity/queries/liveBlog";
import { Category, Subcategory } from "@/sanity/queries/dailyStory";
import { getArticleUrl, getMediaImage, truncateText } from "@/utils/utils";
import ArticleMediaDisplay from "../DailyStory/ArticleMediaDisplay";

type Article = {
  id: string;
  title: string;
  slug: string;
  media: Media;
  category: Category;
  subcategory: Subcategory;
  isLiveBlog?: boolean;
};

export function ArticleCard({ article }: { article: Article }) {
  return (
    <div className="border transition-all border-white/40 hover:border-white rounded-2xl p-4 shadow-sm flex flex-row gap-4 items-start mr-4 sm:mr-0 md:h-full">
      {article.media && (
        <Link
          href={getArticleUrl(
            article.category.slug,
            article.slug,
            article.subcategory?.slug,
          )}
          className="relative block w-[81px] h-[81px] flex-shrink-0"
        >
          <Image
            src={getMediaImage(article.media)}
            alt={article.title}
            fill
            className="
            w-[70px] h-[70px] object-cover rounded-md flex-shrink-0 xs:w-[70px] xs:h-[70px] sm:w-[70px] sm:h-[70px]
            lg:w-[85px] lg:h-[85px]
            hover:opacity-85 transition-all
          "
          />
        </Link>
      )}
      <div>
        <div>
          {!article.isLiveBlog ? (
            <Link
              href={`/${article.category.slug}`}
              className="text-gray-400 font-roboto text-[13px] hover-underline-animation-white leading-0 inline-block"
            >
              {article.category.title}
            </Link>
          ) : (
            <span className="relative text-red-700 font-roboto text-[13px] font-bold leading-0 block">
              <span className="absolute top-1/2 left-0 w-2 h-2 bg-red-700 rounded-full transform -translate-y-1/2"></span>
              <span className="pl-4">LIVE UPDATES</span>
            </span>
          )}
        </div>

        <h3 className="text-[16px] leading-[19px] lg:text-[19px] lg:leading-[24px] font-light font-titles md:mt-1">
          <Link
            href={getArticleUrl(
              article.category.slug,
              article.slug,
              article.subcategory?.slug,
            )}
            className="hover-underline-animation-white"
          >
            {truncateText(article.title, 80)}
          </Link>
        </h3>
      </div>
    </div>
  );
}

export default function LiveBlogBreakingNews({
  liveBlogArticle,
}: {
  liveBlogArticle: LiveBlogArticle;
}) {
  return (
    <div className="bg-black text-white py-10 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="block md:hidden">
          <div className="w-full bg-gray-100">
            <Swiper
              loop={true}
              slidesPerView={1}
              breakpoints={{
                640: { slidesPerView: 2, spaceBetween: 8 },
                768: { slidesPerView: 2, spaceBetween: 12 },
                1024: { slidesPerView: 3, spaceBetween: 16 },
              }}
              centeredSlides={false}
              className="relative w-full [&>.swiper-wrapper]:items-center"
            >
              {liveBlogArticle.relatedArticles?.map((article) => (
                <SwiperSlide key={article.id}>
                  <ArticleCard key={article.id} article={article} />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>

        <div className="grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 mb-6 hidden md:grid">
          {liveBlogArticle.relatedArticles?.map((article, index) => (
            <div
              className={index == 2 ? "sm:hidden md:block" : ""}
              key={article.id}
            >
              <ArticleCard article={article} />
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 sm:pt-4 md:pt-8">
          <div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center lg:items-center gap-2 sm:gap-3 mb-4">
              {liveBlogArticle.isBreakingNews && (
                <span className="relative bg-red-700 text-white px-3 py-1 text-[14px] lg:text-sm font-semibold rounded-[10px] h-9 flex items-center">
                  <span className="absolute left-4 top-[40%] w-[8px] h-[8px] bg-white rounded-full animate-ping"></span>
                  <span className="absolute left-4 top-[40%] w-[8px] h-[8px] bg-white rounded-full"></span>
                  <span className="pl-5">BREAKING NEWS</span>
                </span>
              )}

              {/* Date and Time */}
              <div className="flex flex-col mt-3 sm:mt-0">
                <span className="text-gray-400 text-sm">
                  <Timestamp date={liveBlogArticle.publishedDate} />{" "}
                </span>
                <span className="text-gray-400 text-sm font-bold">
                  Updated <Timestamp date={liveBlogArticle.updatedAt} />
                </span>
              </div>
            </div>

            {/* Left Column (Title + Excerpt + Button for Larger Screens) */}
            <div className="pr-4 mr-2 flex flex-col order-1 md:order-1">
              {/* Title */}
              <h1 className="text-3xl lg:text-5xl font-bold !leading-[1.46] font-titles">
                {liveBlogArticle.title}
              </h1>

              {/* Excerpt */}
              <span className="block mt-4 text-lg font-roboto text-grey-300">
                {liveBlogArticle.description}
              </span>

              {/* Button Below Excerpt (ONLY for screens 1024px and above) */}
              {/* <div className="mt-6 hidden md:block">
                <Button
                  variant="outlined"
                  href="#"
                  className="w-48 border-grey-200 hover:!border-white"
                  iconName="alert"
                >
                  Keep me updated
                </Button>
              </div> */}
            </div>
          </div>

          {/* Right Column (Image + Caption) */}
          <div>
            {liveBlogArticle.media && (
              <ArticleMediaDisplay
                title={liveBlogArticle.title}
                articleMedia={liveBlogArticle.media}
                isLiveBlog
              />
            )}

            {/* Button Below Caption (ONLY for screens 360px - 768px) */}
            {/* <div className="mt-6 block md:hidden">
              <Button
                variant="outlined"
                href="#"
                className="w-48 border-grey-200 hover:!border-white"
                iconName="alert"
              >
                Keep me updated
              </Button>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
}
