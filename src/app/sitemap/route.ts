import { sitemapArticlesFirstYear } from "@/sanity/env";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const baseUrl = process.env.APP_BASE_URL || "";

    const firstYearWithArticles = Number.parseInt(sitemapArticlesFirstYear);
    const currentYear = new Date().getFullYear();
    const uniqueYears = Array.from(
      { length: currentYear - firstYearWithArticles + 1 },
      (_, index) => currentYear - index,
    );

    const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
      <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        <sitemap>
          <loc>${baseUrl}sitemap/main</loc>
          <lastmod>${new Date().toISOString()}</lastmod>
        </sitemap>
        ${uniqueYears
          .map(
            (year) => `
          <sitemap>
            <loc>${baseUrl}sitemap/articles/${year}</loc>
            <lastmod>${new Date().toISOString()}</lastmod>
          </sitemap>
        `,
          )
          .join("")}
      </sitemapindex>
    `;

    return new NextResponse(sitemapIndex, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, s-maxage=86400, stale-while-revalidate=43200",
      },
    });
  } catch (error) {
    console.error("Error generating sitemap index:", error);
    return new NextResponse("Error generating sitemap index", { status: 500 });
  }
}
