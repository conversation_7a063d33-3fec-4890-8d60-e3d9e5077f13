import React from "react";
import { render, screen } from "@testing-library/react";
import FooterMobile, {
  FooterMobileProps,
} from "@/components/Footer/footerMobile";
import {
  ComplianceLinkQueryResult,
  SocialMediaQueryResult,
} from "@/sanity/queries/layout";

describe("FooterMobile Component", () => {
  const mockSocialMediaItems: SocialMediaQueryResult = [
    {
      icon: "facebook",
      link: "https://facebook.com",
      _id: "",
      _type: "socialMedia",
      _createdAt: "",
      _updatedAt: "",
      _rev: "",
    },
    {
      icon: "x",
      link: "https://x.com",
      _id: "",
      _type: "socialMedia",
      _createdAt: "",
      _updatedAt: "",
      _rev: "",
    },
  ];

  const mockComplianceLinkItems: ComplianceLinkQueryResult = [
    {
      _id: "1",
      _type: "complianceLink",
      _createdAt: "2022-01-01T00:00:00Z",
      _updatedAt: "2022-01-01T00:00:00Z",
      _rev: "1",
      title: "Privacy Policy",
      slug: {
        _type: "slug",
        current: "privacy-policy",
      },
      isOnReducedFooter: false,
    },
    {
      _id: "2",
      _type: "complianceLink",
      _createdAt: "2022-01-01T00:00:00Z",
      _updatedAt: "2022-01-01T00:00:00Z",
      _rev: "1",
      title: "Terms And Conditions",
      slug: {
        _type: "slug",
        current: "terms-and-conditions",
      },
      isOnReducedFooter: true,
    },
  ];

  const mockDonationLink = {
    label: "Support EWTN",
    url: "https://donate.ewtn.com",
  };

  const defaultProps: FooterMobileProps = {
    socialMediaItems: mockSocialMediaItems,
    complianceLinkItems: mockComplianceLinkItems,
    donationLink: mockDonationLink,
    isFooterReduced: false,
  };

  test("renders correctly with default props", () => {
    render(<FooterMobile {...defaultProps} />);

    // Check if the logo is rendered
    expect(screen.getByAltText("EWTN Logo")).toBeInTheDocument();

    // Check button is rendered
    expect(screen.getByText("Support EWTN")).toBeInTheDocument();

    // Check social media links are rendered
    mockSocialMediaItems.forEach((social) => {
      expect(
        screen.getByRole("link", { name: social.icon }),
      ).toBeInTheDocument();
    });

    // Check compliance links are rendered
    mockComplianceLinkItems.forEach((link) => {
      expect(screen.queryAllByText(link.title).length).toBeGreaterThan(0);
    });

    // Check copyright text
    expect(screen.getByText(/Copyright ©/)).toBeInTheDocument();
  });
});
