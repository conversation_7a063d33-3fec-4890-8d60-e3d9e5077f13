import { sanityFetch } from "@/sanity/live";
import { articleQuery } from "@/sanity/queries/article";
import { notFound, redirect, RedirectType } from "next/navigation";
import { Metadata } from "next";
import {
  buildArticleJsonLd,
  buildWebPageJsonLd,
  generateArticleMetadata,
  generatePageMetadata,
} from "@/utils/metadata";
import DailyStoryClient from "./DailyStoryClient";
import LiveBlogPageClient from "@/app/[category]/[slugOrSubcategory]/LiveBlogPageClient";
import CategoryClient from "../CategoryClient";
import { categoryQuery } from "@/sanity/queries/Category/category";
import { Article } from "@/sanity/queries/Trending/trending";
import {
  fetchTrendingArticlesFromAnalytics,
  getTrendingWithPinnedArticles,
} from "@/app/api/trending";
import { slugToTitle } from "@/utils/utils";
import { workspace } from "@/sanity/env";
import JsonLD from "@/components/JsonLd";
import { fetchDisqusCommentCount } from "@/utils/disqus";

interface ArticlePageProps {
  params: Promise<{ category: string; slugOrSubcategory: string }>;
}

async function fetchArticle(options: { slug: string; category: string }) {
  return sanityFetch({
    query: articleQuery,
    params: {
      slug: options.slug,
      category: options.category,
    },
  });
}

async function fetchSubcategory(options: { category: string; slug: string }) {
  return sanityFetch({
    query: `*[_type == "subcategory" && agency == '${workspace}' && slug.current == $slug && category->slug.current == $category][0] {
    _id,
    slug,
    title,
    description,
    seoMetadata,
    pinnedArticles[]->{
      "id": _id,
      "created_at": dateTime(_createdAt),
      "updated_at": dateTime(_updatedAt),
      "publishedDate": dateTime(publishedDate),
      "slug": slug.current,
      title,
      truncatedTitle,
      "excerpt": description,
      "media": featuredMedia[] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "tags": tags[]-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "authors": authors[]-> {
        "slug": slug.current,
        "name": fullName,
        image {
          "url": secure_url
        }
      },
    },
    }`,
    params: {
      slug: options.slug,
      category: options.category,
    },
  });
}

async function fetchCategory(options: {
  slug: string;
  subcategoryId: string;
  ignoreArticlesIds: string[];
}) {
  return await sanityFetch({
    query: categoryQuery,
    params: {
      slug: options.slug,
      subcategoryId: options.subcategoryId,
      ignoreArticlesIds: options.ignoreArticlesIds,
    },
  });
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ category: string; slugOrSubcategory: string }>;
}): Promise<Metadata> {
  const { category, slugOrSubcategory } = await params;
  const res = await fetchSubcategory({
    category: category,
    slug: slugOrSubcategory,
  });

  const subcategory = res.data;
  let article;
  if (!subcategory) {
    const { data } = await fetchArticle({
      slug: slugOrSubcategory,
      category: category,
    });
    article = data;
  }

  if (!subcategory && !article) {
    return notFound();
  }

  if (subcategory) {
    return generatePageMetadata(
      `/${category}/${slugOrSubcategory}`,
      subcategory.seoMetadata?.title ??
        `Section - ${slugToTitle(category)} - ${subcategory.title}`,
      subcategory.seoMetadata?.description ?? subcategory.description,
    );
  }

  if (article.subcategory?.slug) {
    return redirect(
      `/${category}/${article.subcategory.slug}/${slugOrSubcategory}`,
      RedirectType.replace,
    );
  }

  return generateArticleMetadata(article, category);
}

export default async function SubcategoryOrArticlePage({
  params,
}: ArticlePageProps) {
  const { category: categorySlug, slugOrSubcategory } = await params;
  const { data: subcategory } = await fetchSubcategory({
    slug: slugOrSubcategory,
    category: categorySlug,
  });
  let article;
  if (!subcategory) {
    const { data } = await fetchArticle({
      slug: slugOrSubcategory,
      category: categorySlug,
    });
    article = data;
  }

  if (!subcategory && !article) {
    return notFound();
  }

  if (subcategory) {
    const trendingArticles = await fetchTrendingArticlesFromAnalytics({
      listSize: 5,
      categorySlug,
      subcategorySlug: subcategory.slug?.current,
      periodEnd: undefined,
      periodStart: undefined,
    });

    const trendingWithPinnedArticles: Article[] = getTrendingWithPinnedArticles(
      subcategory.pinnedArticles,
      trendingArticles,
    );

    const ignoreArticlesIds = trendingWithPinnedArticles.map(
      (trendingArticle: Article) => trendingArticle.id,
    );

    const { data: category } = await fetchCategory({
      slug: categorySlug,
      subcategoryId: subcategory._id,
      ignoreArticlesIds: ignoreArticlesIds,
    });

    if (!category) {
      return notFound();
    }

    const generalTrendingArticles = await fetchTrendingArticlesFromAnalytics({
      listSize: 5,
      categorySlug: null,
      subcategorySlug: null,
      periodEnd: undefined,
      periodStart: undefined,
    });

    const jsonLd = buildWebPageJsonLd(
      subcategory.seoMetadata?.title ??
        `Section - ${slugToTitle(categorySlug)} - ${subcategory.title}`,
      `/${category}/${slugOrSubcategory}`,
      subcategory.seoMetadata?.description ?? subcategory.description,
    );
    return (
      <>
        <JsonLD jsonLd={jsonLd} />
        <CategoryClient
          category={category}
          subcategory={subcategory}
          trendingArticles={trendingWithPinnedArticles}
          generalTrendingArticles={generalTrendingArticles}
          ignoreArticlesIds={ignoreArticlesIds}
        />
      </>
    );
  }

  if (article.subcategory?.slug) {
    return redirect(
      `/${categorySlug}/${article.subcategory.slug}/${slugOrSubcategory}`,
      RedirectType.replace,
    );
  }

  const jsonLd = buildArticleJsonLd(article, categorySlug);

  const commentCount = await fetchDisqusCommentCount(article.id);

  if (article._type === "liveBlogArticle") {
    return (
      <>
        <JsonLD jsonLd={jsonLd} />
        <LiveBlogPageClient liveBlogArticle={article} />
      </>
    );
  }

  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <DailyStoryClient article={article} commentCount={commentCount} />
    </>
  );
}
