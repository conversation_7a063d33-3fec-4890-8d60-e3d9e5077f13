import Skeleton from "@/components/ui/Skeleton";
import { useUser } from "@auth0/nextjs-auth0";
import Image from "next/image";

// We should move this to an utilities file or a user model
const getInitials = (name: string): string => {
  const parts = name.split(" ");
  if (parts.length < 2) return parts[0].substring(0, 2).toUpperCase();

  const firstInitial = parts[0][0];
  const lastInitial = parts[parts.length - 1][0];
  return (firstInitial + lastInitial).toUpperCase();
};

const UserProfile = () => {
  const { user, isLoading } = useUser();
  if (isLoading)
    return (
      <Skeleton
        imagePosition="left"
        showTitle={false}
        showDescription={false}
        imageHeight="6rem"
      />
    );
  if (!user) return null;
  return (
    <div className="flex items-center gap-4 p-4 bg-grey-100 rounded-xl mb-6">
      <div className="relative">
        {user.picture ? (
          <Image
            src={user.picture}
            alt={user.name ?? "Logged in user"}
            width={64}
            height={64}
            className="rounded-full"
          />
        ) : (
          <div className="w-16 h-16 rounded-full bg-grey-300 flex items-center justify-center text-white text-xl font-bold">
            {getInitials(user.name || "-")}
          </div>
        )}
      </div>
      <div>
        <h2 className="font-bold">{user.name}</h2>
        <a
          href={`/auth/logout`}
          className="hover-underline-animation text-left text-sm"
        >
          Log Out
        </a>
      </div>
    </div>
  );
};

export default UserProfile;
