import React, { useState, useEffect } from "react";
import clsx from "clsx";
import WhatsappButton from "./Whatsapp";
import FacebookButton from "./Facebook";
import TwitterButton from "./Twitter";
import MailButton from "./Mail";
import ClipboardButton from "./Clipboard";
import PlusShareButton from "./Plus";
import { Icon } from "../Icon";
import LikeButton from "./LikeButton";
import CommentButton from "@/components/Share/CommentButton";

const ShareButtonRow = ({
  articleTitle,
  articleImageUrl,
  isLiveBlog = false,
  likeCount,
  userLiked,
  isLoading,
  handleLikeClick,
  commentCount,
  commentCountLoading = false,
  onOpenComments,
  showCommentsButton = false,
}: {
  articleTitle?: string;
  articleImageUrl?: string;
  isLiveBlog?: boolean;
  likeCount: number | null;
  userLiked: boolean | null;
  isLoading: boolean;
  handleLikeClick: () => void;
  commentCount?: number | null;
  commentCountLoading?: boolean;
  onOpenComments?: () => void;
  showCommentsButton?: boolean;
}) => {
  const [shareUrl, setShareUrl] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setShareUrl(process.env.NEXT_PUBLIC_URL + window.location.pathname);
    }
  }, []);

  if (!shareUrl) {
    return null;
  }

  const share = () => {
    if (navigator.share) {
      navigator.share({
        title: "Check this out",
        url: shareUrl,
      });
    }
  };

  return (
    <>
      <div
        className={clsx(
          "flex  flex-col gap-4 mt-6 py-4",
          isLiveBlog ? "md:hidden" : "lg:hidden xl:hidden",
        )}
      >
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-row items-center gap-2">
            <p className="text-[14px]">Share this article</p>
            <button
              className="flex items-center text-[12px] justify-center"
              onClick={share}
            >
              <Icon icon="share" />
            </button>
          </div>
          <div className="flex items-center justify-end gap-2">
            <LikeButton
              likeCount={likeCount}
              userLiked={userLiked}
              isLoading={isLoading}
              handleLikeClick={handleLikeClick}
              position="right"
            />
            <div className="flex border border-grey-200 rounded-md width-[50px] bg-white hover:border-black transition-all hidden">
              <button className="flex flex-col items-center text-[12px] justify-center">
                <Icon icon="comments" className="px-4 pt-3 pb-5" />
                <span className="mt-[-1em]">12</span>
              </button>
            </div>
            {showCommentsButton && (
              <CommentButton
                commentCount={commentCount ?? 0}
                commentCountLoading={commentCountLoading}
                onOpenComments={onOpenComments}
              />
            )}
          </div>
        </div>
      </div>
      <div
        className={clsx(
          "hidden justify-between items-center mt-6 py-2",
          isLiveBlog ? "md:flex" : "lg:flex border-b border-t border-grey-300",
        )}
      >
        <div
          className={clsx(
            "flex items-center justify-between",
            isLiveBlog ? "md:w-full" : "lg:w-full",
          )}
        >
          <div className="flex flex-row items-center">
            <p className="text-[14px] whitespace-nowrap pr-1">
              Share this article
            </p>
            <FacebookButton shareUrl={shareUrl} />
            <TwitterButton shareUrl={shareUrl} />
            <MailButton shareUrl={shareUrl} />
            <WhatsappButton shareUrl={shareUrl} />
            <ClipboardButton shareUrl={shareUrl} />
            <PlusShareButton
              shareUrl={shareUrl}
              articleTitle={articleTitle}
              articleImageUrl={articleImageUrl}
            />
          </div>
          <div className="flex items-center justify-end gap-2">
            <LikeButton
              likeCount={likeCount}
              userLiked={userLiked}
              isLoading={isLoading}
              handleLikeClick={handleLikeClick}
              isHorizontal
            />
            <button className="flex flex-row items-center text-[12px] justify-center mt-3 hover:border-black transition-all hidden">
              <Icon icon="comments" className="px-2 pt-3 pb-5" />
              <span className="mt-[-12px]">145</span>
            </button>
            {showCommentsButton && (
              <CommentButton
                commentCount={commentCount ?? 0}
                commentCountLoading={commentCountLoading}
                onOpenComments={onOpenComments}
                useLikeButtonStyle
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ShareButtonRow;
