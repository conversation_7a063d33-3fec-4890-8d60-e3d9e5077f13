import Link from "next/link";

/**
 * Text color on hover
 * - 'none': No color change on hover
 * - 'grey': Changes to grey (grey-700)
 * - 'black': Changes to black
 * - 'red': Changes to red (red-700)
 * - 'white': Changes to white
 */
type HoverColorStyle = "none" | "grey" | "black" | "red" | "white";

/**
 * Underline style options
 * - 'none': No underline
 * - 'static': Permanent underline without animation
 * - 'effect': Animated underline on hover
 */
type UnderlineStyle = "none" | "static" | "effect";

/**
 * Props for the Breadcrumb component
 * @param title - Title to display in the breadcrumb
 * @param hoverColor - Text hover color (default: 'grey')
 * @param underlineStyle - Style of the underline (default: 'effect')
 */
interface BreadcrumbProps {
  title: string;
  hoverColor?: HoverColorStyle;
  underlineStyle?: UnderlineStyle;
}

/**
 * Helper function to get the appropriate hover color class
 */
const getHoverColorClass = (color: HoverColorStyle): string => {
  switch (color) {
    case "grey":
      return "hover:text-grey-700";
    case "black":
      return "hover:text-black";
    case "red":
      return "hover:text-red-700";
    case "white":
      return "hover:text-white";
    case "none":
      return "";
    default:
      return "hover:text-grey-700";
  }
};

/**
 * Helper function to get the appropriate underline class based on style and color
 */
const getUnderlineClass = (
  style: UnderlineStyle,
  hoverColor: HoverColorStyle,
): string => {
  // Return classes based on underline style
  switch (style) {
    case "none":
      return "";

    case "static":
      return "underline"; // Simple static underline

    case "effect":
      // Animated underline based on hover color
      switch (hoverColor) {
        case "white":
          return "hover-underline-animation-white";
        case "red":
          return "hover-underline-animation-red";
        case "none":
          return "";
        default:
          return "hover-underline-animation";
      }

    default:
      return "";
  }
};

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  title,
  hoverColor = "grey",
  underlineStyle = "effect",
}) => {
  const hoverClass = getHoverColorClass(hoverColor);
  const underlineClass = getUnderlineClass(underlineStyle, hoverColor);

  return (
    <nav
      className="flex mb-2 text-sm"
      aria-label="Breadcrumb"
      data-testid="breadcrumb-navigation"
    >
      <ol className="flex items-center gap-2 text-[13px] lg:text-[14px]">
        <li className="inline-flex items-center">
          <Link
            href="/"
            className={`${hoverClass} ${underlineClass}`}
            data-testid="breadcrumb-home-link"
          >
            Home
          </Link>
        </li>
        <li>·</li>
        <li>
          <span className="text-grey-700">{title}</span>
        </li>
      </ol>
    </nav>
  );
};

export default Breadcrumb;
