import {
  mockBigImageSectionItem,
  mockEWTNOriginalsItem,
  mockHighlightVideoItem,
  mockVideoItems,
} from "@/app/watch/mocks";
import WatchClient from "./WatchClient";
import { Metadata } from "next";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata("/watch", "Watch", "Watch Page");
}

export default async function WatchPage() {
  const sections = {
    items: mockVideoItems,
    EWTNitems: mockEWTNOriginalsItem,
    highlightedVideo: mockHighlightVideoItem,
    bigImageItem: mockBigImageSectionItem,
    videos: mockVideoItems,
  };

  const jsonLd = buildWebPageJsonLd("Watch", "/watch");
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <WatchClient videoSections={sections} />
    </>
  );
}
