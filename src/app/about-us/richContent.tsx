import React from "react";
import { PortableText, PortableTextReactComponents } from "@portabletext/react";
import { IMAGES_PATH } from "@/constants";
import Image from "next/image";

interface TimelineItem {
  _key: string;
  _type: string;
  title: string;
  year: number;
  highlighted: boolean;
  imageUrl?: string;
  image?: {
    _type: "image";
    asset: {
      _ref: string;
      _type: "reference";
    };
  };
}

interface TimelineBlock {
  _type: "timeline";
  _key: string;
  items: TimelineItem[];
}

interface BlockContent {
  _key: string;
  _type: string;
  style?: string;
  children: Array<{
    _key: string;
    _type: string;
    marks: string[];
    text: string;
  }>;
}

interface GrayArea {
  _type: "grayArea";
  imageUrl: string;
  description: BlockContent[];
}

type Content = BlockContent | GrayArea | TimelineItem | TimelineBlock;

interface RichContentProps {
  content: Content[];
  firstLetterBig?: boolean;
}

const Timeline = ({ items }: { items: TimelineItem[] }) => {
  if (!items || !Array.isArray(items)) return null;

  const validItems = items.filter((item) => item.title && item.year);

  return (
    <div className="flex flex-col mb-8 lg:mb-10">
      {validItems.map((item) => (
        <div
          key={item._key}
          className="
            border-l-2 border-black pl-2 relative group pt-3
            lg:border-0
          "
        >
          <div className="absolute z-20 -left-[7px] top-[27px] w-3 h-3 bg-red-700 rounded-full lg:left-1/2 lg:-ml-1.5 lg:top-0"></div>
          <div className="hidden group-first:block lg:hidden absolute z-10 -left-1 top-0 h-[27px] w-2 bg-white"></div>
          <div className="hidden lg:block absolute z-10 left-1/2 top-0 -bottom-8 w-3 bg-red-100 -ml-1.5 rounded-b-full"></div>

          <div className="relative lg:w-1/2 lg:group-odd:left-1/2">
            <div
              className="
                text-[32px] leading-none font-titles mb-3 font-light
                lg:w-full lg:mb-0 lg:pb-5 lg:text-[40px]
                lg:group-even:pr-3 lg:group-even:mr-4 lg:group-even:border-r lg:group-even:text-right lg:group-even:-ml-6
                lg:group-odd:pl-3 lg:group-odd:ml-4 lg:group-odd:border-l
              "
            >
              {item.year}
            </div>

            <div
              className={`
                bg-grey-100 rounded-xl overflow-hidden
                lg:group-even:rounded-r-none lg:group-even:mr-6 lg:group-even:border-r lg:group-even:border-red-700 lg:group-even:flex-row-reverse
                lg:group-odd:rounded-l-none lg:group-odd:ml-4 lg:group-odd:border-l lg:group-odd:border-red-700
                ${!item.highlighted ? "flex gap-2 p-2 lg:p-4 items-center" : ""}
              `}
            >
              {item.imageUrl && (
                <figure>
                  {item.highlighted ? (
                    <Image
                      className="block"
                      src={item.imageUrl}
                      alt={item.title}
                      layout="responsive"
                      width={16}
                      height={9}
                    />
                  ) : (
                    <Image
                      className="block"
                      src={item.imageUrl}
                      alt={item.title}
                      layout="responsive"
                      width={1}
                      height={1}
                    />
                  )}
                </figure>
              )}

              <div
                className={`
                  flex flex-col
                  ${!item.highlighted ? "justify-between lg:gap-2" : "p-4 gap-3"}
                `}
              >
                <figure>
                  <Image
                    src={`${IMAGES_PATH}/about-us/cna-logo.png`}
                    alt="CNA - Catholic News Agency"
                    width={83}
                    height={31}
                  />
                </figure>

                <h3 className="text-[18px] lg:text-[22px] leading-none font-titles font-semibold">
                  {item.title}
                </h3>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

const RichContent: React.FC<RichContentProps> = ({
  content,
  firstLetterBig = false,
}) => {
  const portableTextComponents: Partial<PortableTextReactComponents> = {
    block: {
      normal: ({ children }: { children?: React.ReactNode }) => (
        <p
          className={` text-[16px] md:text-[18px] mt-6 ${firstLetterBig ? "first-letter:text-5xl first-letter:font-bold first-letter:float-left first-letter:mr-2" : ""}`}
        >
          {children}
        </p>
      ),
      h1: ({ children }: { children?: React.ReactNode }) => <h1>{children}</h1>,
      h2: ({ children }: { children?: React.ReactNode }) => <h2>{children}</h2>,
      h3: ({ children }: { children?: React.ReactNode }) => <h3>{children}</h3>,
      h4: ({ children }: { children?: React.ReactNode }) => <h4>{children}</h4>,
      blockquote: ({ children }: { children?: React.ReactNode }) => (
        <blockquote>{children}</blockquote>
      ),
      dropCap: ({ children }: { children?: React.ReactNode }) => (
        <p
          className={`drop-cap text-[16px] md:text-[18px] ${firstLetterBig ? "first-letter:text-5xl first-letter:font-bold first-letter:float-left first-letter:mr-2" : ""}`}
        >
          {children}
        </p>
      ),
    },

    marks: {
      strong: ({ children }: { children: React.ReactNode }) => (
        <strong>{children}</strong>
      ),
      em: ({ children }: { children: React.ReactNode }) => <em>{children}</em>,
      underline: ({ children }: { children: React.ReactNode }) => (
        <span style={{ textDecoration: "underline" }}>{children}</span>
      ),
      code: ({ children }: { children: React.ReactNode }) => (
        <code>{children}</code>
      ),
      link: ({
        children,
        value,
      }: {
        children: React.ReactNode;
        value?: { href: string; target?: string };
      }) => (
        <a
          href={value?.href || "#"}
          target={value?.target || "_blank"}
          rel="noopener noreferrer"
          className="text-red-700 hover-underline-animation-red"
        >
          {children}
        </a>
      ),
    },

    types: {
      grayArea: ({ value }: { value: GrayArea }) => (
        <div className="mt-7 pb-1">
          <div className="bg-grey-100 p-4 rounded-2xl">
            {value.imageUrl && (
              <Image
                src={value.imageUrl}
                alt="Gray area image"
                className="max-w-full !w-auto !h-12 mb-4"
                layout="responsive"
                width={16}
                height={9}
              />
            )}
            <div className="mt-2 pt-2 border-t border-grey-200">
              {value.description && (
                <PortableText
                  value={value.description}
                  components={portableTextComponents}
                />
              )}
            </div>
          </div>
        </div>
      ),
      timeline: ({ value }: { value: TimelineBlock }) => (
        <Timeline items={value.items} />
      ),
      image: ({ value }: { value: { src: string; alt?: string } }) => (
        <Image
          src={value.src}
          alt={value.alt || "Inline Image"}
          className="inline-image max-w-full !h-auto !w-auto"
          layout="responsive"
          width={16}
          height={9}
        />
      ),
    },

    list: {
      bullet: ({ children }: { children?: React.ReactNode }) => (
        <ul>{children}</ul>
      ),
      number: ({ children }: { children?: React.ReactNode }) => (
        <ol>{children}</ol>
      ),
    },
    listItem: {
      bullet: ({ children }: { children?: React.ReactNode }) => (
        <li>{children}</li>
      ),
      number: ({ children }: { children?: React.ReactNode }) => (
        <li>{children}</li>
      ),
    },
  };

  return (
    <div className="rich-content mb-1" data-testid="rich-content">
      <PortableText value={content} components={portableTextComponents} />
    </div>
  );
};

export type {
  Content,
  BlockContent,
  GrayArea,
  TimelineItem,
  TimelineBlock,
  RichContentProps,
};
export default RichContent;
