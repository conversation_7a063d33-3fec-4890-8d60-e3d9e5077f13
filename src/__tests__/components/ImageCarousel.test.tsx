import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { render, screen, fireEvent } from "@testing-library/react";
import ImageCarousel from "../../components/ImageCarousel";
import { Media } from "@/sanity/queries/Trending/trending";

const mockSection: {
  _type: "thisWeekInPhotosSection";
  articles: {
    title: string;
    slug: string;
    media: Media[];
    category?: { title?: string; slug?: string };
    subcategory?: { slug?: string };
  }[];
} = {
  _type: "thisWeekInPhotosSection",
  articles: [
    {
      title: "Sunset Over Mountains",
      slug: "sunset-over-mountains",
      media: [
        {
          type: "image",
          resource_type: "image",
          url: "https://via.placeholder.com/800x600",
          text: "A breathtaking view of the sunset.",
        },
      ],
      category: { title: "Nature", slug: "nature" },
      subcategory: { slug: "landscape" },
    },
    {
      title: "City Lights at Night",
      slug: "city-lights-at-night",
      media: [
        {
          type: "image",
          resource_type: "image",
          url: "https://via.placeholder.com/800x600",
          text: "The city comes to life after dark.",
        },
      ],
      category: { title: "City", slug: "city" },
      subcategory: { slug: "urban" },
    },
  ],
};

describe("ImageCarousel Component", () => {
  test("renders the component correctly", () => {
    render(<ImageCarousel section={mockSection} />);

    expect(screen.getByText(mockSection.articles[0].title)).toBeInTheDocument();
    expect(
      screen.getByText(mockSection.articles[0].media[0].text || ""),
    ).toBeInTheDocument();
  });

  test("displays the first slide initially", () => {
    render(<ImageCarousel section={mockSection} />);

    expect(screen.getByText(mockSection.articles[0].title)).toBeInTheDocument();
  });

  test("navigates to the next slide on button click", () => {
    render(<ImageCarousel section={mockSection} />);

    const nextButton = screen.getByRole("button", { name: /arrow_forward/i });
    fireEvent.click(nextButton);

    expect(screen.getByText(mockSection.articles[1].title)).toBeInTheDocument();
  });

  test("navigates to the previous slide on button click", () => {
    render(<ImageCarousel section={mockSection} />);

    const nextButton = screen.getByRole("button", { name: /arrow_forward/i });
    fireEvent.click(nextButton);

    const prevButton = screen.getByRole("button", { name: /arrow_back/i });
    fireEvent.click(prevButton);

    expect(screen.getByText(mockSection.articles[0].title)).toBeInTheDocument();
  });
});
