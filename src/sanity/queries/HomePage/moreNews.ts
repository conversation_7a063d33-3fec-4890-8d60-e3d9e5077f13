import { defineQuery } from "next-sanity";
import { Subcategory } from "./categoryArticles";
import { workspace } from "@/sanity/env";

export const moreNewsQuery = defineQuery(`
  *[
    _type == "dailyStoryArticle" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    defined(category) &&
    category->_id != null
  ] {
    "id": _id,
    title,
    description,
    "slug": slug.current,
    "publishedDate": publishedDate,
    "created_at": _createdAt,
    "featuredMedia": featuredMedia[0],
    "author": authors[0] -> {
      "slug": slug.current,
      "name": fullName,
      "image": image {
        "url": secure_url
      }
    },
    "media": featuredMedia[] {
      "url": secure_url,
      "type": resource_type,
      "text": context.custom.caption
    },
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current,
      description
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current,
    },
  } | order(publishedDate desc) [0...4]
`);

export type Media = {
  url: string;
  type: string;
  text?: string;
};

export type Author = {
  slug: string;
  name: string;
  image?: {
    url: string;
  };
};

export type Category = {
  id: string;
  title: string;
  slug: string;
  description?: string;
};

export type Article = {
  id: string;
  slug?: string;
  title: string;
  description: string;
  author: Author;
  publishedDate: string;
  created_at: string;
  media?: Media[];
  category: Category;
  subcategory?: Subcategory;
};
