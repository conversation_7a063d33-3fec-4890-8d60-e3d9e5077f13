"use client";
import { useEffect, useState } from "react";
import MultiSelect from "@/components/ui/Form/MultiSelect";
import Pagination from "@/components/ui/Pagination";
import { IMAGES_PATH } from "@/constants";
import { SearchTag } from "@/sanity/queries/Tag/tag";
import { Article } from "@/app/api/articles/bookmarks/route";
import NewsCard from "@/components/Account/Interests/newsCard";
import Image from "next/image";
import Skeleton from "@/components/ui/Skeleton";

type Props = {
  tags: SearchTag[];
};

export default function Interest({ tags }: Props) {
  const [articles, setArticles] = useState<Article[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;

  const [loadingTags, setLoadingTags] = useState(true);
  const [loadingArticles, setLoadingArticles] = useState(true);

  const selectedOptions = tags.filter((tag) => selectedTagIds.includes(tag.id));

  const currentNews = articles.slice(indexOfFirstItem, indexOfLastItem);

  useEffect(() => {
    const fetchFollowedTags = async () => {
      try {
        const res = await fetch("/api/tags/followers");
        const json = await res.json();

        setSelectedTagIds(json.followedTagIds ?? []);
      } catch (error) {
        console.error("Error loading followed tags:", error);
      } finally {
        setLoadingTags(false);
      }
    };

    fetchFollowedTags();
  }, []);

  useEffect(() => {
    const fetchArticles = async () => {
      if (!selectedTagIds.length) {
        setArticles([]);
        setLoadingArticles(false);
        return;
      }

      setLoadingArticles(true);

      try {
        const res = await fetch("/api/articles/by-tags", {
          method: "POST",
          body: JSON.stringify({ tagIds: selectedTagIds }),
          headers: { "Content-Type": "application/json" },
        });

        const json = await res.json();
        setArticles(json.items || []);
      } catch (err) {
        console.error("Failed to load articles by tag:", err);
      } finally {
        setLoadingArticles(false);
      }
    };

    fetchArticles();
  }, [selectedTagIds]);

  const handleSelect = async (id: string) => {
    setSelectedTagIds((prev) => [...prev, id]);

    try {
      await fetch(`/api/tags/${id}/followers`, {
        method: "POST",
      });
    } catch (error) {
      console.error("Failed to follow tag:", id, error);
    }
  };

  const handleRemove = async (id: string) => {
    setSelectedTagIds((prev) => prev.filter((tagId) => tagId !== id));

    try {
      await fetch(`/api/tags/${id}/followers`, {
        method: "DELETE",
      });
    } catch (error) {
      console.error("Failed to unfollow tag:", id, error);
    }
  };

  return (
    <div>
      <div className="bg-grey-100 p-4 rounded-xl">
        <div className="mb-4 border-b border-grey-200 pb-4">
          <h2 className="text-2xl font-titles font-bold mb-2">Interests</h2>

          <p className="text-sm lg:text-[16px] text-gray-600 mb-4">
            Access your followed topics anytime, anywhere
          </p>
          <MultiSelect
            options={tags}
            selectedOptions={selectedOptions}
            onSelect={handleSelect}
            onRemove={handleRemove}
            placeholder="Add a new tag"
            onEmptyText="You've reached the maximum amount of tags. To follow a new one, please remove a tag first."
          />
        </div>

        <div className="bg-white rounded-xl p-4 mt-5">
          <h2 className="text-[20px] lg:text-2xl font-bold mb-2 font-titles">
            Recommended news according to your interests
          </h2>
          <p className="text-[16px] text-gray-600 mb-6">
            The latest updates based on your interests
          </p>
          {loadingTags || loadingArticles ? (
            Array.from({ length: 5 }, (_, i) => (
              <div
                className="flex gap-4 items-center group"
                data-testid="news-card-container"
                key={`interests-article-skeleton-${i}`}
              >
                <div className="flex gap-4 pb-4 w-full">
                  <div className="w-full">
                    <Skeleton
                      imagePosition="right"
                      imageHeight="5rem"
                      imageWidth="8rem"
                      titleWidth="100%"
                      titleHeight="1.5rem"
                    />
                  </div>
                </div>
              </div>
            ))
          ) : currentNews.length ? (
            <div className="space-y-4">
              {currentNews.map((item) => (
                <NewsCard key={item.id} item={item} />
              ))}
            </div>
          ) : (
            <div className="mt-8 max-w-96 mx-auto flex flex-col items-center">
              <Image
                src={`${IMAGES_PATH}/account/no-interests-selected.png`}
                alt="Follow tags to personalize your news feed. You can follow up to 20 tags and stay up to date on the topics that matter most to you."
                width={162}
                height={162}
                className="max-w-full"
              />

              <p className="text-sm text-center">
                Follow tags to personalize your news feed. You can follow up to
                20 tags and stay up to date on the topics that matter most to
                you.
              </p>
            </div>
          )}

          <Pagination
            totalItems={articles.length}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
    </div>
  );
}
