import { NextRequest } from "next/server";
import { auth0 } from "@/lib/auth0";
import {
  addFollower,
  getFollowerCount,
  getUserFollows,
  removeFollower,
} from "@/app/api/services/tags/followers";
import {
  unauthorizedResponse,
  successResponse,
  errorResponse,
  successResponseWithData,
} from "@/app/api/util/errors";
import { POST, GET, DELETE } from "./route";

// Mock dependencies
jest.mock("@/lib/auth0", () => ({
  auth0: {
    getSession: jest.fn(),
  },
}));

jest.mock("@/app/api/services/tags/followers", () => ({
  addFollower: jest.fn(),
  getFollowerCount: jest.fn(),
  getUserFollows: jest.fn(),
  removeFollower: jest.fn(),
}));

jest.mock("@/app/api/util/errors", () => ({
  unauthorizedResponse: jest.fn(() => ({
    status: 401,
    message: "Unauthorized",
  })),
  badRequestResponse: jest.fn((message: string) => ({ status: 400, message })),
  successResponse: jest.fn((message: string, status: number) => ({
    status,
    message,
  })),
  errorResponse: jest.fn((error) => ({
    status: 500,
    message: error.message,
  })),
  successResponseWithData: jest.fn((data, status: number) => ({
    status,
    data,
  })),
}));

describe("Follower API Routes", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("POST /api/tags/:tag_id/followers", () => {
    it("should return unauthorized if no user session", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce(null);

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(unauthorizedResponse).toHaveBeenCalled();
      expect(response).toEqual({ status: 401, message: "Unauthorized" });
    });

    it("should add a follower and return success response", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(addFollower).toHaveBeenCalledWith("user123", "tag123");
      expect(successResponse).toHaveBeenCalledWith("Author followed", 201);
      expect(response).toEqual({ status: 201, message: "Author followed" });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (addFollower as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to add follower"),
      );

      const response = await POST({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to add follower"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to add follower",
      });
    });
  });

  describe("GET /api/tags/:tag_id/followers", () => {
    it("should return follower count and user follower status", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      (getFollowerCount as jest.Mock).mockResolvedValueOnce({
        follower_count: 10,
      });

      (getUserFollows as jest.Mock).mockResolvedValueOnce(true);

      const response = await GET({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(getFollowerCount).toHaveBeenCalledWith("user123");
      expect(getUserFollows).toHaveBeenCalledWith("user123", "tag123");

      expect(successResponseWithData).toHaveBeenCalledWith(
        { followerCount: 10, userFollows: true },
        200,
      );

      expect(response).toEqual({
        status: 200,
        data: { followerCount: 10, userFollows: true },
      });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (getFollowerCount as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to get follower count"),
      );

      const response = await GET({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to get follower count"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to get follower count",
      });
    });
  });

  describe("DELETE /api/tags/:tag_id/followers", () => {
    it("should return unauthorized if no user session", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce(null);

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(unauthorizedResponse).toHaveBeenCalled();
      expect(response).toEqual({ status: 401, message: "Unauthorized" });
    });

    it("should remove a follower and return success response", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(removeFollower).toHaveBeenCalledWith("user123", "tag123");
      expect(successResponse).toHaveBeenCalledWith(
        "Author follower removed",
        200,
      );
      expect(response).toEqual({
        status: 200,
        message: "Author follower removed",
      });
    });

    it("should return error response on exception", async () => {
      (auth0.getSession as jest.Mock).mockResolvedValueOnce({
        user: { sub: "user123" },
      });
      (removeFollower as jest.Mock).mockRejectedValueOnce(
        new Error("Failed to remove follower"),
      );

      const response = await DELETE({} as NextRequest, {
        params: Promise.resolve({ tagId: "tag123" }),
      });

      expect(errorResponse).toHaveBeenCalledWith(
        new Error("Failed to remove follower"),
      );
      expect(response).toEqual({
        status: 500,
        message: "Failed to remove follower",
      });
    });
  });
});
