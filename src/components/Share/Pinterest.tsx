import { PinterestShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const PinterestButton = ({
  shareUrl,
  articleTitle,
  articleImageUrl,
}: {
  shareUrl: string;
  articleTitle: string;
  articleImageUrl: string;
}) => {
  return (
    <PinterestShareButton
      url={shareUrl}
      description={articleTitle}
      media={articleImageUrl}
    >
      <ShareIconButton
        icon={(hover) => (
          <Icon
            icon="pinterest"
            size={24}
            color={hover ? "#c52b2d" : "black"}
          />
        )}
      />
    </PinterestShareButton>
  );
};

export default PinterestButton;
