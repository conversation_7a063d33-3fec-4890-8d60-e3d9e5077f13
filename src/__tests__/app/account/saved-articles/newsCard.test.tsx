import { render, screen, fireEvent, within } from "@testing-library/react";
import NewsCard from "@/components/Account/SavedArticles/newsCard";
import "@testing-library/jest-dom";
import { Article } from "@/app/api/articles/bookmarks/route";
import { ImgHTMLAttributes } from "react";

export const mockArticle: Article = {
  id: "123",
  title: "Mock Article Title",
  slug: "mock-article-title",
  updated_at: "2025-04-01T00:00:00Z",
  media: {
    url: "",
    type: "image",
  },
  category: {
    id: "cat1",
    title: "World",
    slug: "world",
    description: "World news",
  },
  subcategory: {
    id: "sub1",
    title: "Europe",
    slug: "europe",
  },
  authors: [
    {
      _id: "author1",
      name: "<PERSON>",
      slug: "jane-doe",
    },
  ],
};

// 🔧 If you're using next/image in the component, mock it
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: ImgHTMLAttributes<HTMLImageElement>) => {
    const { src, alt, ...rest } = props;
    return <img src={src as string} alt={alt} {...rest} />;
  },
}));

describe("NewsCard Component", () => {
  const mockOnRemoveAuthor = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders responsive image correctly", () => {
    render(<NewsCard item={mockArticle} onRemoveAuthor={mockOnRemoveAuthor} />);

    const imageContainer = screen.getByTestId("news-image-container");
    expect(imageContainer).toHaveClass(
      "w-[128px]",
      "h-[85px]",
      "relative",
      "shrink-0",
    );

    const image = screen.getByAltText(mockArticle.title);
    expect(image).toHaveClass("rounded");
  });

  it("calls onRemoveAuthor when clicking remove button", () => {
    render(<NewsCard item={mockArticle} onRemoveAuthor={mockOnRemoveAuthor} />);

    const removeButton = within(
      screen.getByTestId("news-remove-button-container"),
    ).getByRole("button");

    fireEvent.click(removeButton);

    expect(mockOnRemoveAuthor).toHaveBeenCalledTimes(1);
  });

  it("has expected group class on container", () => {
    render(<NewsCard item={mockArticle} onRemoveAuthor={mockOnRemoveAuthor} />);

    expect(screen.getByTestId("news-card-container")).toHaveClass("group");
  });

  it("displays article title", () => {
    render(<NewsCard item={mockArticle} onRemoveAuthor={mockOnRemoveAuthor} />);

    expect(screen.getByText(mockArticle.title)).toBeInTheDocument();
  });
});
