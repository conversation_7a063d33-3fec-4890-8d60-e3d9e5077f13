"use client";

import { useEffect, useState } from "react";

const YoutubeEmbed = ({ embedUrl }: { embedUrl: string }) => {
  const [canRender, setCanRender] = useState(false);

  useEffect(() => {
    const handleConsent = () => {
      if (window._iub?.cs?.isConsentGiven?.()) {
        setCanRender(true);
      }
    };

    if (window._iub?.cs?.isConsentGiven?.()) {
      setCanRender(true);
    } else {
      window.addEventListener("iubenda_cs_consent_given", handleConsent);
    }

    return () => {
      window.removeEventListener("iubenda_cs_consent_given", handleConsent);
    };
  }, []);

  if (!canRender) {
    return;
  }

  const videoIdMatch = embedUrl.match(/[?&]v=(.+?)(?:$|[&?])/)?.[1];
  const shortsIdMatch = embedUrl.match(
    /https:\/\/(?:www\.)?youtube\.com\/shorts\/(.+?)(?:$|[&?])/,
  )?.[1];
  const shortLinkMatch = embedUrl.match(
    /https:\/\/youtu\.be\/(.+?)(?:$|[&?])/,
  )?.[1];
  const embedLinkMatch = embedUrl.match(
    /https:\/\/(?:www\.)youtube(-nocookie)?\.com\/embed\/(.+?)(?:$|[&?])/,
  )?.[2];
  const videoId =
    videoIdMatch ??
    shortsIdMatch ??
    shortLinkMatch ??
    embedLinkMatch ??
    "00000000";

  return (
    <div className="mt-4">
      <iframe
        src={`https://www.youtube.com/embed/${videoId}`}
        allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        className="aspect-video w-full"
        allowFullScreen
      />
    </div>
  );
};

export default YoutubeEmbed;
