import { getUserFollowing } from "@/app/api/services/authors/followers";
import { errorResponse, successResponseWithData } from "@/app/api/util/errors";
import { getAuth0UserId } from "@/app/api/util";
import { sanityFetch } from "@/sanity/client";
import { authorFollowersQuery } from "@/sanity/queries/Author/authorFollowers";

/**
 * Handles the GET request to retrieve follower information for a specific author.
 *
 * @param params.authorId - The ID of the author for which to retrieve follower information.
 * @returns A JSON response containing the follower count and whether the user follows the author.
 *
 */
export async function GET() {
  try {
    const auth0UserId = await getAuth0UserId();

    const [userData] = await Promise.all([
      auth0UserId ? getUserFollowing(auth0UserId) : null,
    ]);
    const authorIds = userData?.map((item) => item.author_id);
    const authors = await sanityFetch({
      query: authorFollowersQuery,
      params: { authorIds: authorIds },
      tags: [],
    });

    return successResponseWithData(
      {
        userFollows: authors,
      },
      200,
    );
  } catch (error) {
    return errorResponse(error);
  }
}
