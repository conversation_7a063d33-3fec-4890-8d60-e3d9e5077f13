import { render, screen } from "@testing-library/react";
import HighlightedNews from "@/components/HomePage/HighlightedNews";
import { SectionArticle } from "@/sanity/queries/HomePage/homePage";

jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: {
    src: string;
    alt: string;
    className?: string;
    fill?: boolean;
  }) => <img {...props} fill={props.fill ? "true" : undefined} />,
}));

jest.mock("next/link", () => ({
  __esModule: true,
  default: ({
    children,
    href,
    className,
  }: {
    children: React.ReactNode;
    href: string;
    className?: string;
  }) => (
    <a href={href} className={className}>
      {children}
    </a>
  ),
}));

jest.mock("@/components/Icon", () => ({
  Icon: ({ icon, className }: { icon: string; className?: string }) => (
    <span data-testid={`icon-${icon}`} className={className} />
  ),
}));

jest.mock("@/components/ui/Button", () => ({
  __esModule: true,
  default: ({
    children,
    href,
    className,
    variant,
  }: {
    children: React.ReactNode;
    href: string;
    className?: string;
    variant?: string;
  }) => (
    <a href={href} className={className} data-variant={variant}>
      {children}
    </a>
  ),
}));

jest.mock("@/utils/utils", () => ({
  getMediaImage: jest.fn((media) => media?.url || ""),
  getFirstMediaImageUrl: jest.fn((media) => media?.url || ""),
  getArticleUrl: (
    categorySlug: string,
    articleSlug: string,
    subcategorySlug?: string,
  ) => {
    if (subcategorySlug) {
      return `/${categorySlug}/${subcategorySlug}/${articleSlug}`;
    }
    return `/${categorySlug}/${articleSlug}`;
  },
  truncateTitle: jest.fn((text: string) =>
    text && text.length > 100 ? `${text.substring(0)}...` : text || "",
  ),
  truncateDescription: jest.fn((text: string) =>
    text && text.length > 200 ? `${text.substring(0, 200)}...` : text || "",
  ),
  truncateText: jest.fn((text: string, maxLength: number) =>
    text && text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text || "",
  ),
}));

describe("HighlightedNews", () => {
  const mockSection = {
    _type: "highlightedArticleSection",
    status: true,
    title: "Abortion on the Ballot",
    description:
      "Founded on Oct. 2, 1928, this is an important story for our readers.",
    image: {
      url: "/images/test.jpg",
      type: "image",
      text: "Test image",
    },
    article: {
      id: "article-123",
      title: "Article Original Title",
      description: "Article original description",
      _updatedAt: "2023-01-01T00:00:00Z",
      slug: "article-slug",
      publishedDate: "2023-01-01T00:00:00Z",
      created_at: "2023-01-01T00:00:00Z",
      media: [
        {
          url: "/images/test.jpg",
          type: "image",
          text: "Test image",
        },
      ],
      authors: [],
      category: {
        id: "category-123",
        title: "US Election 2024",
        slug: "us-election-2024",
        description: "Coverage of the US Election",
      },
      subcategory: {
        id: "subcategory-123",
        title: "Analysis",
        slug: "analysis",
      },
      tags: [
        {
          id: "tag-123",
          title: "Other Topics",
          slug: "other-topics",
        },
      ],
    } as SectionArticle,
  } as const;

  it("renders with default colors", () => {
    render(<HighlightedNews section={mockSection} />);

    const container = screen.getByTestId("highlighted-news").children[1];
    expect(container).toHaveStyle({
      backgroundColor: "#000000",
      color: "#ffffff",
    });
  });

  it("renders with custom colors", () => {
    render(
      <HighlightedNews
        section={mockSection}
        backgroundColor="#FF0000"
        textColor="#00FF00"
      />,
    );

    const container = screen.getByTestId("highlighted-news").children[1];
    expect(container).toHaveStyle({
      backgroundColor: "#FF0000",
      color: "#00FF00",
    });
  });

  it("renders the category text and links to category page", () => {
    render(<HighlightedNews section={mockSection} />);

    const categoryLink = screen.getByText("US Election 2024");
    expect(categoryLink).toBeInTheDocument();
    expect(categoryLink.closest("a")).toHaveAttribute(
      "href",
      "/us-election-2024",
    );
  });

  it("renders the section title when provided", () => {
    render(<HighlightedNews section={mockSection} />);

    const title = screen.getByText("Abortion on the Ballot");
    expect(title).toBeInTheDocument();
    expect(title.closest("a")).toHaveAttribute(
      "href",
      "/us-election-2024/analysis/article-slug",
    );
  });

  it("renders the article title when section title is not provided", () => {
    const modifiedSection = {
      ...mockSection,
      title: null,
    };

    render(<HighlightedNews section={modifiedSection} />);

    const title = screen.getByText(/Article Original Title/);
    expect(title).toBeInTheDocument();
  });

  it("renders the section description when provided", () => {
    render(<HighlightedNews section={mockSection} />);

    expect(screen.getByText(/Founded on Oct. 2, 1928/)).toBeInTheDocument();
  });

  it("renders the article description when section description is not provided", () => {
    const modifiedSection = {
      ...mockSection,
      description: null,
    };

    render(<HighlightedNews section={modifiedSection} />);

    expect(
      screen.getByText(/Article original description/),
    ).toBeInTheDocument();
  });

  it('renders the "Go Deeper" button with correct link', () => {
    render(<HighlightedNews section={mockSection} />);

    const button = screen.getByRole("link", { name: "Go Deeper" });
    expect(button).toHaveAttribute(
      "href",
      "/us-election-2024/analysis/article-slug",
    );
    expect(button).toHaveClass("w-48");
    expect(button).toHaveAttribute("data-variant", "outlined");
  });

  it("renders the tags link when tags are present", () => {
    render(<HighlightedNews section={mockSection} />);

    const link = screen.getByRole("link", {
      name: /You can also explore Other Topics/i,
    });
    expect(link).toHaveAttribute("href", "/tags/other-topics");
    expect(screen.getByTestId("icon-arrow_forward")).toBeInTheDocument();
  });

  it("does not render tags link when article has no tags", () => {
    const sectionWithoutTags = {
      ...mockSection,
      article: {
        ...mockSection.article,
        tags: [],
      },
    };

    render(<HighlightedNews section={sectionWithoutTags} />);

    const linkText = screen.queryByText(/You can also explore Other Topics/i);
    expect(linkText).not.toBeInTheDocument();
  });
});
