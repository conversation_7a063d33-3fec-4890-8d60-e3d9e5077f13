import { sanityFetch } from "@/sanity/client";
import {
  staticTabbedPageQuery,
  StaticTabbedPageQueryResult,
} from "@/sanity/queries/static-tabbed-page";
import AboutUsClient from "@/app/about-us/AboutUsClient";
import { Metadata } from "next";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata(): Promise<Metadata> {
  const pageData = await fetchStaticTabbedPageData("about-us");

  return generatePageMetadata(
    "/about-us",
    pageData.title,
    pageData.description
      .map((block) => block.children.map((child) => child.text).join(""))
      .join("\n"),
    pageData.imageUrl,
  );
}

async function fetchStaticTabbedPageData(
  slug: string,
): Promise<StaticTabbedPageQueryResult> {
  const staticTabbedPage = await sanityFetch<StaticTabbedPageQueryResult>({
    query: staticTabbedPageQuery,
    params: { slug },
  });
  return staticTabbedPage;
}

export default async function AboutUsPage() {
  const pageData = await fetchStaticTabbedPageData("about-us");
  const jsonLd = buildWebPageJsonLd("About Us", "/about-us");
  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <AboutUsClient pageData={pageData} />
    </>
  );
}
