import { defineQuery } from "next-sanity";
import { workspace } from "@/sanity/env";

export const breakingNewsQuery = defineQuery(`
  *[
    _type == "breakingNews" &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    active == true
  ][0]{
    _type,
    _id,
    title,
    active,
    lastUpdate,
    media {
      "url": secure_url,
      "type": resource_type,
      format,
      public_id
    },
    relatedLiveBlogArticle->{
      _id,
      _updatedAt,
      title,
      slug {
        current
      },
      category->{
        slug {
          current
        }
      },
      subcategory->{
        slug {
          current
        }
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      },
    }
  }
`);

export type BreakingNewsResult = {
  _type: "breakingNews";
  _id: string;
  title: string;
  active: boolean;
  lastUpdate: string;
  media: {
    url: string;
    type: string;
    format: string;
    public_id: string;
  };
  relatedLiveBlogArticle: {
    _id: string;
    _updatedAt: string;
    title: string;
    slug: {
      current: string;
    };
    category: {
      slug: {
        current: string;
      };
    };
    subcategory: {
      slug: {
        current: string;
      };
    };
    media: {
      url: string;
      type: string;
      text: string;
    };
  };
} | null;
