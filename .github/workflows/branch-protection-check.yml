name: Branch Protection Check

on:
  pull_request:
    branches:
      - main
      - 'feature/**'
    types: [opened, reopened, synchronize]

jobs:
  branch-protection-check:
    runs-on: ubuntu-latest

    steps:
      - name: Branch Protection Status Check
        run: |
          echo "Branch protection check passed!"
          echo "This lightweight check ensures branch protection rules are satisfied."
          echo "Target branch: ${{ github.base_ref }}"
          echo "Source branch: ${{ github.head_ref }}"
          echo "Pull request: #${{ github.event.number }}"
