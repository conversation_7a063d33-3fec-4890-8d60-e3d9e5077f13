import { workspace } from "@/sanity/env";

export const trendingArticlesQuery = `
  *[
    _type in ["dailyStoryArticle", "liveBlogArticle"] &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    slug.current in $slugs &&
    (!defined($categorySlug) || category->slug.current == $categorySlug) &&
    (!defined($subcategorySlug) || subcategory->slug.current == $subcategorySlug)
  ] {
    "id": _id,
    "created_at": dateTime(_createdAt),
    "updated_at": dateTime(_updatedAt),
    "publishedDate": dateTime(publishedDate),
    "slug": slug.current,
    title,
    description,
    truncatedTitle,
    "excerpt": description,
    "media": featuredMedia[] {
      "url": secure_url,
      "type": resource_type,
      "text": context.custom.caption
    },
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current,
      description
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "tags": tags[]-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "authors": authors[]-> {
      "slug": slug.current,
      "name": fullName,
      image {
        "url": secure_url
      }
    },
  }
  `;

export const latestArticlesQuery = `
  *[
    _type in ["dailyStoryArticle", "liveBlogArticle"] &&
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    defined(publishedDate) &&
    !(slug.current in $slugs) &&
    (!defined($categorySlug) || category->slug.current == $categorySlug) &&
    (!defined($subcategorySlug) || subcategory->slug.current == $subcategorySlug)
  ] | order(dateTime(publishedDate) desc)[0...$limit] {
    "id": _id,
    "created_at": dateTime(_createdAt),
    "updated_at": dateTime(_updatedAt),
    "publishedDate": dateTime(publishedDate),
    "slug": slug.current,
    title,
    truncatedTitle,
    "excerpt": description,
    "media": featuredMedia[] {
      "url": secure_url,
      "type": resource_type,
      "text": context.custom.caption
    },
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current,
      description
    },
    "subcategory": subcategory-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "tags": tags[]-> {
      "id": _id,
      title,
      "slug": slug.current
    },
    "authors": authors[]-> {
      "slug": slug.current,
      "name": fullName,
      image {
        "url": secure_url
      }
    },
  }
  `;

export type ArticlesResponse = {
  trendingArticles: Article[];
  latestArticles: Article[];
};

export type Subcategory = {
  id: string;
  title: string;
  slug: string;
  articles: Article[];
  articlesCount: number;
};

export type Media = {
  url: string;
  resource_type: string;
  text?: string;
  type: string;
};

export type Author = {
  slug: string;
  name: string;
  image?: Media;
};

export type Article = {
  id: string;
  title: string;
  truncatedTitle: string;
  description: string;
  slug: string;
  created_at: string;
  updated_at: string;
  publishedDate: string;
  excerpt: string;
  category: {
    id: string;
    title: string;
    slug: string;
    description: string;
  };
  subcategory: {
    id: string;
    title: string;
    slug: string;
  };
  tags: {
    id: string;
    title: string;
    slug: string;
  }[];
  media: {
    url: string;
    type: string;
    text?: string;
  }[];
  authors: Author[];
};
