"use client";

import Link from "next/link";
import { Icon } from "../Icon";
import { truncateTitle } from "@/utils/utils";
import Timestamp from "../Timestamp";
import Image from "next/image";
import clsx from "clsx";
import { VideoItem } from "@/app/watch/mocks";

export const VideoCard = ({
  video,
  thumbnail,
  showDescription = false,
  showCaption = true,
  showTimestamp = true,
  mode = "column",
  truncateTitleAt = 80,
  truncateDescriptionAt = 80,
  captionClassName = "",
  titleClassName = "",
  descriptionClassName = "",
  mobileView = "vertical",
  isClip = false,
}: {
  video: VideoItem;
  thumbnail: string;
  showDescription?: boolean;
  showCaption?: boolean;
  showTimestamp?: boolean;
  mode?: "column" | "row";
  truncateTitleAt?: number;
  truncateDescriptionAt?: number;
  captionClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  mobileView?: "vertical" | "landscape";
  isClip?: boolean;
}) => {
  const isRow = mode === "row";

  return (
    <div
      className={clsx(
        "bg-grey-900 hover:opacity-90 rounded-md group",
        isRow ? "flex flex-row gap-4 items-start" : "block",
        mobileView == "landscape" && "flex gap-4 items-start md:block",
      )}
    >
      {/* Thumbnail */}
      <div
        className={clsx(
          "relative aspect-[16/9] rounded-2xl overflow-visible group box-border",
          isRow
            ? "w-1/2"
            : mobileView === "landscape"
              ? "w-[136px] md:w-full"
              : "w-full",
        )}
      >
        <div
          className="w-full h-full transition-all duration-300 ease-in-out rounded-2xl overflow-hidden bg-gray-800 hover:scale-105 hover:border-[2px] hover:border-white"
          style={{ transformOrigin: "center center" }}
        >
          <Link href={video.href}>
            <Image
              src={thumbnail}
              alt={video.title}
              fill
              className={clsx(
                "object-cover",
                mobileView ? "rounded-xl md:rounded-2xl" : "rounded-2xl",
              )}
            />
          </Link>
        </div>

        <div
          className={clsx(
            "absolute w-[35px] h-[35px] rounded bg-black/70 text-white flex items-center justify-center",
            mobileView
              ? "bottom-2 left-2 md:bottom-4 md:left-4"
              : "bottom-4 left-4",
          )}
        >
          <Icon icon="play" className="w-4 h-4" />
        </div>
      </div>

      {/* Text Content */}
      <div
        className={clsx(
          "flex-1",
          isRow && "w-1/2",
          mobileView && `p-0 ${!isRow ? "md:p-4" : ""}`,
        )}
      >
        {!mobileView && showCaption && video.caption && (
          <p
            className={clsx(
              "text-xs text-[14px] mb-1 text-white",
              captionClassName,
            )}
          >
            {video.caption}
          </p>
        )}

        <Link
          href={video.href}
          className={clsx(
            "font-semibold text-base mb-1 leading-snug !text-[18px] hover-underline-animation-white text-white",
            titleClassName,
            mobileView ? "md:pt-1" : "pt-1",
          )}
        >
          {truncateTitle(video.title, truncateTitleAt)}
        </Link>

        {showDescription && video.description && (
          <p
            className={clsx(
              "text-sm line-clamp-2 text-[14px] font-normal pt-1 text-white",
              descriptionClassName,
            )}
          >
            {truncateTitle(video.description, truncateDescriptionAt)}
          </p>
        )}

        {showTimestamp && video.timestamp && (
          <div className="flex justify-between items-center text-[12px] text-grey-500 mt-2">
            <Timestamp date={video.timestamp} />
            {isClip && <Icon icon="clip" className="-mr-4" />}
          </div>
        )}
      </div>
    </div>
  );
};
