import { TwitterShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const TwitterButton = ({ shareUrl }: { shareUrl: string }) => {
  return (
    <TwitterShareButton
      url={shareUrl}
      title="Check this out"
      aria-label="Share on Twitter"
    >
      <ShareIconButton icon={() => <Icon icon="x" size={24} />} />
    </TwitterShareButton>
  );
};

export default TwitterButton;
