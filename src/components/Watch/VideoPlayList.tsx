"use client";
import React, { useEffect, useRef, useState } from "react";
import { Icon } from "../Icon";
import Button from "../ui/Button";
import Timestamp from "../Timestamp";
import { truncateTitle } from "@/utils/utils";
import VideoPlayer from "../ui/VideoPlayer";
import { FaPause, FaPlay } from "react-icons/fa";
import { useRouter } from "next/navigation";
import Skeleton from "../ui/Skeleton";
import { VideoItem } from "@/app/watch/mocks";
import Image from "next/image";

type Props = {
  videos: VideoItem[];
  homeMode?: boolean;
};

const VideoPlaylist: React.FC<Props> = ({ videos, homeMode = true }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showOverlay, setShowOverlay] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [autoplayCancelled, setAutoplayCancelled] = useState(false);
  const [autoplayEnabled, setAutoplayEnabled] = useState(true);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const nextIndexRef = useRef<number | null>(null);

  const [showAutoplayBlockedOverlay, setShowAutoplayBlockedOverlay] =
    useState(false);
  const router = useRouter();

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleEnded = () => {
      if (!autoplayEnabled) {
        setShowAutoplayBlockedOverlay(true);
        return;
      }

      const nextIndex = currentIndex + 1;
      if (nextIndex < videos.length) {
        nextIndexRef.current = nextIndex;
        setShowOverlay(true);
        setCountdown(5);
        setAutoplayCancelled(false);
      }
    };

    const handlePause = () => {
      if (showOverlay && countdownRef.current) {
        clearInterval(countdownRef.current);
        countdownRef.current = null;
        setAutoplayCancelled(true);
      }
    };

    video.addEventListener("ended", handleEnded);
    video.addEventListener("pause", handlePause);

    return () => {
      video.removeEventListener("ended", handleEnded);
      video.removeEventListener("pause", handlePause);
    };
  }, [currentIndex, showOverlay, videos.length, autoplayEnabled]);

  useEffect(() => {
    if (!showOverlay || autoplayCancelled) return;

    countdownRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 1) {
          clearInterval(countdownRef.current!);
          countdownRef.current = null;
          setShowOverlay(false);

          if (nextIndexRef.current !== null) {
            const nextVideo = videos[nextIndexRef.current];
            if (homeMode) {
              setCurrentIndex(nextIndexRef.current);
            } else {
              router.push(nextVideo.href);
            }
            nextIndexRef.current = null;
          }
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (countdownRef.current) clearInterval(countdownRef.current);
    };
  }, [showOverlay, autoplayCancelled, autoplayEnabled]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current
        .play()
        .catch((err) => console.error("Error playing video:", err));
    }
  }, [currentIndex]);

  const clearCountdown = () => {
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
  };

  const handleCancel = () => {
    clearCountdown();
    setAutoplayCancelled(true);
    setShowOverlay(false);
  };

  const handlePlayNow = () => {
    clearCountdown();
    if (nextIndexRef.current !== null) {
      if (homeMode) {
        setCurrentIndex(nextIndexRef.current);
      } else {
        router.push(videos[nextIndexRef.current].href);
      }
      nextIndexRef.current = null;
      setShowOverlay(false);
    }
  };

  const toggleAutoplay = () => {
    setAutoplayEnabled((prev) => !prev);
  };

  const visibleVideos = homeMode
    ? videos.slice(0, 6)
    : videos.filter((_, i) => i !== currentIndex).slice(0, 6);

  return (
    <div className="flex flex-col md:flex-row gap-4 pt-4 text-white">
      {/* Left/main section */}
      <div className="flex-1">
        <div className="relative w-full">
          <VideoPlayer
            src={videos[currentIndex].url}
            ref={videoRef}
            playing={true}
            controls
            pauseOnClickOutside={false}
            className="rounded"
          />

          {/* Overlay for autoplay blocked */}
          {!autoplayEnabled && showAutoplayBlockedOverlay && (
            <div className="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center text-white rounded-lg p-4 z-10">
              <button
                onClick={() => {
                  videoRef.current?.play();
                  setShowAutoplayBlockedOverlay(false);
                }}
                className="flex flex-col items-center justify-center"
              >
                <Icon
                  icon="refresh"
                  className="text-white mb-2 !text-[100px]"
                />
              </button>
            </div>
          )}
          {/* Overlay for autoplay next video */}
          {autoplayEnabled && showOverlay && nextIndexRef.current !== null && (
            <div className="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center text-white rounded-lg p-4">
              {/* Mobile layout */}
              <div className="flex lg:hidden flex-row items-center w-full max-w-md space-x-4">
                <div className="relative w-28 h-16 flex-shrink-0">
                  <Image
                    src={videos[nextIndexRef.current]!.imageUrl}
                    alt={videos[nextIndexRef.current].title}
                    fill
                    className="w-full h-full object-cover rounded"
                  />
                  <div className="absolute bottom-4 left-4 w-[35px] h-[35px] rounded bg-black/70 text-white flex items-center justify-center">
                    <Icon icon="play" className="w-4 h-4" />
                  </div>
                </div>
                <div className="flex flex-col justify-between space-y-1 flex-1">
                  <p className="text-[13px] text-grey-300">
                    Up next in{" "}
                    <span className="font-bold text-white">{countdown}</span>
                  </p>
                  <p className="text-[14px] font-semibold text-white leading-snug line-clamp-2">
                    {videos[nextIndexRef.current].title}
                  </p>
                  <div className="flex gap-2 pt-1">
                    <Button
                      onClick={handleCancel}
                      variant="outlined"
                      className="text-xs px-2 py-1 w-28"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handlePlayNow}
                      variant="primaryHover"
                      className="text-xs px-2 w-28"
                    >
                      Play Now
                    </Button>
                  </div>
                </div>
              </div>

              {/* Desktop layout */}
              <div className="hidden lg:flex flex-col items-center space-y-4">
                <div className="w-full text-left">
                  <p className="text-[16px] text-grey-500 px-4">
                    Up next in{" "}
                    <span className="font-bold text-white">{countdown}</span>
                  </p>
                </div>
                <div className="relative w-72 h-40">
                  <Image
                    src={videos[nextIndexRef.current]!.imageUrl}
                    alt={videos[nextIndexRef.current].title}
                    fill
                    className="w-full h-full object-cover rounded"
                  />
                  <div className="absolute bottom-4 left-4 w-[35px] h-[35px] rounded bg-black/70 text-white flex items-center justify-center">
                    <Icon icon="play" className="w-6 h-6" />
                  </div>
                </div>
                <p className="text-[16px] font-semibold max-w-xs text-white px-4">
                  {truncateTitle(videos[nextIndexRef.current].title, 45)}
                </p>
                <div className="flex gap-4">
                  <Button
                    onClick={handleCancel}
                    variant="outlined"
                    className="w-32"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handlePlayNow}
                    variant="primaryHover"
                    className="w-32"
                  >
                    Play Now
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Info section */}
        <div className="mt-4 space-y-1 text-sm text-white">
          <p className="text-[13px] uppercase">
            {videos[currentIndex].caption}
          </p>
          <p className="text-[24px] font-semibold leading-[44px] pt-3 pb-3">
            {videos[currentIndex].title}
          </p>
          <p className="text-[16px] text-grey-500 leading-[24px]">
            {videos[currentIndex].description}
          </p>
          <p className="text-[12px] text-grey-500 pt-2">
            <Timestamp date={videos[currentIndex].timestamp} />
          </p>
        </div>
      </div>

      {/* Right panel */}
      <div className="w-full md:w-80 space-y-2 mt-6 md:mt-0">
        <div className="flex items-center justify-between mb-8">
          <span className="text-white font-semibold text-[16px]">
            {homeMode ? "TRENDING VIDEOS" : "RELATED VIDEOS"}
          </span>
          {!homeMode && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-white">Autoplay</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={autoplayEnabled}
                  onChange={toggleAutoplay}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-grey-400 rounded-full peer peer-checked:bg-green-500 transition-colors duration-300 h-[15px]" />
                <div
                  className={`absolute w-5 h-5 bg-white rounded-full flex items-center justify-center transition-transform duration-300 transform peer-checked:translate-x-5 ${autoplayEnabled ? "left-1" : ""}`}
                >
                  {autoplayEnabled ? (
                    <FaPlay className="w-3 h-3 text-grey-400" />
                  ) : (
                    <FaPause className="w-3 h-3 text-grey-400" />
                  )}
                </div>
              </label>
            </div>
          )}
        </div>

        {visibleVideos.map((video, i) => {
          const actualIndex = homeMode
            ? i
            : videos.findIndex((v) => v === video);
          const isCurrent = actualIndex === currentIndex;

          return (
            <div
              key={i}
              onClick={() => {
                if (!video) return;
                if (homeMode) {
                  setCurrentIndex(actualIndex);
                  nextIndexRef.current = null;
                  setShowOverlay(false);
                  clearCountdown();
                  setAutoplayCancelled(true);
                } else {
                  router.push(video.href);
                }
              }}
              className={`cursor-pointer flex items-start gap-3 p-2 rounded transition-all border border-black ${
                isCurrent ? "border-white" : "hover:border-white"
              }`}
            >
              <div className="relative w-[140px] h-[80px] bg-gray-700 rounded-md overflow-hidden">
                {video?.url ? (
                  <Image
                    src={videos[actualIndex]!.imageUrl}
                    alt={video.title}
                    fill
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Skeleton
                    imageHeight="80px"
                    imageWidth="140px"
                    overrideImageWidth
                    showTitle={false}
                    showDescription={false}
                    showImage={true}
                  />
                )}

                {isCurrent && homeMode && (
                  <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center rounded-md">
                    <span className="text-white text-xs font-semibold">
                      NOW PLAYING
                    </span>
                  </div>
                )}

                {!isCurrent && (
                  <div className="absolute bottom-1 left-1 w-[35px] h-[35px] rounded bg-black/70 text-white flex items-center justify-center">
                    <Icon icon="play" className="w-4 h-4" />
                  </div>
                )}
              </div>

              <div className="flex-1">
                <p className="font-[16px]">{truncateTitle(video.title, 45)}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default VideoPlaylist;
