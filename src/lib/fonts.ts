import { <PERSON>o } from "next/font/google";
import localFont from "next/font/local";

export const roboto = Roboto({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  variable: "--font-roboto",
  display: "swap",
});

export const ivyPresto = localFont({
  variable: "--font-ivyPresto",
  display: "swap",
  preload: true,
  src: [
    {
      path: "../fonts/fonnts.com-Ivy-Presto-Headline.otf",
      weight: "400",
    },
    {
      path: "../fonts/fonnts.com-Ivy-Presto-Headline-Light.otf",
      weight: "300",
    },
    {
      path: "../fonts/fonnts.com-Ivy-Presto-Headline-Semi-Bold.otf",
      weight: "600",
    },
    {
      path: "../fonts/fonnts.com-Ivy-Presto-Headline-Thin.otf",
      weight: "200",
    },
  ],
});
