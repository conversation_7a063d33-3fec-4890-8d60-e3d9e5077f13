import SearchClient from "./SearchClient";
import { sanityFetch } from "@/sanity/client";
import {
  searchQueryAsc,
  searchQueryDesc,
  SearchResponse,
} from "@/sanity/queries/search";
import { Metadata } from "next";
import { fetchTrendingArticlesFromAnalytics } from "../api/trending";
import { buildWebPageJsonLd, generatePageMetadata } from "@/utils/metadata";
import JsonLD from "@/components/JsonLd";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata(
    "/search",
    "Search Results",
    "Search Results Page",
  );
}

interface SearchPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

const SearchPage = async ({ searchParams }: SearchPageProps) => {
  const params = await searchParams;
  const query = typeof params?.q === "string" ? params.q.trim() : "";
  const sortParam = params?.sort === "oldest" ? "asc" : "desc";
  const rawStart =
    typeof params?.startDate === "string" ? params.startDate : null;
  const rawEnd = typeof params?.endDate === "string" ? params.endDate : null;

  const startDate = rawStart ? `${rawStart}T00:00:00Z` : null;
  const endDate = rawEnd ? `${rawEnd}T23:59:59Z` : null;
  const generalTrendingArticles = await fetchTrendingArticlesFromAnalytics({
    listSize: 5,
    categorySlug: null,
    subcategorySlug: null,
    periodStart: undefined,
    periodEnd: undefined,
  });

  const jsonLd = buildWebPageJsonLd("Search Results", "/search");

  if (!query) {
    return (
      <>
        <JsonLD jsonLd={jsonLd} />
        <SearchClient
          latestNews={{ articles: [], articlesCount: 0 }}
          generalTrendingArticles={generalTrendingArticles}
        />
      </>
    );
  }

  const searchQueryToUse =
    sortParam === "asc" ? searchQueryAsc : searchQueryDesc;
  const queryParams: Record<string, string> = {
    searchQuery: query,
  };

  if (startDate) queryParams.startDate = startDate;
  if (endDate) queryParams.endDate = endDate;
  const searchData = await sanityFetch<SearchResponse>({
    query: searchQueryToUse,
    params: {
      searchQuery: query,
      startDate: startDate || null,
      endDate: endDate || null,
    },
  });

  return (
    <>
      <JsonLD jsonLd={jsonLd} />
      <SearchClient
        latestNews={searchData}
        generalTrendingArticles={generalTrendingArticles}
      />
    </>
  );
};

export default SearchPage;
