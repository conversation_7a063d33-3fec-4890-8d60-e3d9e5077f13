import { IconName } from "../Icon";

export const langOptions = [
  { value: "en", label: "English" },
  { value: "es", label: "Español" },
  { value: "de", label: "Deutsch" },
  { value: "pt", label: "Português" },
  { value: "it", label: "Italiano" },
  { value: "fr", label: "Français" },
  { value: "ar", label: "عربي" },
];

export const additionalLinks = [
  {
    name: "Editor Service",
    href:
      process.env.NEXT_PUBLIC_EDITOR_SERVICE_URL ||
      "https://editors.catholicnewsagency.com/login",
    icon: "edit" as IconName,
  },
];
