import Drawer, { DrawerProps } from "@/components/ui/Drawer";
import { render, screen, fireEvent } from "@testing-library/react";

describe("Drawer Component", () => {
  const defaultDrawerProps: DrawerProps = {
    open: true,
    onClose: jest.fn(),
    side: "left",
    children: <div>Drawer Content</div>,
  };

  test("renders the drawer component", () => {
    render(<Drawer {...defaultDrawerProps} />);
    const drawerComponent = screen.getByTestId("drawer");
    expect(drawerComponent).toBeInTheDocument();
  });

  test("triggers the onClose callback function", () => {
    render(<Drawer {...defaultDrawerProps} />);
    const drawerComponent = screen.getByTestId("drawer");
    expect(drawerComponent).toBeInTheDocument();

    fireEvent.click(drawerComponent);
    expect(defaultDrawerProps.onClose).toHaveBeenCalledTimes(1);
  });
});
