import { apiVersion, dataset, projectId } from "./env";
import { createClient } from "next-sanity";
import { defineLive } from "next-sanity";

const token = process.env.SANITY_AUTH_TOKEN;
if (!token) {
  throw new Error("Missing SANITY_AUTH_TOKEN");
}

export const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: true,
  token: process.env.SANITY_AUTH_TOKEN,
  perspective: "published",
  stega: {
    studioUrl: process.env.SANITY_STUDIO_URL,
  },
});

export const { sanityFetch, SanityLive } = defineLive({
  client,
  serverToken: token,
  browserToken: token,
  fetchOptions: {
    revalidate: 0,
  },
});
