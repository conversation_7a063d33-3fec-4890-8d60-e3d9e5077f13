import { EmailShareButton } from "react-share";
import ShareIconButton from "./ShareIconButton";
import { Icon } from "../Icon";

const MailButton = ({ shareUrl }: { shareUrl: string }) => {
  return (
    <EmailShareButton
      url={shareUrl}
      subject="I'd like to share a link with you"
      aria-label="Share by Mail"
    >
      <ShareIconButton
        icon={(hover) => (
          <Icon
            icon="shareEmail"
            size={24}
            color={hover ? "#c52b2d" : "black"}
          />
        )}
      />
    </EmailShareButton>
  );
};

export default MailButton;
