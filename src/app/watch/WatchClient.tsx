"use client";
import {
  BigImageSectionItem,
  EWTNOriginalsItem,
  getRandom6Items,
  HighlightVideoItem,
  VideoItem,
} from "@/app/watch/mocks";
import VideoCategorySection from "@/components/Watch/HomePage/VideoCategorySection";
import Link from "next/link";
import EWTNOriginalsSection from "@/components/Watch/HomePage/EWTNOriginalsSection";
import HighlightedVideoSection from "@/components/Watch/HomePage/HighlightedVideoSection";
import BigImageSection from "@/components/Watch/HomePage/BigImageSection";
import VideoPlaylist from "@/components/Watch/VideoPlayList";

type WatchClientProps = {
  videoSections: {
    items: VideoItem[];
    EWTNitems: EWTNOriginalsItem[];
    highlightedVideo: HighlightVideoItem;
    bigImageItem: BigImageSectionItem;
    videos: VideoItem[];
  };
};

export default function WatchClient({ videoSections }: WatchClientProps) {
  return (
    <div className="w-full bg-black">
      <div className="container mx-auto pt-8 px-4 h-auto">
        <header>
          <nav className="mt-2">
            <ol className="flex items-center gap-2 text-[13px] lg:text-[14px] text-grey-300 font-normal">
              <li className="underline hover:text-red-700 text-white transition-all">
                <Link href="/">Home</Link>
              </li>
              <li>·</li>
              <li>Watch</li>
            </ol>
          </nav>
        </header>

        <h1 className="font-titles text-white font-semibold text-[32px] xl:text-[52px] leading-10 xl:leading-[56px] pt-2">
          Watch
        </h1>

        <div className="xl:grid xl:grid-cols-12">
          <div className="xl:col-span-8">
            <p className="mt-2 sm:mt-4 lg:mt-4 text-[18px] text-grey-300 ">
              The Best Videos, Handpicked for You
            </p>
          </div>
        </div>
        <>
          <div className="pt-6 pb-6">
            <VideoPlaylist
              videos={videoSections.videos}
              homeMode={true}
            ></VideoPlaylist>
          </div>

          <VideoCategorySection
            title={"LATEST"}
            videos={getRandom6Items(videoSections.items)}
          />

          <VideoCategorySection
            title={"US"}
            videos={getRandom6Items(videoSections.items)}
          />

          <EWTNOriginalsSection ewtnOriginalItems={videoSections.EWTNitems} />

          <VideoCategorySection
            title={"EUROPE"}
            videos={getRandom6Items(videoSections.items)}
          />

          <HighlightedVideoSection
            highlightVideo={videoSections.highlightedVideo}
          />

          <VideoCategorySection
            title={"CONCLAVE 2025"}
            videos={getRandom6Items(videoSections.items)}
          />

          <BigImageSection bigImageItem={videoSections.bigImageItem} />

          <VideoCategorySection
            title={"TRAVEL"}
            videos={getRandom6Items(videoSections.items)}
          />
        </>
      </div>
    </div>
  );
}
