import { Content } from "@/components/ui/RichContent";
import { defineQuery } from "next-sanity";
import { RelatedArticle } from "./dailyStory";
import { workspace } from "@/sanity/env";
import { AuthorSocialMedia } from "./Author/author";

const createLiveBlogQuery = (direction: "asc" | "desc") =>
  defineQuery(`
  *[
    _type == "liveBlogArticle" && 
    agency == '${workspace}' &&
    !(_id in path("drafts.**")) &&
    slug.current == $slug 
  ][0] {
    "id": _id,
    title,
    description,
    summary,
    "slug": slug.current,
    publishedDate,
    _updatedAt,
    "updatedAt": _updatedAt,
    isBreakingNews,
    "media": featuredMedia[] {
      "url": secure_url,
      "type": resource_type,
      "text": context.custom.caption
    },
    "authors": authors[]-> {
      "id": _id,
      "slug": slug.current,
      "name": fullName,
      shortBio,
      "jobTitle": jobTitle->name,  
      "location": location->name, 
      email,
      image {
        "url": secure_url
      },
      "socialMedia": socialMedia[] {
        icon,
        link,
        nickname
      },
    },
    "category": category-> {
      "id": _id,
      title,
      "slug": slug.current,
      description
    },
    "liveUpdatesCount": count(liveUpdates[
      (
        $searchQuery == "" ||
        title match "*"+$searchQuery+"*" ||
        pt::text(content) match "*"+$searchQuery+"*" ||
        author->fullName match "*"+$searchQuery+"*"
      ) && (
        $dateQuery == "" || (
          dateTime(timestamp) >= dateTime($dateQuery+"T00:00:00Z") && 
          dateTime(timestamp) < dateTime($dateQuery+"T23:59:59Z")
        )
      )
    ]),
    "liveUpdates": liveUpdates[
      (
        $searchQuery == "" ||
        title match "*"+$searchQuery+"*" ||
        pt::text(content) match "*"+$searchQuery+"*" ||
        author->fullName match "*"+$searchQuery+"*"
      ) && (
        $dateQuery == "" || (
          dateTime(timestamp) >= dateTime($dateQuery+"T00:00:00Z") && 
          dateTime(timestamp) < dateTime($dateQuery+"T23:59:59Z")
        )
      )
    ] | order(timestamp ${direction}) [($pageQuery - 1) * $itemsPerPageQuery...$pageQuery * $itemsPerPageQuery] {
      _key,
      title,
      timestamp,
      "slug": slug.current,
      content,
      "featuredMedia": featuredMedia {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption,
      },
      "authors": authors[]-> {
        "id": _id,
        "slug": slug.current,
        "name": fullName,
        shortBio,
        "jobTitle": jobTitle->name,  
        "location": location->name, 
        email,
        image {
          "url": secure_url
        },
        "socialMedia": socialMedia[] {
          icon,
          link,
          nickname
        },
      },
      "relatedArticle": relatedArticle-> {
        _id,
        title,
        "slug": slug.current
      }
    },
    "relatedArticles": relatedArticles[0...3]-> {
      "id": _id,
      title,
      "slug": slug.current,
      "category": category-> {
        "id": _id,
        title,
        "slug": slug.current,
        description
      },
      "subcategory": subcategory-> {
        "id": _id,
        title,
        "slug": slug.current
      },
      "media": featuredMedia[0] {
        "url": secure_url,
        "type": resource_type,
        "text": context.custom.caption
      }
    }
  }
`);

export const liveBlogArticleQueryDesc = createLiveBlogQuery("desc");
export const liveBlogArticleQueryAsc = createLiveBlogQuery("asc");

export type Media = {
  url: string;
  resource_type: string;
  text?: string;
  type: string;
};

type Author = {
  id: string;
  slug: string;
  name: string;
  image?: Media;
  shortBio?: string;
  location?: string;
  jobTitle?: string;
  email: string;
  socialMedia: AuthorSocialMedia[];
};

type Category = {
  id: string;
  title: string;
  slug: string;
  description: string;
};

type LiveUpdate = {
  _key: string;
  title: string;
  timestamp: string;
  slug: string;
  content: Content[];
  featuredMedia?: FeaturedMedia;
  authors: Author[];
  relatedArticle: {
    _id: string | null;
    title: string;
    slug: string;
  } | null;
};

type FeaturedMedia = {
  url: string;
  type: string;
  resource_type: string;
  text: string;
};

export type SummaryItem = {
  text: string;
  _key: string;
};

type LiveBlogArticle = {
  id: string;
  title: string;
  description: string;
  slug: string;
  publishedDate: string;
  updatedAt: string;
  media: Media[];
  authors: Author[];
  category: Category;
  liveUpdates: LiveUpdate[];
  isBreakingNews: boolean;
  relatedArticles: RelatedArticle[];
  liveUpdatesCount: number;
  summary: SummaryItem[];
  totalLiveUpdatesWithMedia: number;
  allLiveUpdateMedia: Media[];
};

export type { LiveUpdate, LiveBlogArticle, Author };
