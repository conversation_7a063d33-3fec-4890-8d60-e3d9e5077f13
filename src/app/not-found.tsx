import Link from "next/link";
import { sanityFetch } from "@/sanity/client";
import {
  navigationMenuQuery,
  NavigationMenuQueryResult,
} from "@/sanity/queries/layout";
import SearchInput from "@/components/ui/Form/SearchInput";
import { Metadata } from "next";
import { generatePageMetadata } from "@/utils/metadata";
import Image from "next/image";
import { Key } from "react";

export async function generateMetadata(): Promise<Metadata> {
  return generatePageMetadata("/not-found", "Not Found", "Not Found Page");
}

async function fetchSections() {
  try {
    const result = await sanityFetch<NavigationMenuQueryResult>({
      query: navigationMenuQuery,
    });

    if (!result || !result.sections) {
      return [];
    }

    return result.sections || [];
  } catch (error) {
    console.error("Error fetching navigation menu:", error); // Added for now to bypass Vercel error until it's implemented
    return [];
  }
}

export default async function NotFound() {
  const sections = await fetchSections();

  return (
    <div className="flex flex-col items-center justify-center text-center px-4 py-12">
      <div className="w-[200px] h-[160px] mb-6 sm:w-[307px] sm:h-[247px] relative">
        <Image src="/images/404/404.png" alt="Page not found" fill />
      </div>

      <h2 className="font-semibold font-titles text-[24px] sm:text-[32px] mb-2">
        <b>Something is wrong, this page is not available</b>
      </h2>
      <p className="py-3">
        The link you followed may be broken or the page may have been removed.
      </p>
      <div className="mb-4">
        <div className="space-x-4 space-y-2">
          <div key="title" className="font-bold inline-block">
            Today:
          </div>

          {sections?.length > 0 ? (
            sections
              .slice(0, 6)
              .map(
                (section: {
                  slug: { current: Key | null | undefined };
                  title: string;
                }) => (
                  <div className="inline-block" key={section.slug.current}>
                    <Link
                      href={`/${section.slug.current}`}
                      className="hover-underline-animation"
                    >
                      {section.title}
                    </Link>
                  </div>
                ),
              )
          ) : (
            <p>No sections available</p>
          )}
        </div>
      </div>
      <div className="w-full max-w-xs pt-6">
        <SearchInput />
      </div>
    </div>
  );
}
