import Link from "next/link";

type AboutProps = {
  authors: { name: string; slug: string }[];
  color?: "black" | "white";
};

export default function AuthorsByName({
  authors,
  color = "black",
}: AboutProps) {
  return (
    <>
      By{" "}
      {authors.map((author, index) => (
        <span key={`${author.slug}-${index}`}>
          <Link
            className={`font-bold ${color === "black" ? "hover-underline-animation" : "hover-underline-animation-white"}`}
            href={`/author/${author.slug}`}
          >
            {author.name}
          </Link>
          {index < authors.length - 1 && ", "}
        </span>
      ))}
    </>
  );
}
