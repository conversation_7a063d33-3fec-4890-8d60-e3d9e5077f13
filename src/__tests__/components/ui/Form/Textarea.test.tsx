import { render, screen, fireEvent } from "@testing-library/react";
import Textarea from "@/components/ui/Form/Textarea";

describe("Textarea Component", () => {
  const defaultProps = {
    label: "Test Label",
    value: "",
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders label correctly when provided", () => {
    render(<Textarea {...defaultProps} />);
    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  it("does not render label when not provided", () => {
    render(<Textarea {...defaultProps} label="" />);
    expect(screen.queryByText("Test Label")).not.toBeInTheDocument();
  });

  it("handles value changes correctly", () => {
    render(<Textarea {...defaultProps} />);
    const textarea = screen.getByRole("textarea");
    fireEvent.change(textarea, { target: { value: "New text" } });
    expect(defaultProps.onChange).toHaveBeenCalledWith("New text");
  });

  it("shows error state with string message", () => {
    const errorMessage = "This field is required";
    render(<Textarea {...defaultProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole("textarea")).toHaveClass(
      "border-red-700",
      "bg-red-100",
    );
  });

  it("shows error state with boolean error", () => {
    render(<Textarea {...defaultProps} error={true} />);
    expect(screen.getByRole("textarea")).toHaveClass(
      "border-red-700",
      "bg-red-100",
    );
  });

  it("applies disabled styles when disabled", () => {
    render(<Textarea {...defaultProps} disabled />);
    const textarea = screen.getByRole("textarea");
    expect(textarea).toBeDisabled();
    expect(textarea).toHaveClass(
      "disabled:cursor-not-allowed",
      "disabled:opacity-70",
    );
  });

  it("displays initial value correctly", () => {
    const initialValue = "Initial text";
    render(<Textarea {...defaultProps} value={initialValue} />);
    expect(screen.getByRole("textarea")).toHaveValue(initialValue);
  });

  it("handles null value correctly", () => {
    render(<Textarea {...defaultProps} value={null} />);
    expect(screen.getByRole("textarea")).toHaveValue("");
  });

  it("applies custom className correctly", () => {
    const customClass = "custom-class";
    render(<Textarea {...defaultProps} className={customClass} />);
    expect(screen.getByRole("textarea")).toHaveClass(customClass);
  });

  it("maintains proper spacing with label", () => {
    render(<Textarea {...defaultProps} />);
    const container = screen.getByRole("textarea").parentElement;
    expect(container).toHaveClass("mt-1", "lg:mt-2");
  });

  it("sets correct id and htmlFor attributes", () => {
    render(<Textarea {...defaultProps} />);
    const textarea = screen.getByRole("textarea");
    const label = screen.getByText("Test Label");
    expect(textarea.id).toBeTruthy();
    expect(label).toHaveAttribute("for", textarea.id);
  });
});
