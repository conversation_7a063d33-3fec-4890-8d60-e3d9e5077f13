import { NextRequest } from "next/server";
import { getUserBookmarks } from "@/app/api/services/articles/bookmarks";
import {
  errorResponse,
  successResponseWithData,
  unauthorizedResponse,
} from "@/app/api/util/errors";
import { getAuth0UserId } from "@/app/api/util";
import { sanityFetch } from "@/sanity/client";
import { paginatedSavedArticlesQuery } from "@/sanity/queries/SavedArticles/paginatedSavedArticles";

/**
 * Handles the GET request to retrieve all the bookmarks information for a specific user.
 *
 * @param params.articleId - The ID of the article for which to retrieve bookmark information.
 * @returns A JSON response containing the bookmark count and whether the user has bookmarked the article.
 *
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const auth0UserId = await getAuth0UserId();
    if (!auth0UserId) {
      return unauthorizedResponse();
    }
    const { data: bookmarks, totalCount } = await getUserBookmarks(
      auth0UserId,
      start,
      end,
    );
    const articleIds = bookmarks.map((b) => b.article_id);
    if (articleIds.length === 0) {
      return successResponseWithData({ items: [], totalCount: 0 }, 200);
    }

    const articles = await sanityFetch<SavedArticlesResponse>({
      query: paginatedSavedArticlesQuery,
      params: { ids: articleIds },
      tags: [],
    });

    const orderedArticles = articleIds
      .map((id) =>
        articles.articles.find((article: { id: string }) => article.id === id),
      )
      .filter(Boolean);

    return Response.json(
      {
        data: {
          items: orderedArticles,
          totalCount: totalCount,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    return errorResponse(error);
  }
}

export type Article = {
  id: string;
  updated_at: string;
  title: string;
  slug: string;
  media: {
    url: string;
    type: string;
  };
  subcategory?: {
    id: string;
    title: string;
    slug: string;
  } | null;
  category: {
    id: string;
    title: string;
    slug: string;
    description: string;
  };
  authors: {
    _id: string;
    name: string;
    slug: string;
  }[];
};

export type SavedArticlesResponse = {
  articlesCount: number;
  articles: Article[];
};
