"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { useRef } from "react";
import type { Swiper as SwiperType } from "swiper";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import Link from "next/link";
import clsx from "clsx";
import { EWTNOriginalsItem } from "@/app/watch/mocks";

type EWTNOriginalsSectionProps = {
  ewtnOriginalItems: EWTNOriginalsItem[];
  sectionTitle?: string;
};

export default function EWTNOriginalsSection({
  ewtnOriginalItems,
  sectionTitle = "EWTN ORIGINALS",
}: EWTNOriginalsSectionProps) {
  const swiperRef = useRef<SwiperType | null>(null);

  const gap = "24px"; // tailwind gap-6 = 1.5rem = 24px

  // Calculate how many dummy placeholders are needed
  const missingItems = Math.max(0, 4 - ewtnOriginalItems.length);

  return (
    <div className="border-t border-grey-400 pt-4 pb-8">
      <h2 className="text-white text-xl font-semibold mb-4">{sectionTitle}</h2>

      {/* MOBILE (xs-sm) */}
      <div className="block md:hidden">
        <Swiper
          modules={[Navigation]}
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          breakpoints={{
            0: { slidesPerView: 1.3 },
            768: { slidesPerView: 3.6 },
          }}
        >
          {ewtnOriginalItems.map((item) => (
            <SwiperSlide key={item.title}>
              <div className="flex flex-col gap-2">
                <Link
                  href={`/watch/show/${item.slug}`}
                  className="relative w-[245px] h-[384px] rounded-2xl overflow-hidden cursor-pointer"
                >
                  <Image
                    src={item.imageUrl}
                    alt={item.title}
                    fill
                    className="object-cover"
                  />
                </Link>
                <p className="text-white text-sm">{item.title}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* TABLET / MID SCREENS (md-lg) */}
      <div className="hidden md:grid xl:hidden grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 justify-items-center">
        {ewtnOriginalItems.map((item) => (
          <div
            key={item.title}
            className="flex flex-col gap-2 group w-[200px] md:w-[220px] lg:w-[245px]"
          >
            <Link
              href={`/watch/show/${item.slug}`}
              className="relative w-full h-[300px] md:h-[340px] lg:h-[384px] rounded-2xl overflow-hidden box-border cursor-pointer"
            >
              <Image
                src={item.imageUrl}
                alt={item.title}
                fill
                className="object-cover rounded-2xl"
              />
            </Link>
            <p className="text-white text-base md:text-lg text-center">
              {item.title}
            </p>
          </div>
        ))}
      </div>

      {/* DESKTOP (xl+) */}
      <div className="hidden xl:flex justify-between flex-wrap">
        {ewtnOriginalItems.map((item, index) => (
          <div
            key={item.title}
            className={clsx(
              "flex flex-col gap-2 group w-[245px]",
              index === 0
                ? "ml-0"
                : index === ewtnOriginalItems.length - 1
                  ? "mr-0"
                  : `mx-[${gap}]`,
            )}
          >
            <Link
              href={`/watch/show/${item.slug}`}
              className="relative w-full h-[384px] rounded-2xl overflow-visible box-border cursor-pointer"
            >
              <div
                className="w-full h-full transition-all duration-300 ease-in-out rounded-2xl group-hover:scale-105 group-hover:border-[3px] group-hover:border-white"
                style={{ transformOrigin: "center center" }}
              >
                <Image
                  src={item.imageUrl}
                  alt={item.title}
                  fill
                  className="object-cover rounded-2xl"
                />
              </div>
            </Link>
            <p className="text-white text-lg">{item.title}</p>
          </div>
        ))}

        {/* Invisible placeholders to fill flex layout */}
        {Array.from({ length: missingItems }).map((_, idx) => (
          <div
            key={`placeholder-${idx}`}
            className="w-[245px] h-[384px] invisible"
          ></div>
        ))}
      </div>
    </div>
  );
}
